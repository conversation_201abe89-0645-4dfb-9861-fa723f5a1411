import { axiosInstance } from '@/lib/axios';

export interface StoreItem {
  id: string;
  name: string;
  description: string;
  coinPrice: number;
  quantity: number;
  category: string;
  image: string | null;
  status: 'ACTIVE' | 'INACTIVE';
  createdAt: string;
  updatedAt: string;
}

export interface StoreFilters {
  category?: string;
  status?: string;
  search?: string;
}

export interface StoreStats {
  totalItems: number;
  activeItems: number;
  inactiveItems: number;
  outOfStockItems: number;
  categoriesCount: number;
  categories: Array<{
    category: string;
    count: number;
  }>;
}

export const getAllStoreItems = async (filters?: StoreFilters) => {
  try {
    const params = new URLSearchParams();
    if (filters?.category) params.append('category', filters.category);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.search) params.append('search', filters.search);

    params.append('status', 'ACTIVE');

    const response = await axiosInstance.get(`/admin/store?${params.toString()}`);
    return {
      success: true,
      data: response.data.data
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store items'
    };
  }
};

export const getStoreItemById = async (id: string) => {
  try {
    const response = await axiosInstance.get(`/admin/store/${id}`);
    return {
      success: true,
      data: response.data.data
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store item'
    };
  }
};

export const getStoreStats = async () => {
  try {
    const response = await axiosInstance.get('/admin/store/stats');
    return {
      success: true,
      data: response.data.data
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store statistics'
    };
  }
};
