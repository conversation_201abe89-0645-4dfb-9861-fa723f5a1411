"use client";

import { useEffect, useState, useCallback } from "react";
import { useParams } from "next/navigation";
import { ColumnDef } from "@tanstack/react-table";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTable } from "@/app-components/dataTable";
import Pagination from "@/app-components/pagination";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Filter,
  Download,
} from "lucide-react";
import { toast } from "sonner";
import { downloadExamRankingsExcel, ExamRankingFilters, getExamRankings } from "@/services/uwhiz-result";


type Ranking = {
  rank: number;
  firstName: string;
  lastName: string;
  email: string;
  score: number;
  contact:string;
  attempts: number;
  totalQuestions: number;
};

interface PaginationData {
  totalPages?: number;
  totalItems?: number;
  totalEntries?: number;
}

export default function ExamRankingPage() {
  const { examId } = useParams();
  const [data, setData] = useState<Ranking[]>([]);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalEntries, setTotalEntries] = useState(0);
  const [loading, setLoading] = useState(true);
  const [downloadLoading, setDownloadLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  const [filters, setFilters] = useState<ExamRankingFilters>({
    firstName: '',
    lastName: '',
    email: '',
    score: '',
    rank: '',
    contact: ''
  });

  const [appliedFilters, setAppliedFilters] = useState<ExamRankingFilters>({});

  const fetchRankings = useCallback(async (
    page: number = 1,
    limit: number = 10,
    filtersToApply: ExamRankingFilters = {}
  ) => {
    if (!examId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await getExamRankings(examId as string, page, limit, filtersToApply);

      console.log('API Response:', response);

      if (!response.success && response.error) {
        throw new Error(response.error);
      }

      // Handle different possible response structures
      let rankings: Ranking[] = [];
      let pagination: PaginationData = {};

      if (response.success) {
        rankings = response.data?.rankings || response.data || [];
        pagination = response.data?.pagination || response.pagination || {};
      } else if (response.rankings) {
        rankings = response.rankings || [];
        pagination = {
          totalPages: response.totalPages || 1,
          totalItems: response.totalItems || 0,
        };
      } else {
        rankings = Array.isArray(response) ? response : [];
        pagination = {};
      }

      console.log('Processed rankings:', rankings);
      console.log('Processed pagination:', pagination);

      setData(rankings);

      const totalItems = pagination.totalItems || pagination.totalEntries || rankings.length;
      const calculatedTotalPages = pagination.totalPages || Math.ceil(totalItems / limit) || 1;

      setTotalPages(calculatedTotalPages);
      setTotalEntries(totalItems);
      setError(null);
    } catch (err: any) {
      console.error("API Error:", err);
      const errorMessage = err.message || "Unknown error occurred";
      setError("Error fetching rankings: " + errorMessage);
      toast.error("Failed to fetch exam rankings: " + errorMessage);
      setData([]);
      setTotalPages(1);
      setTotalEntries(0);
    } finally {
      setLoading(false);
    }
  }, [examId]);

  useEffect(() => {
    if (examId) {
      fetchRankings(page, limit, appliedFilters);
    }
  }, [examId, page, limit, fetchRankings, appliedFilters]);

  const handleSearch = () => {
    console.log('Applying filters:', filters);
    setPage(1);
    setAppliedFilters({ ...filters });
  };

  const handleResetFilters = async () => {
    console.log('Resetting filters');
    const resetFilters: ExamRankingFilters = {
      firstName: '',
      lastName: '',
      email: '',
      score: '',
      rank: '',
      contact: '',
    };
    setFilters(resetFilters);
    setAppliedFilters({});
    setPage(1);
  };

  const handleDownloadExcel = async () => {
    if (!examId) return;

    setDownloadLoading(true);
    try {
      await downloadExamRankingsExcel(examId as string, appliedFilters);
      toast.success("Excel file downloaded successfully!");
    } catch (error: any) {
      console.error("Download error:", error);
      toast.error(error.message || "Failed to download Excel file");
    } finally {
      setDownloadLoading(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (value: string) => {
    setLimit(Number(value));
    setPage(1);
  };

  const handleFilterChange = (field: keyof ExamRankingFilters, value: string) => {
    setFilters((prev: any) => ({
      ...prev,
      [field]: value
    }));
  };

  const columns: ColumnDef<Ranking>[] = [
    {
      accessorKey: "rank",
      header: "Rank",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("rank")}</div>
      ),
    },
    {
      accessorKey: "firstName",
      header: "First Name",
      cell: ({ row }) => row.getValue("firstName") || "N/A",
    },
    {
      accessorKey: "lastName",
      header: "Last Name",
      cell: ({ row }) => row.getValue("lastName") || "N/A",
    },
    {
      accessorKey: "email",
      header: "Student Email",
      cell: ({ row }) => row.getValue("email") || "N/A",
    },
     {
      accessorKey: "contact",
      header: "Student Contact",
      cell: ({ row }) => row.getValue("contact") || "N/A",
    },
    {
      accessorKey: "score",
      header: "Correct Answers",
    },
    {
      accessorKey: "attempts",
      header: "Attempts",
    },
    {
      accessorKey: "totalQuestions",
      header: "Total Questions",
    },
    {
      header: "Accuracy",
      cell: ({ row }) => {
        const { score, totalQuestions } = row.original;
        const accuracy = totalQuestions
          ? ((score / totalQuestions) * 100).toFixed(1)
          : "0.0";
        return `${accuracy}%`;
      },
    },
  ];

  const hasActiveFilters = Object.values(filters).some(filter => filter && filter.toString().trim() !== '');

  if (!examId) return <div className="p-6">Invalid exam ID</div>;

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">Exam Rankings</h1>
      <div className="flex justify-end gap-4 mb-4">
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className={`flex items-center gap-2 ${hasActiveFilters ? 'border-primary text-primary' : ''}`}
          aria-label="Toggle filters"
        >
          <Filter className="w-4 h-4" />
          Filters
        </Button>
         <Button
          onClick={handleDownloadExcel}
          disabled={downloadLoading || loading}
          className="flex items-center gap-2 bg-[#ff914d] text-white hover:bg-[#e8823d] disabled:opacity-50"
          aria-label="Download Excel file"
        >
          <Download className="h-4 w-4" />
          {downloadLoading ? "Downloading..." : "Download xlsx"}
        </Button>
      </div>
      {showFilters && (
        <Card>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  placeholder="Filter by first name"
                  value={filters.firstName || ''}
                  onChange={(e) => handleFilterChange('firstName', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  placeholder="Filter by last name"
                  value={filters.lastName || ''}
                  onChange={(e) => handleFilterChange('lastName', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  placeholder="Filter by email"
                  value={filters.email || ''}
                  onChange={(e) => handleFilterChange('email', e.target.value)}
                />
              </div>
               <div>
                <Label htmlFor="email">Contact</Label>
                <Input
                  id="contact"
                  placeholder="Filter by contact"
                  value={filters.contact || ''}
                  onChange={(e) => handleFilterChange('contact', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="score">Correct Answers</Label>
                <Input
                  id="score"
                  type="number"
                  placeholder="Filter Correct Answers"
                  value={filters.score || ''}
                  onChange={(e) => handleFilterChange('score', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="rank">Rank</Label>
                <Input
                  id="rank"
                  type="number"
                  placeholder="Filter by rank"
                  value={filters.rank || ''}
                  onChange={(e) => handleFilterChange('rank', e.target.value)}
                />
              </div>
            </div>

            <div className="flex gap-2 mt-4">
              <Button
                onClick={handleSearch}
                disabled={loading || !examId}
              >
                Search
              </Button>
              <Button
                variant="outline"
                onClick={handleResetFilters}
                disabled={loading || !examId}
              >
                Reset
              </Button>
            </div>
          </CardContent>
        </Card>
      )}


      {/* Data Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>
              Exam Rankings ({totalEntries} total)
              {hasActiveFilters && (
                <span className="text-sm font-normal text-gray-500 ml-2">
                  - Filtered Results
                </span>
              )}
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          ) : error ? (
            <div className="text-red-600 text-center py-8">{error}</div>
          ) : (
            <>
              <DataTable
                columns={columns}
                data={data}
                isLoading={loading}
              />

              <div className="flex justify-between items-center mt-4">
                <div className="text-sm text-gray-600">
                  {data.length} of {totalEntries} records
                  {limit && totalEntries > limit && ` (${limit} per page)`}
                </div>

                <div className="flex items-center gap-2">
                  <Select
                    value={limit.toString()}
                    onValueChange={handleLimitChange}
                  >
                    <SelectTrigger
                      className="w-[100px]"
                      aria-label="Select number of records per page"
                    >
                      <SelectValue placeholder="Select limit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                      <SelectItem value="200">200</SelectItem>
                      <SelectItem value="500">500</SelectItem>
                    </SelectContent>
                  </Select>

                  <Pagination
                    page={page}
                    totalPages={totalPages}
                    setPage={handlePageChange}
                    entriesText=""
                  />
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}