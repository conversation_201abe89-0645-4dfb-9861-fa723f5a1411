<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'voku\\' => array($vendorDir . '/voku/portable-ascii/src/voku'),
    'ZipStream\\' => array($vendorDir . '/maennchen/zipstream-php/src'),
    'Years\\' => array($baseDir . '/modules/Years/app'),
    'YearsSeeders\\' => array($baseDir . '/modules/Years/database/seeders'),
    'Yajra\\DataTables\\' => array($vendorDir . '/yajra/laravel-datatables-oracle/src'),
    'Whoops\\' => array($vendorDir . '/filp/whoops/src/Whoops'),
    'Webmozart\\Assert\\' => array($vendorDir . '/webmozart/assert/src'),
    'Timeslots\\' => array($baseDir . '/modules/Timeslots/app'),
    'TimeslotsSeeders\\' => array($baseDir . '/modules/Timeslots/database/seeders'),
    'TijsVerkoyen\\CssToInlineStyles\\' => array($vendorDir . '/tijsverkoyen/css-to-inline-styles/src'),
    'Thoughts\\' => array($baseDir . '/modules/Thoughts/app'),
    'ThoughtsSeeders\\' => array($baseDir . '/modules/Thoughts/database/seeders'),
    'Tests\\' => array($baseDir . '/tests'),
    'Testimonial\\' => array($baseDir . '/modules/Testimonial/app'),
    'TestimonialSeeders\\' => array($baseDir . '/modules/Testimonial/database/seeders'),
    'Termwind\\' => array($vendorDir . '/nunomaduro/termwind/src'),
    'Symfony\\Polyfill\\Uuid\\' => array($vendorDir . '/symfony/polyfill-uuid'),
    'Symfony\\Polyfill\\Php83\\' => array($vendorDir . '/symfony/polyfill-php83'),
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\Idn\\' => array($vendorDir . '/symfony/polyfill-intl-idn'),
    'Symfony\\Polyfill\\Intl\\Grapheme\\' => array($vendorDir . '/symfony/polyfill-intl-grapheme'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Contracts\\Translation\\' => array($vendorDir . '/symfony/translation-contracts'),
    'Symfony\\Contracts\\Service\\' => array($vendorDir . '/symfony/service-contracts'),
    'Symfony\\Contracts\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher-contracts'),
    'Symfony\\Component\\Yaml\\' => array($vendorDir . '/symfony/yaml'),
    'Symfony\\Component\\VarDumper\\' => array($vendorDir . '/symfony/var-dumper'),
    'Symfony\\Component\\Uid\\' => array($vendorDir . '/symfony/uid'),
    'Symfony\\Component\\Translation\\' => array($vendorDir . '/symfony/translation'),
    'Symfony\\Component\\String\\' => array($vendorDir . '/symfony/string'),
    'Symfony\\Component\\Routing\\' => array($vendorDir . '/symfony/routing'),
    'Symfony\\Component\\Process\\' => array($vendorDir . '/symfony/process'),
    'Symfony\\Component\\Mime\\' => array($vendorDir . '/symfony/mime'),
    'Symfony\\Component\\Mailer\\' => array($vendorDir . '/symfony/mailer'),
    'Symfony\\Component\\HttpKernel\\' => array($vendorDir . '/symfony/http-kernel'),
    'Symfony\\Component\\HttpFoundation\\' => array($vendorDir . '/symfony/http-foundation'),
    'Symfony\\Component\\Finder\\' => array($vendorDir . '/symfony/finder'),
    'Symfony\\Component\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher'),
    'Symfony\\Component\\ErrorHandler\\' => array($vendorDir . '/symfony/error-handler'),
    'Symfony\\Component\\CssSelector\\' => array($vendorDir . '/symfony/css-selector'),
    'Symfony\\Component\\Console\\' => array($vendorDir . '/symfony/console'),
    'Svg\\' => array($vendorDir . '/phenx/php-svg-lib/src/Svg'),
    'Subject\\' => array($baseDir . '/modules/Subject/app'),
    'SubjectSeeders\\' => array($baseDir . '/modules/Subject/database/seeders'),
    'StudentLeave\\' => array($baseDir . '/modules/StudentLeave/app'),
    'StudentLeaveSeeders\\' => array($baseDir . '/modules/StudentLeave/database/seeders'),
    'StudentAttendance\\' => array($baseDir . '/modules/StudentAttendance/app'),
    'StudentAttendanceSeeders\\' => array($baseDir . '/modules/StudentAttendance/database/seeders'),
    'Spatie\\Permission\\' => array($vendorDir . '/spatie/laravel-permission/src'),
    'Spatie\\LaravelIgnition\\' => array($vendorDir . '/spatie/laravel-ignition/src'),
    'Spatie\\Ignition\\' => array($vendorDir . '/spatie/ignition/src'),
    'Spatie\\FlareClient\\' => array($vendorDir . '/spatie/flare-client-php/src'),
    'Spatie\\Backtrace\\' => array($vendorDir . '/spatie/backtrace/src'),
    'SimpleSoftwareIO\\QrCode\\' => array($vendorDir . '/simplesoftwareio/simple-qrcode/src'),
    'Sabberworm\\CSS\\' => array($vendorDir . '/sabberworm/php-css-parser/src'),
    'Resources\\' => array($baseDir . '/modules/Resources/app'),
    'ResourcesSeeders\\' => array($baseDir . '/modules/Resources/database/seeders'),
    'Ramsey\\Uuid\\' => array($vendorDir . '/ramsey/uuid/src'),
    'Ramsey\\Collection\\' => array($vendorDir . '/ramsey/collection/src'),
    'Psy\\' => array($vendorDir . '/psy/psysh/src'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'Psr\\EventDispatcher\\' => array($vendorDir . '/psr/event-dispatcher/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Psr\\Clock\\' => array($vendorDir . '/psr/clock/src'),
    'ProfileViews\\' => array($baseDir . '/modules/ProfileViews/app'),
    'ProfileViewsSeeders\\' => array($baseDir . '/modules/ProfileViews/database/seeders'),
    'Proengsoft\\JsValidation\\' => array($vendorDir . '/proengsoft/laravel-jsvalidation/src'),
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'PhpOption\\' => array($vendorDir . '/phpoption/phpoption/src/PhpOption'),
    'PhpOffice\\PhpSpreadsheet\\' => array($vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet'),
    'Passbook\\' => array($baseDir . '/modules/Passbook/app'),
    'PassbookSeeders\\' => array($baseDir . '/modules/Passbook/database/seeders'),
    'NunoMaduro\\Collision\\' => array($vendorDir . '/nunomaduro/collision/src'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'Module\\' => array($baseDir . '/modules'),
    'Mockery\\' => array($vendorDir . '/mockery/mockery/library/Mockery'),
    'Matrix\\' => array($vendorDir . '/markbaker/matrix/classes/src'),
    'Masterminds\\' => array($vendorDir . '/masterminds/html5/src'),
    'Maatwebsite\\Excel\\' => array($vendorDir . '/maatwebsite/excel/src'),
    'League\\MimeTypeDetection\\' => array($vendorDir . '/league/mime-type-detection/src'),
    'League\\Flysystem\\Local\\' => array($vendorDir . '/league/flysystem-local'),
    'League\\Flysystem\\' => array($vendorDir . '/league/flysystem/src'),
    'League\\Config\\' => array($vendorDir . '/league/config/src'),
    'League\\CommonMark\\' => array($vendorDir . '/league/commonmark/src'),
    'Laravel\\Ui\\' => array($vendorDir . '/laravel/ui/src'),
    'Laravel\\Tinker\\' => array($vendorDir . '/laravel/tinker/src'),
    'Laravel\\SerializableClosure\\' => array($vendorDir . '/laravel/serializable-closure/src'),
    'Laravel\\Sanctum\\' => array($vendorDir . '/laravel/sanctum/src'),
    'Laravel\\Sail\\' => array($vendorDir . '/laravel/sail/src'),
    'LaravelPWA\\' => array($vendorDir . '/silviolleite/laravelpwa'),
    'Illuminate\\Support\\' => array($vendorDir . '/laravel/framework/src/Illuminate/Macroable', $vendorDir . '/laravel/framework/src/Illuminate/Collections', $vendorDir . '/laravel/framework/src/Illuminate/Conditionable'),
    'Illuminate\\Foundation\\Auth\\' => array($vendorDir . '/laravel/ui/auth-backend'),
    'Illuminate\\' => array($vendorDir . '/laravel/framework/src/Illuminate'),
    'HomeWork\\' => array($baseDir . '/modules/HomeWork/app'),
    'HomeWorkSeeders\\' => array($baseDir . '/modules/HomeWork/database/seeders'),
    'Holiday\\' => array($baseDir . '/modules/Holiday/app'),
    'HolidaySeeders\\' => array($baseDir . '/modules/Holiday/database/seeders'),
    'GuzzleHttp\\UriTemplate\\' => array($vendorDir . '/guzzlehttp/uri-template/src'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'GrahamCampbell\\ResultType\\' => array($vendorDir . '/graham-campbell/result-type/src'),
    'Fruitcake\\Cors\\' => array($vendorDir . '/fruitcake/php-cors/src'),
    'FontLib\\' => array($vendorDir . '/phenx/php-font-lib/src/FontLib'),
    'Firebase\\JWT\\' => array($vendorDir . '/firebase/php-jwt/src'),
    'Fees\\' => array($baseDir . '/modules/Fees/app'),
    'FeesSeeders\\' => array($baseDir . '/modules/Fees/database/seeders'),
    'Faker\\' => array($vendorDir . '/fakerphp/faker/src/Faker'),
    'Event\\' => array($baseDir . '/modules/Event/app'),
    'EventSeeders\\' => array($baseDir . '/modules/Event/database/seeders'),
    'Enquiry\\' => array($baseDir . '/modules/Enquiry/app'),
    'EnquirySeeders\\' => array($baseDir . '/modules/Enquiry/database/seeders'),
    'Egulias\\EmailValidator\\' => array($vendorDir . '/egulias/email-validator/src'),
    'Dotenv\\' => array($vendorDir . '/vlucas/phpdotenv/src'),
    'Dompdf\\' => array($vendorDir . '/dompdf/dompdf/src'),
    'DocumentsSeeders\\' => array($baseDir . '/modules/Document/database/seeders'),
    'Document\\' => array($baseDir . '/modules/Document/app'),
    'DocumentCategory\\' => array($baseDir . '/modules/DocumentCategory/app'),
    'DocumentCategorySeeders\\' => array($baseDir . '/modules/DocumentCategory/database/seeders'),
    'Doctrine\\Instantiator\\' => array($vendorDir . '/doctrine/instantiator/src/Doctrine/Instantiator'),
    'Doctrine\\Inflector\\' => array($vendorDir . '/doctrine/inflector/lib/Doctrine/Inflector'),
    'Doctrine\\Common\\Lexer\\' => array($vendorDir . '/doctrine/lexer/src'),
    'Dflydev\\DotAccessData\\' => array($vendorDir . '/dflydev/dot-access-data/src'),
    'Department\\' => array($baseDir . '/modules/Department/app'),
    'DepartmentSeeders\\' => array($baseDir . '/modules/Department/database/seeders'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
    'Database\\Seeders\\' => array($baseDir . '/database/seeders', $vendorDir . '/laravel/pint/database/seeders'),
    'Database\\Factories\\' => array($baseDir . '/database/factories', $vendorDir . '/laravel/pint/database/factories'),
    'DASPRiD\\Enum\\' => array($vendorDir . '/dasprid/enum/src'),
    'Cron\\' => array($vendorDir . '/dragonmantank/cron-expression/src/Cron'),
    'Composer\\Semver\\' => array($vendorDir . '/composer/semver/src'),
    'Composer\\Pcre\\' => array($vendorDir . '/composer/pcre/src'),
    'Complex\\' => array($vendorDir . '/markbaker/complex/classes/src'),
    'Collective\\Html\\' => array($vendorDir . '/laravelcollective/html/src'),
    'Classroom\\' => array($baseDir . '/modules/Classroom/app'),
    'ClassroomSeeders\\' => array($baseDir . '/modules/Classroom/database/seeders'),
    'ClassWork\\' => array($baseDir . '/modules/ClassWork/app'),
    'ClassWorkSeeders\\' => array($baseDir . '/modules/ClassWork/database/seeders'),
    'Circulars\\' => array($baseDir . '/modules/Circulars/app'),
    'CircularsSeeders\\' => array($baseDir . '/modules/Circulars/database/seeders'),
    'Carbon\\Doctrine\\' => array($vendorDir . '/carbonphp/carbon-doctrine-types/src/Carbon/Doctrine'),
    'Carbon\\' => array($vendorDir . '/nesbot/carbon/src/Carbon'),
    'Brick\\Math\\' => array($vendorDir . '/brick/math/src'),
    'Blogs\\' => array($baseDir . '/modules/Blogs/app'),
    'BlogsSeeders\\' => array($baseDir . '/modules/Blogs/database/seeders'),
    'Batches\\' => array($baseDir . '/modules/Batches/app'),
    'BatchesLeaveSeeders\\' => array($baseDir . '/modules/Batches/database/seeders'),
    'Barryvdh\\DomPDF\\' => array($vendorDir . '/barryvdh/laravel-dompdf/src'),
    'BaconQrCode\\' => array($vendorDir . '/bacon/bacon-qr-code/src'),
    'App\\' => array($baseDir . '/app', $vendorDir . '/laravel/pint/app'),
    'AnnualCalendar\\' => array($baseDir . '/modules/AnnualCalendar/app'),
    'AnnualCalendarSeeders\\' => array($baseDir . '/modules/AnnualCalendar/database/seeders'),
    'Admission\\' => array($baseDir . '/modules/Admission/app'),
    'AdmissionSeeders\\' => array($baseDir . '/modules/Admission/database/seeders'),
);
