import { SetStateAction } from 'react';
import { z } from 'zod';

export interface Blog {
  id: string;
  blogTitle: string;
  blogImage: string;
  blogDescription: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  createdAt: string;
  updatedAt: string;
  classId?: string;
  class?: {
    id: string;
    firstName: string;
    lastName: string;
    className: string;
  };
}

export interface CreateBlogInput {
  blogTitle: string;
  blogDescription: string;
  blogImage?: File;
}

export interface UpdateBlogInput {
  blogTitle?: string;
  blogDescription?: string;
  blogImage?: File;
  status?: 'PENDING' | 'APPROVED' | 'REJECTED';
}

export interface PaginatedBlogResponse {
  blogs: Blog[];
  total: number;
  currentPage: number;
  totalPages: number;
}


export interface Exam {
  total_student_intake: number | undefined;
  id: number;
  exam_name: string;
  start_date: string;
  duration: number;
  marks: string | number;
  total_questions: number;
  level: 'easy' | 'medium' | 'hard';
  max_classes_can_join: number;
  max_questions_class_can_apply?: number;
  joinedClassesCount: number;
  createdAt: string;
  updatedAt: string;
  coins_required: string;
  hasApplied: boolean;
  isMaxLimitReached: boolean;
  UwhizPriceRank: { id: string; rank: number; price: number }[];
  start_registration_date?: string;
  totalApplicants?: number;
  _count?: { examApplication: number };
  hasAttempted: boolean
}

export interface TransformedExam {
  id: number;
  exam_name: string;
  start_date: string;
  duration: number;
  marks: number;
  total_student_intake: number;
  total_questions: number;
  level: 'easy' | 'medium' | 'hard';
  max_classes_can_join: number;
  max_questions_class_can_apply: number;
  joinedClassesCount: number;
  createdAt: string;
  updatedAt: string;
  start_registration_date?: string;
  coins_required?: string;
}

export interface ExamInput {
  exam_name: string;
  start_date: string;
  duration: number;
  marks: number;
  total_student_intake: number;
  total_questions: number;
  level: 'easy' | 'medium' | 'hard';
  max_classes_can_join: number;
  max_questions_class_can_apply: number;
  joinedClassesCount: number;
  start_registration_date?: string;
  coins_required?: string;
}


export interface ExamClass {
  id: number;
  examName: string;
  classesName: string;
  classId: number;
  status: 'ACCEPT' | 'REJECT' | 'PENDING';
}

export interface PaginatedResponse<T> {
  data: SetStateAction<ExamClass[]>;
  max_questions_class_can_apply: SetStateAction<number>;
  exam_name: string;
  exam: any;
  questions: T[];
  total_questions: number;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}


export interface Question {
  id: number;
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAns: string;
  examId: number;
  classId: number;
}


export interface Result {
  userId: number;
  userName: string;
  examName: string;
  correctAnswers: number;
  totalQuestions: number;
  attempted: boolean;
  success: boolean;
  message: string;
  results: Result[];
}


export const questionSchema = z.object({
  question: z.string().min(1, 'Question is required'),
  optionOne: z.string().min(1, 'Option 1 is required'),
  optionTwo: z.string().min(1, 'Option 2 is required'),
  optionThree: z.string().min(1, 'Option 3 is required'),
  optionFour: z.string().min(1, 'Option 4 is required'),
  correctAns: z.string().min(1, 'Correct answer is required'),
  examId: z.number().positive('Valid exam ID is required'),
  classId: z.number().positive('Valid class ID is required'),
});

export type QuestionInput = z.infer<typeof questionSchema>;



export interface Option {
  key: string;
  value: string;
}

export interface Question {
  id: number;
  text: string;
  options: Option[];
  correctAnswer: string;
}

export interface QuizCardProps {
  examId: number | null;
}


export interface Schedule {
  id: string;
  startDate: string;
  endDate: string;
  yearName: string;
  status: 'ACTIVE' | 'INACTIVE';
  classId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateScheduleDto {
  startDate: string;
  endDate: string;
  yearName: string;
  status: 'ACTIVE' | 'INACTIVE';
}

export interface TimeSlot {
  id: string;
  startTime: string;
  endTime: string;
  classId: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTimeSlotDto {
  startTime: string;
  endTime: string;
  classId?: string;
}

export interface ClassRoom {
  id: string;
  classId: string;
  classRoomName: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateClassRoomDto {
  classId?: string;
  classRoomName: string;
}

export interface Subject {
  id: string;
  classId: string;
  subject: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSubjectDto {
  subject: string;
}

export interface PaginatedScheduleResponse {
  schedules: Schedule[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export type YearFormValues = {
  yearName: string;
  startDate: string;
  endDate: string;
  status: 'ACTIVE' | 'INACTIVE';
};

export interface Testimonial {
  id: string;
  message: string;
  rating: number;
  status: string;
  createdAt: string;
  class: {
    id: string;
    className: string;
    fullName?: string;
    classesLogo?: string | null;
    profilePhoto?: string | null;
  }
  studentId?: string;
  studentName?: string;
  student: {
    firstName: string;
    lastName: string;
  }
}

export interface Thought {
  id: string;
  thoughts: string;
  createdAt: string;
  class: {
    id: string;
  };
}

export interface ThoughtTableMeta {
  onEdit?: (row: Thought) => void;
  onDelete?: (id: string) => void;
  deletingId?: string | null;
}

export type ThoughtFormValues = {
  thoughts: string;
  classId: string;
};

export interface StudentRegisterData {
  firstName: string;
  lastName: string;
  contact: string;
  referralCode?: string;
}

export interface StudentLoginData {
  contact: string;
  referralCode?: string;
}

export interface GoogleAuthData {
  googleId: string;
  email: string;
  firstName: string;
  lastName: string;
  profilePhoto?: string;
}

export interface ForgotPasswordData {
  email: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;
    user: {
      id: number;
      firstName: string;
      lastName: string;
      email: string;
      contact: string;
      profilePhoto?: string;
    };
  };
}

export interface Ranking {
  rank: number;
  userId: string;
  name: string;
  score: number;
  totalQuestions: number;
  classesName: string;
  classesLogo: string | null;
}


export interface QuestionBankInput {
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAnswer: 'optionOne' | 'optionTwo' | 'optionThree' | 'optionFour' | undefined;
  medium: 'ENGLISH' | 'GUJARATI' | undefined;
  standard: string;
  subject: string;
  level: 'EASY' | 'MEDIUM' | 'HARD'| undefined;
  classID: string;
  chapter: 'Polynomial'| 'Statics' | 'Probability';
}

export interface QuestionBank {
  id: string;
  question: string;
  optionOne: string;
  optionTwo: string;
  optionThree: string;
  optionFour: string;
  correctAnswer: 'optionOne' | 'optionTwo' | 'optionThree' | 'optionFour';
  medium: 'ENGLISH' | 'GUJARATI'
  standard: string;
  subject: string;
  level: 'EASY' | 'MEDIUM' | 'HARD';
  classId: string;
    chapter:  'Polynomial'| 'Statics' | 'Probability';
}

export interface ChatMessage {
  id: string;
  text: string;
  sender: string;
  senderId: string;
  recipient?: string;
  recipientId?: string;
  timestamp: Date;
}

export interface OnlineUser {
  username: string;
  userType: string;
  userId?: string;
}

export interface SharedChatProps {
  userType: 'student' | 'class';
  isAuthenticated: boolean;
  username: string;
  userId: string;
  loginPath: string;
  initialSelectedUser?: string;
  initialSelectedUserId?: string;
  initialSelectedUserName?: string;
}

export interface mockExamResult {
  studentId: string;
  score: number;
  coinEarnings: number;
}

export interface CartItem {
  id: string;
  name: string;
  coinPrice: number;
  quantity: number;
  image: string;
}

export interface PurchaseData {
  cartItems: CartItem[];
  totalCoins: number;
}

export interface PurchaseResponse {
  success: boolean;
  orderId: string;
  message: string;
}

export interface StoreOrder {
  id: string;
  studentId: string;
  studentName: string;
  studentEmail: string | null;
  itemId: string;
  itemName: string;
  itemPrice: number;
  quantity: number;
  totalCoins: number;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  createdAt: string;
  updatedAt: string;
  item: {
    id: string;
    name: string;
    description: string;
    coinPrice: number;
    quantity: number;
    category: string;
    image: string | null;
    status: string;
  };
}
