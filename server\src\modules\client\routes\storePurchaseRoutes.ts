import { Router } from 'express';
import { purchaseItems, getMyOrders, getOrderDetails } from '../controllers/storePurchaseController';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';

const router = Router();

router.post('/purchase', studentAuthMiddleware, purchaseItems);

router.get('/orders', studentAuthMiddleware, getMyOrders);

router.get('/orders/:orderId', studentAuthMiddleware, getOrderDetails);

export default router;
