import { axiosInstance } from '@/lib/axios';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchStudentProfile = createAsyncThunk(
  'studentProfile/fetchStudentProfile',
  async (_, { rejectWithValue }) => {
    try {
      const studentToken = localStorage.getItem('studentToken');
      if (!studentToken) {
        return rejectWithValue('No authentication token found');
      }

      const res = await axiosInstance.get('/student-profile/all-data', {
        headers: {
          'Authorization': `Bearer ${studentToken}`
        }
      });

      if (res.data && typeof res.data === 'object') {
        // Check if the response has a data property (API wrapper format)
        if (res.data.success !== undefined && res.data.data !== undefined) {
          return res.data.data;
        }
        return res.data;
      }

      return null;
    } catch (err: any) {
      if (err.response?.status === 404) {
        return null;
      }

      const errorMessage = err.response?.data?.message || 'Failed to fetch student data';
      return rejectWithValue(errorMessage);
    }
  }
);

export const updateStudentProfile = createAsyncThunk(
  'studentProfile/updateStudentProfile',
  async (jsonData: any, { rejectWithValue }) => {
    try {
      const studentToken = localStorage.getItem('studentToken');
      if (!studentToken) {
        return rejectWithValue('No authentication token found');
      }

      const url = '/student-profile/combined';
      const method = 'put';

      const res = await axiosInstance({
        method,
        url,
        data: jsonData,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${studentToken}`
        },
      });

      if (res.data && typeof res.data === 'object') {
        // Check if the response has a data property (API wrapper format)
        if (res.data.success !== undefined && res.data.data !== undefined) {
          return res.data.data;
        }
        return res.data;
      }

      return null;
    } catch (err: any) {
      console.error('Profile update error:', err);
      let errorMsg = 'Failed to update student profile';

      if (err.response?.data?.message) {
        errorMsg = err.response.data.message;
      } else if (err.response?.data?.error) {
        errorMsg = err.response.data.error;
      } else if (err.response?.status === 400) {
        errorMsg = 'Invalid data provided. Please check all fields.';
      } else if (err.response?.status === 401) {
        errorMsg = 'Please login again to update your profile.';
      } else if (err.message) {
        errorMsg = err.message;
      }

      return rejectWithValue(errorMsg);
    }
  }
);