{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs"], "sourcesContent": ["\"use client\";\nimport { createContext } from 'react';\n\n/**\n * @public\n */\nconst MotionConfigContext = createContext({\n    transformPagePoint: (p) => p,\n    isStatic: false,\n    reducedMotion: \"never\",\n});\n\nexport { MotionConfigContext };\n"], "names": [], "mappings": ";;;AACA;AADA;;AAGA;;CAEC,GACD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;IACtC,oBAAoB,CAAC,IAAM;IAC3B,UAAU;IACV,eAAe;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/utils/use-constant.mjs"], "sourcesContent": ["import { useRef } from 'react';\n\n/**\n * Creates a constant value over the lifecycle of a component.\n *\n * Even if `useMemo` is provided an empty array as its final argument, it doesn't offer\n * a guarantee that it won't re-run for performance reasons later on. By using `useConstant`\n * you can ensure that initialisers don't execute twice or more.\n */\nfunction useConstant(init) {\n    const ref = useRef(null);\n    if (ref.current === null) {\n        ref.current = init();\n    }\n    return ref.current;\n}\n\nexport { useConstant };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;;;;CAMC,GACD,SAAS,YAAY,IAAI;IACrB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,IAAI,IAAI,OAAO,KAAK,MAAM;QACtB,IAAI,OAAO,GAAG;IAClB;IACA,OAAO,IAAI,OAAO;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/value/use-motion-value.mjs"], "sourcesContent": ["import { motionValue } from 'motion-dom';\nimport { useContext, useState, useEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useConstant } from '../utils/use-constant.mjs';\n\n/**\n * Creates a `MotionValue` to track the state and velocity of a value.\n *\n * Usually, these are created automatically. For advanced use-cases, like use with `useTransform`, you can create `MotionValue`s externally and pass them into the animated component via the `style` prop.\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const scale = useMotionValue(1)\n *\n *   return <motion.div style={{ scale }} />\n * }\n * ```\n *\n * @param initial - The initial state.\n *\n * @public\n */\nfunction useMotionValue(initial) {\n    const value = useConstant(() => motionValue(initial));\n    /**\n     * If this motion value is being used in static mode, like on\n     * the Framer canvas, force components to rerender when the motion\n     * value is updated.\n     */\n    const { isStatic } = useContext(MotionConfigContext);\n    if (isStatic) {\n        const [, setLatest] = useState(initial);\n        useEffect(() => value.on(\"change\", setLatest), []);\n    }\n    return value;\n}\n\nexport { useMotionValue };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA;;;;;;;;;;;;;;;;CAgBC,GACD,SAAS,eAAe,OAAO;IAC3B,MAAM,QAAQ,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE,IAAM,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IAC5C;;;;KAIC,GACD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,iLAAA,CAAA,sBAAmB;IACnD,IAAI,UAAU;QACV,MAAM,GAAG,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE,IAAM,MAAM,EAAE,CAAC,UAAU,YAAY,EAAE;IACrD;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/utils/use-animation-frame.mjs"], "sourcesContent": ["import { frame, cancelFrame } from 'motion-dom';\nimport { useRef, useContext, useEffect } from 'react';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\n\nfunction useAnimationFrame(callback) {\n    const initialTimestamp = useRef(0);\n    const { isStatic } = useContext(MotionConfigContext);\n    useEffect(() => {\n        if (isStatic)\n            return;\n        const provideTimeSinceStart = ({ timestamp, delta }) => {\n            if (!initialTimestamp.current)\n                initialTimestamp.current = timestamp;\n            callback(timestamp - initialTimestamp.current, delta);\n        };\n        frame.update(provideTimeSinceStart, true);\n        return () => cancelFrame(provideTimeSinceStart);\n    }, [callback]);\n}\n\nexport { useAnimationFrame };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,kBAAkB,QAAQ;IAC/B,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,iLAAA,CAAA,sBAAmB;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,UACA;QACJ,MAAM,wBAAwB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE;YAC/C,IAAI,CAAC,iBAAiB,OAAO,EACzB,iBAAiB,OAAO,GAAG;YAC/B,SAAS,YAAY,iBAAiB,OAAO,EAAE;QACnD;QACA,kKAAA,CAAA,QAAK,CAAC,MAAM,CAAC,uBAAuB;QACpC,OAAO,IAAM,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,GAAG;QAAC;KAAS;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/components/create-proxy.mjs"], "sourcesContent": ["import { warnOnce } from 'motion-utils';\n\nfunction createDOMMotionComponentProxy(componentFactory) {\n    if (typeof Proxy === \"undefined\") {\n        return componentFactory;\n    }\n    /**\n     * A cache of generated `motion` components, e.g `motion.div`, `motion.input` etc.\n     * Rather than generating them anew every render.\n     */\n    const componentCache = new Map();\n    const deprecatedFactoryFunction = (...args) => {\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(false, \"motion() is deprecated. Use motion.create() instead.\");\n        }\n        return componentFactory(...args);\n    };\n    return new Proxy(deprecatedFactoryFunction, {\n        /**\n         * Called when `motion` is referenced with a prop: `motion.div`, `motion.input` etc.\n         * The prop name is passed through as `key` and we can use that to generate a `motion`\n         * DOM component with that name.\n         */\n        get: (_target, key) => {\n            if (key === \"create\")\n                return componentFactory;\n            /**\n             * If this element doesn't exist in the component cache, create it and cache.\n             */\n            if (!componentCache.has(key)) {\n                componentCache.set(key, componentFactory(key));\n            }\n            return componentCache.get(key);\n        },\n    });\n}\n\nexport { createDOMMotionComponentProxy };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,8BAA8B,gBAAgB;IACnD,IAAI,OAAO,UAAU,aAAa;QAC9B,OAAO;IACX;IACA;;;KAGC,GACD,MAAM,iBAAiB,IAAI;IAC3B,MAAM,4BAA4B,CAAC,GAAG;QAClC,wCAA2C;YACvC,CAAA,GAAA,8JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;QACpB;QACA,OAAO,oBAAoB;IAC/B;IACA,OAAO,IAAI,MAAM,2BAA2B;QACxC;;;;SAIC,GACD,KAAK,CAAC,SAAS;YACX,IAAI,QAAQ,UACR,OAAO;YACX;;aAEC,GACD,IAAI,CAAC,eAAe,GAAG,CAAC,MAAM;gBAC1B,eAAe,GAAG,CAAC,KAAK,iBAAiB;YAC7C;YACA,OAAO,eAAe,GAAG,CAAC;QAC9B;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs"], "sourcesContent": ["function isAnimationControls(v) {\n    return (v !== null &&\n        typeof v === \"object\" &&\n        typeof v.start === \"function\");\n}\n\nexport { isAnimationControls };\n"], "names": [], "mappings": ";;;AAAA,SAAS,oBAAoB,CAAC;IAC1B,OAAQ,MAAM,QACV,OAAO,MAAM,YACb,OAAO,EAAE,KAAK,KAAK;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs"], "sourcesContent": ["function getValueState(visualElement) {\n    const state = [{}, {}];\n    visualElement?.values.forEach((value, key) => {\n        state[0][key] = value.get();\n        state[1][key] = value.getVelocity();\n    });\n    return state;\n}\nfunction resolveVariantFromProps(props, definition, custom, visualElement) {\n    /**\n     * If the variant definition is a function, resolve.\n     */\n    if (typeof definition === \"function\") {\n        const [current, velocity] = getValueState(visualElement);\n        definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n    }\n    /**\n     * If the variant definition is a variant label, or\n     * the function returned a variant label, resolve.\n     */\n    if (typeof definition === \"string\") {\n        definition = props.variants && props.variants[definition];\n    }\n    /**\n     * At this point we've resolved both functions and variant labels,\n     * but the resolved variant label might itself have been a function.\n     * If so, resolve. This can only have returned a valid target object.\n     */\n    if (typeof definition === \"function\") {\n        const [current, velocity] = getValueState(visualElement);\n        definition = definition(custom !== undefined ? custom : props.custom, current, velocity);\n    }\n    return definition;\n}\n\nexport { resolveVariantFromProps };\n"], "names": [], "mappings": ";;;AAAA,SAAS,cAAc,aAAa;IAChC,MAAM,QAAQ;QAAC,CAAC;QAAG,CAAC;KAAE;IACtB,eAAe,OAAO,QAAQ,CAAC,OAAO;QAClC,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,MAAM,GAAG;QACzB,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,MAAM,WAAW;IACrC;IACA,OAAO;AACX;AACA,SAAS,wBAAwB,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,aAAa;IACrE;;KAEC,GACD,IAAI,OAAO,eAAe,YAAY;QAClC,MAAM,CAAC,SAAS,SAAS,GAAG,cAAc;QAC1C,aAAa,WAAW,WAAW,YAAY,SAAS,MAAM,MAAM,EAAE,SAAS;IACnF;IACA;;;KAGC,GACD,IAAI,OAAO,eAAe,UAAU;QAChC,aAAa,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,WAAW;IAC7D;IACA;;;;KAIC,GACD,IAAI,OAAO,eAAe,YAAY;QAClC,MAAM,CAAC,SAAS,SAAS,GAAG,cAAc;QAC1C,aAAa,WAAW,WAAW,YAAY,SAAS,MAAM,MAAM,EAAE,SAAS;IACnF;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs"], "sourcesContent": ["import { resolveVariantFromProps } from './resolve-variants.mjs';\n\nfunction resolveVariant(visualElement, definition, custom) {\n    const props = visualElement.getProps();\n    return resolveVariantFromProps(props, definition, custom !== undefined ? custom : props.custom, visualElement);\n}\n\nexport { resolveVariant };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,eAAe,aAAa,EAAE,UAAU,EAAE,MAAM;IACrD,MAAM,QAAQ,cAAc,QAAQ;IACpC,OAAO,CAAA,GAAA,yLAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,YAAY,WAAW,YAAY,SAAS,MAAM,MAAM,EAAE;AACpG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs"], "sourcesContent": ["const isKeyframesTarget = (v) => {\n    return Array.isArray(v);\n};\n\nexport { isKeyframesTarget };\n"], "names": [], "mappings": ";;;AAAA,MAAM,oBAAoB,CAAC;IACvB,OAAO,MAAM,OAAO,CAAC;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/utils/setters.mjs"], "sourcesContent": ["import { motionValue } from 'motion-dom';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\n\n/**\n * Set VisualElement's MotionValue, creating a new MotionValue for it if\n * it doesn't exist.\n */\nfunction setMotionValue(visualElement, key, value) {\n    if (visualElement.hasValue(key)) {\n        visualElement.getValue(key).set(value);\n    }\n    else {\n        visualElement.addValue(key, motionValue(value));\n    }\n}\nfunction resolveFinalValueInKeyframes(v) {\n    // TODO maybe throw if v.length - 1 is placeholder token?\n    return isKeyframesTarget(v) ? v[v.length - 1] || 0 : v;\n}\nfunction setTarget(visualElement, definition) {\n    const resolved = resolveVariant(visualElement, definition);\n    let { transitionEnd = {}, transition = {}, ...target } = resolved || {};\n    target = { ...target, ...transitionEnd };\n    for (const key in target) {\n        const value = resolveFinalValueInKeyframes(target[key]);\n        setMotionValue(visualElement, key, value);\n    }\n}\n\nexport { setTarget };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA;;;CAGC,GACD,SAAS,eAAe,aAAa,EAAE,GAAG,EAAE,KAAK;IAC7C,IAAI,cAAc,QAAQ,CAAC,MAAM;QAC7B,cAAc,QAAQ,CAAC,KAAK,GAAG,CAAC;IACpC,OACK;QACD,cAAc,QAAQ,CAAC,KAAK,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IAC5C;AACJ;AACA,SAAS,6BAA6B,CAAC;IACnC,yDAAyD;IACzD,OAAO,CAAA,GAAA,kMAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,IAAI;AACzD;AACA,SAAS,UAAU,aAAa,EAAE,UAAU;IACxC,MAAM,WAAW,CAAA,GAAA,oMAAA,CAAA,iBAAc,AAAD,EAAE,eAAe;IAC/C,IAAI,EAAE,gBAAgB,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,GAAG,QAAQ,GAAG,YAAY,CAAC;IACtE,SAAS;QAAE,GAAG,MAAM;QAAE,GAAG,aAAa;IAAC;IACvC,IAAK,MAAM,OAAO,OAAQ;QACtB,MAAM,QAAQ,6BAA6B,MAAM,CAAC,IAAI;QACtD,eAAe,eAAe,KAAK;IACvC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/value/use-will-change/is.mjs"], "sourcesContent": ["import { isMotionValue } from 'motion-dom';\n\nfunction isWillChangeMotionValue(value) {\n    return Boolean(isMotionValue(value) && value.add);\n}\n\nexport { isWillChangeMotionValue };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,wBAAwB,KAAK;IAClC,OAAO,QAAQ,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,MAAM,GAAG;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/value/use-will-change/add-will-change.mjs"], "sourcesContent": ["import { MotionGlobalConfig } from 'motion-utils';\nimport { isWillChangeMotionValue } from './is.mjs';\n\nfunction addValueToWillChange(visualElement, key) {\n    const willChange = visualElement.getValue(\"willChange\");\n    /**\n     * It could be that a user has set will<PERSON>hange to a regular MotionValue,\n     * in which case we can't add the value to it.\n     */\n    if (isWillChangeMotionValue(willChange)) {\n        return willChange.add(key);\n    }\n    else if (!willChange && MotionGlobalConfig.WillChange) {\n        const newWillChange = new MotionGlobalConfig.WillChange(\"auto\");\n        visualElement.addValue(\"willChange\", newWillChange);\n        newWillChange.add(key);\n    }\n}\n\nexport { addValueToWillChange };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,qBAAqB,aAAa,EAAE,GAAG;IAC5C,MAAM,aAAa,cAAc,QAAQ,CAAC;IAC1C;;;KAGC,GACD,IAAI,CAAA,GAAA,uLAAA,CAAA,0BAAuB,AAAD,EAAE,aAAa;QACrC,OAAO,WAAW,GAAG,CAAC;IAC1B,OACK,IAAI,CAAC,cAAc,kKAAA,CAAA,qBAAkB,CAAC,UAAU,EAAE;QACnD,MAAM,gBAAgB,IAAI,kKAAA,CAAA,qBAAkB,CAAC,UAAU,CAAC;QACxD,cAAc,QAAQ,CAAC,cAAc;QACrC,cAAc,GAAG,CAAC;IACtB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs"], "sourcesContent": ["/**\n * Convert camelCase to dash-case properties.\n */\nconst camelToDash = (str) => str.replace(/([a-z])([A-Z])/gu, \"$1-$2\").toLowerCase();\n\nexport { camelToDash };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,cAAc,CAAC,MAAQ,IAAI,OAAO,CAAC,oBAAoB,SAAS,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs"], "sourcesContent": ["import { camelToDash } from '../../render/dom/utils/camel-to-dash.mjs';\n\nconst optimizedAppearDataId = \"framerAppearId\";\nconst optimizedAppearDataAttribute = \"data-\" + camelToDash(optimizedAppearDataId);\n\nexport { optimizedAppearDataAttribute, optimizedAppearDataId };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,wBAAwB;AAC9B,MAAM,+BAA+B,UAAU,CAAA,GAAA,gMAAA,CAAA,cAAW,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/animation/optimized-appear/get-appear-id.mjs"], "sourcesContent": ["import { optimizedAppearDataAttribute } from './data-id.mjs';\n\nfunction getOptimisedAppearId(visualElement) {\n    return visualElement.props[optimizedAppearDataAttribute];\n}\n\nexport { getOptimisedAppearId };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,qBAAqB,aAAa;IACvC,OAAO,cAAc,KAAK,CAAC,iMAAA,CAAA,+BAA4B,CAAC;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs"], "sourcesContent": ["const isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1\n        ? 0\n        : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\nexport { getFinalKeyframe };\n"], "names": [], "mappings": ";;;AAAA,MAAM,YAAY,CAAC,QAAU,UAAU;AACvC,SAAS,iBAAiB,SAAS,EAAE,EAAE,MAAM,EAAE,aAAa,MAAM,EAAE,EAAE,aAAa;IAC/E,MAAM,oBAAoB,UAAU,MAAM,CAAC;IAC3C,MAAM,QAAQ,UAAU,eAAe,UAAU,SAAS,MAAM,IAC1D,IACA,kBAAkB,MAAM,GAAG;IACjC,OAAO,CAAC,SAAS,kBAAkB,YAC7B,iBAAiB,CAAC,MAAM,GACxB;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/animation/utils/default-transitions.mjs"], "sourcesContent": ["import { transformProps } from 'motion-dom';\n\nconst underDampedSpring = {\n    type: \"spring\",\n    stiffness: 500,\n    damping: 25,\n    restSpeed: 10,\n};\nconst criticallyDampedSpring = (target) => ({\n    type: \"spring\",\n    stiffness: 550,\n    damping: target === 0 ? 2 * Math.sqrt(550) : 30,\n    restSpeed: 10,\n});\nconst keyframesTransition = {\n    type: \"keyframes\",\n    duration: 0.8,\n};\n/**\n * Default easing curve is a slightly shallower version of\n * the default browser easing curve.\n */\nconst ease = {\n    type: \"keyframes\",\n    ease: [0.25, 0.1, 0.35, 1],\n    duration: 0.3,\n};\nconst getDefaultTransition = (valueKey, { keyframes }) => {\n    if (keyframes.length > 2) {\n        return keyframesTransition;\n    }\n    else if (transformProps.has(valueKey)) {\n        return valueKey.startsWith(\"scale\")\n            ? criticallyDampedSpring(keyframes[1])\n            : underDampedSpring;\n    }\n    return ease;\n};\n\nexport { getDefaultTransition };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;IACtB,MAAM;IACN,WAAW;IACX,SAAS;IACT,WAAW;AACf;AACA,MAAM,yBAAyB,CAAC,SAAW,CAAC;QACxC,MAAM;QACN,WAAW;QACX,SAAS,WAAW,IAAI,IAAI,KAAK,IAAI,CAAC,OAAO;QAC7C,WAAW;IACf,CAAC;AACD,MAAM,sBAAsB;IACxB,MAAM;IACN,UAAU;AACd;AACA;;;CAGC,GACD,MAAM,OAAO;IACT,MAAM;IACN,MAAM;QAAC;QAAM;QAAK;QAAM;KAAE;IAC1B,UAAU;AACd;AACA,MAAM,uBAAuB,CAAC,UAAU,EAAE,SAAS,EAAE;IACjD,IAAI,UAAU,MAAM,GAAG,GAAG;QACtB,OAAO;IACX,OACK,IAAI,oLAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,WAAW;QACnC,OAAO,SAAS,UAAU,CAAC,WACrB,uBAAuB,SAAS,CAAC,EAAE,IACnC;IACV;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/animation/utils/is-transition-defined.mjs"], "sourcesContent": ["/**\n * Decide whether a transition is defined on a given Transition.\n * This filters out orchestration options and returns true\n * if any options are left.\n */\nfunction isTransitionDefined({ when, delay: _delay, delayChildren, staggerChildren, staggerDirection, repeat, repeatType, repeatDelay, from, elapsed, ...transition }) {\n    return !!Object.keys(transition).length;\n}\n\nexport { isTransitionDefined };\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACD,SAAS,oBAAoB,EAAE,IAAI,EAAE,OAAO,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,YAAY;IACjK,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC,YAAY,MAAM;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs"], "sourcesContent": ["import { getValueTransition, frame, JSAnimation, AsyncMotionValueAnimation } from 'motion-dom';\nimport { secondsToMilliseconds, MotionGlobalConfig } from 'motion-utils';\nimport { getFinalKeyframe } from '../animators/waapi/utils/get-final-keyframe.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isTransitionDefined } from '../utils/is-transition-defined.mjs';\n\nconst animateMotionValue = (name, value, target, transition = {}, element, isHandoff) => (onComplete) => {\n    const valueTransition = getValueTransition(transition, name) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let { elapsed = 0 } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    const options = {\n        keyframes: Array.isArray(target) ? target : [null, target],\n        ease: \"easeOut\",\n        velocity: value.getVelocity(),\n        ...valueTransition,\n        delay: -elapsed,\n        onUpdate: (v) => {\n            value.set(v);\n            valueTransition.onUpdate && valueTransition.onUpdate(v);\n        },\n        onComplete: () => {\n            onComplete();\n            valueTransition.onComplete && valueTransition.onComplete();\n        },\n        name,\n        motionValue: value,\n        element: isHandoff ? undefined : element,\n    };\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unique transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n        Object.assign(options, getDefaultTransition(name, options));\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    options.duration && (options.duration = secondsToMilliseconds(options.duration));\n    options.repeatDelay && (options.repeatDelay = secondsToMilliseconds(options.repeatDelay));\n    /**\n     * Support deprecated way to set initial value. Prefer keyframe syntax.\n     */\n    if (options.from !== undefined) {\n        options.keyframes[0] = options.from;\n    }\n    let shouldSkip = false;\n    if (options.type === false ||\n        (options.duration === 0 && !options.repeatDelay)) {\n        options.duration = 0;\n        if (options.delay === 0) {\n            shouldSkip = true;\n        }\n    }\n    if (MotionGlobalConfig.instantAnimations ||\n        MotionGlobalConfig.skipAnimations) {\n        shouldSkip = true;\n        options.duration = 0;\n        options.delay = 0;\n    }\n    /**\n     * If the transition type or easing has been explicitly set by the user\n     * then we don't want to allow flattening the animation.\n     */\n    options.allowFlatten = !valueTransition.type && !valueTransition.ease;\n    /**\n     * If we can or must skip creating the animation, and apply only\n     * the final keyframe, do so. We also check once keyframes are resolved but\n     * this early check prevents the need to create an animation at all.\n     */\n    if (shouldSkip && !isHandoff && value.get() !== undefined) {\n        const finalKeyframe = getFinalKeyframe(options.keyframes, valueTransition);\n        if (finalKeyframe !== undefined) {\n            frame.update(() => {\n                options.onUpdate(finalKeyframe);\n                options.onComplete();\n            });\n            return;\n        }\n    }\n    return valueTransition.isSync\n        ? new JSAnimation(options)\n        : new AsyncMotionValueAnimation(options);\n};\n\nexport { animateMotionValue };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,qBAAqB,CAAC,MAAM,OAAO,QAAQ,aAAa,CAAC,CAAC,EAAE,SAAS,YAAc,CAAC;QACtF,MAAM,kBAAkB,CAAA,GAAA,gMAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,SAAS,CAAC;QACjE;;;;KAIC,GACD,MAAM,QAAQ,gBAAgB,KAAK,IAAI,WAAW,KAAK,IAAI;QAC3D;;;KAGC,GACD,IAAI,EAAE,UAAU,CAAC,EAAE,GAAG;QACtB,UAAU,UAAU,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE;QAC1C,MAAM,UAAU;YACZ,WAAW,MAAM,OAAO,CAAC,UAAU,SAAS;gBAAC;gBAAM;aAAO;YAC1D,MAAM;YACN,UAAU,MAAM,WAAW;YAC3B,GAAG,eAAe;YAClB,OAAO,CAAC;YACR,UAAU,CAAC;gBACP,MAAM,GAAG,CAAC;gBACV,gBAAgB,QAAQ,IAAI,gBAAgB,QAAQ,CAAC;YACzD;YACA,YAAY;gBACR;gBACA,gBAAgB,UAAU,IAAI,gBAAgB,UAAU;YAC5D;YACA;YACA,aAAa;YACb,SAAS,YAAY,YAAY;QACrC;QACA;;;KAGC,GACD,IAAI,CAAC,CAAA,GAAA,oMAAA,CAAA,sBAAmB,AAAD,EAAE,kBAAkB;YACvC,OAAO,MAAM,CAAC,SAAS,CAAA,GAAA,+LAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM;QACtD;QACA;;;;KAIC,GACD,QAAQ,QAAQ,IAAI,CAAC,QAAQ,QAAQ,GAAG,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ,QAAQ,CAAC;QAC/E,QAAQ,WAAW,IAAI,CAAC,QAAQ,WAAW,GAAG,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ,WAAW,CAAC;QACxF;;KAEC,GACD,IAAI,QAAQ,IAAI,KAAK,WAAW;YAC5B,QAAQ,SAAS,CAAC,EAAE,GAAG,QAAQ,IAAI;QACvC;QACA,IAAI,aAAa;QACjB,IAAI,QAAQ,IAAI,KAAK,SAChB,QAAQ,QAAQ,KAAK,KAAK,CAAC,QAAQ,WAAW,EAAG;YAClD,QAAQ,QAAQ,GAAG;YACnB,IAAI,QAAQ,KAAK,KAAK,GAAG;gBACrB,aAAa;YACjB;QACJ;QACA,IAAI,kKAAA,CAAA,qBAAkB,CAAC,iBAAiB,IACpC,kKAAA,CAAA,qBAAkB,CAAC,cAAc,EAAE;YACnC,aAAa;YACb,QAAQ,QAAQ,GAAG;YACnB,QAAQ,KAAK,GAAG;QACpB;QACA;;;KAGC,GACD,QAAQ,YAAY,GAAG,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,IAAI;QACrE;;;;KAIC,GACD,IAAI,cAAc,CAAC,aAAa,MAAM,GAAG,OAAO,WAAW;YACvD,MAAM,gBAAgB,CAAA,GAAA,uNAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,SAAS,EAAE;YAC1D,IAAI,kBAAkB,WAAW;gBAC7B,kKAAA,CAAA,QAAK,CAAC,MAAM,CAAC;oBACT,QAAQ,QAAQ,CAAC;oBACjB,QAAQ,UAAU;gBACtB;gBACA;YACJ;QACJ;QACA,OAAO,gBAAgB,MAAM,GACvB,IAAI,wKAAA,CAAA,cAAW,CAAC,WAChB,IAAI,sLAAA,CAAA,4BAAyB,CAAC;IACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs"], "sourcesContent": ["import { getValueTransition, frame, positionalKeys } from 'motion-dom';\nimport { setTarget } from '../../render/utils/setters.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { getOptimisedAppearId } from '../optimized-appear/get-appear-id.mjs';\nimport { animateMotionValue } from './motion-value.mjs';\n\n/**\n * Decide whether we should block this animation. Previously, we achieved this\n * just by checking whether the key was listed in protectedKeys, but this\n * posed problems if an animation was triggered by afterChildren and protectedKeys\n * had been set to true in the meantime.\n */\nfunction shouldBlockAnimation({ protectedKeys, needsAnimating }, key) {\n    const shouldBlock = protectedKeys.hasOwnProperty(key) && needsAnimating[key] !== true;\n    needsAnimating[key] = false;\n    return shouldBlock;\n}\nfunction animateTarget(visualElement, targetAndTransition, { delay = 0, transitionOverride, type } = {}) {\n    let { transition = visualElement.getDefaultTransition(), transitionEnd, ...target } = targetAndTransition;\n    if (transitionOverride)\n        transition = transitionOverride;\n    const animations = [];\n    const animationTypeState = type &&\n        visualElement.animationState &&\n        visualElement.animationState.getState()[type];\n    for (const key in target) {\n        const value = visualElement.getValue(key, visualElement.latestValues[key] ?? null);\n        const valueTarget = target[key];\n        if (valueTarget === undefined ||\n            (animationTypeState &&\n                shouldBlockAnimation(animationTypeState, key))) {\n            continue;\n        }\n        const valueTransition = {\n            delay,\n            ...getValueTransition(transition || {}, key),\n        };\n        /**\n         * If the value is already at the defined target, skip the animation.\n         */\n        const currentValue = value.get();\n        if (currentValue !== undefined &&\n            !value.isAnimating &&\n            !Array.isArray(valueTarget) &&\n            valueTarget === currentValue &&\n            !valueTransition.velocity) {\n            continue;\n        }\n        /**\n         * If this is the first time a value is being animated, check\n         * to see if we're handling off from an existing animation.\n         */\n        let isHandoff = false;\n        if (window.MotionHandoffAnimation) {\n            const appearId = getOptimisedAppearId(visualElement);\n            if (appearId) {\n                const startTime = window.MotionHandoffAnimation(appearId, key, frame);\n                if (startTime !== null) {\n                    valueTransition.startTime = startTime;\n                    isHandoff = true;\n                }\n            }\n        }\n        addValueToWillChange(visualElement, key);\n        value.start(animateMotionValue(key, value, valueTarget, visualElement.shouldReduceMotion && positionalKeys.has(key)\n            ? { type: false }\n            : valueTransition, visualElement, isHandoff));\n        const animation = value.animation;\n        if (animation) {\n            animations.push(animation);\n        }\n    }\n    if (transitionEnd) {\n        Promise.all(animations).then(() => {\n            frame.update(() => {\n                transitionEnd && setTarget(visualElement, transitionEnd);\n            });\n        });\n    }\n    return animations;\n}\n\nexport { animateTarget };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA;;;;;CAKC,GACD,SAAS,qBAAqB,EAAE,aAAa,EAAE,cAAc,EAAE,EAAE,GAAG;IAChE,MAAM,cAAc,cAAc,cAAc,CAAC,QAAQ,cAAc,CAAC,IAAI,KAAK;IACjF,cAAc,CAAC,IAAI,GAAG;IACtB,OAAO;AACX;AACA,SAAS,cAAc,aAAa,EAAE,mBAAmB,EAAE,EAAE,QAAQ,CAAC,EAAE,kBAAkB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACnG,IAAI,EAAE,aAAa,cAAc,oBAAoB,EAAE,EAAE,aAAa,EAAE,GAAG,QAAQ,GAAG;IACtF,IAAI,oBACA,aAAa;IACjB,MAAM,aAAa,EAAE;IACrB,MAAM,qBAAqB,QACvB,cAAc,cAAc,IAC5B,cAAc,cAAc,CAAC,QAAQ,EAAE,CAAC,KAAK;IACjD,IAAK,MAAM,OAAO,OAAQ;QACtB,MAAM,QAAQ,cAAc,QAAQ,CAAC,KAAK,cAAc,YAAY,CAAC,IAAI,IAAI;QAC7E,MAAM,cAAc,MAAM,CAAC,IAAI;QAC/B,IAAI,gBAAgB,aACf,sBACG,qBAAqB,oBAAoB,MAAO;YACpD;QACJ;QACA,MAAM,kBAAkB;YACpB;YACA,GAAG,CAAA,GAAA,gMAAA,CAAA,qBAAkB,AAAD,EAAE,cAAc,CAAC,GAAG,IAAI;QAChD;QACA;;SAEC,GACD,MAAM,eAAe,MAAM,GAAG;QAC9B,IAAI,iBAAiB,aACjB,CAAC,MAAM,WAAW,IAClB,CAAC,MAAM,OAAO,CAAC,gBACf,gBAAgB,gBAChB,CAAC,gBAAgB,QAAQ,EAAE;YAC3B;QACJ;QACA;;;SAGC,GACD,IAAI,YAAY;QAChB,IAAI,OAAO,sBAAsB,EAAE;YAC/B,MAAM,WAAW,CAAA,GAAA,0MAAA,CAAA,uBAAoB,AAAD,EAAE;YACtC,IAAI,UAAU;gBACV,MAAM,YAAY,OAAO,sBAAsB,CAAC,UAAU,KAAK,kKAAA,CAAA,QAAK;gBACpE,IAAI,cAAc,MAAM;oBACpB,gBAAgB,SAAS,GAAG;oBAC5B,YAAY;gBAChB;YACJ;QACJ;QACA,CAAA,GAAA,0MAAA,CAAA,uBAAoB,AAAD,EAAE,eAAe;QACpC,MAAM,KAAK,CAAC,CAAA,GAAA,6LAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,OAAO,aAAa,cAAc,kBAAkB,IAAI,mLAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,OACzG;YAAE,MAAM;QAAM,IACd,iBAAiB,eAAe;QACtC,MAAM,YAAY,MAAM,SAAS;QACjC,IAAI,WAAW;YACX,WAAW,IAAI,CAAC;QACpB;IACJ;IACA,IAAI,eAAe;QACf,QAAQ,GAAG,CAAC,YAAY,IAAI,CAAC;YACzB,kKAAA,CAAA,QAAK,CAAC,MAAM,CAAC;gBACT,iBAAiB,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe;YAC9C;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-variant.mjs"], "sourcesContent": ["import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\n\nfunction animateVariant(visualElement, variant, options = {}) {\n    const resolved = resolveVariant(visualElement, variant, options.type === \"exit\"\n        ? visualElement.presenceContext?.custom\n        : undefined);\n    let { transition = visualElement.getDefaultTransition() || {} } = resolved || {};\n    if (options.transitionOverride) {\n        transition = options.transitionOverride;\n    }\n    /**\n     * If we have a variant, create a callback that runs it as an animation.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getAnimation = resolved\n        ? () => Promise.all(animateTarget(visualElement, resolved, options))\n        : () => Promise.resolve();\n    /**\n     * If we have children, create a callback that runs all their animations.\n     * Otherwise, we resolve a Promise immediately for a composable no-op.\n     */\n    const getChildAnimations = visualElement.variantChildren && visualElement.variantChildren.size\n        ? (forwardDelay = 0) => {\n            const { delayChildren = 0, staggerChildren, staggerDirection, } = transition;\n            return animateChildren(visualElement, variant, forwardDelay, delayChildren, staggerChildren, staggerDirection, options);\n        }\n        : () => Promise.resolve();\n    /**\n     * If the transition explicitly defines a \"when\" option, we need to resolve either\n     * this animation or all children animations before playing the other.\n     */\n    const { when } = transition;\n    if (when) {\n        const [first, last] = when === \"beforeChildren\"\n            ? [getAnimation, getChildAnimations]\n            : [getChildAnimations, getAnimation];\n        return first().then(() => last());\n    }\n    else {\n        return Promise.all([getAnimation(), getChildAnimations(options.delay)]);\n    }\n}\nfunction animateChildren(visualElement, variant, delay = 0, delayChildren = 0, staggerChildren = 0, staggerDirection = 1, options) {\n    const animations = [];\n    const numChildren = visualElement.variantChildren.size;\n    const maxStaggerDuration = (numChildren - 1) * staggerChildren;\n    const delayIsFunction = typeof delayChildren === \"function\";\n    const generateStaggerDuration = delayIsFunction\n        ? (i) => delayChildren(i, numChildren)\n        : // Support deprecated staggerChildren\n            staggerDirection === 1\n                ? (i = 0) => i * staggerChildren\n                : (i = 0) => maxStaggerDuration - i * staggerChildren;\n    Array.from(visualElement.variantChildren)\n        .sort(sortByTreeOrder)\n        .forEach((child, i) => {\n        child.notify(\"AnimationStart\", variant);\n        animations.push(animateVariant(child, variant, {\n            ...options,\n            delay: delay +\n                (delayIsFunction ? 0 : delayChildren) +\n                generateStaggerDuration(i),\n        }).then(() => child.notify(\"AnimationComplete\", variant)));\n    });\n    return Promise.all(animations);\n}\nfunction sortByTreeOrder(a, b) {\n    return a.sortNodePosition(b);\n}\n\nexport { animateVariant, sortByTreeOrder };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,SAAS,eAAe,aAAa,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;IACxD,MAAM,WAAW,CAAA,GAAA,oMAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,SAAS,QAAQ,IAAI,KAAK,SACnE,cAAc,eAAe,EAAE,SAC/B;IACN,IAAI,EAAE,aAAa,cAAc,oBAAoB,MAAM,CAAC,CAAC,EAAE,GAAG,YAAY,CAAC;IAC/E,IAAI,QAAQ,kBAAkB,EAAE;QAC5B,aAAa,QAAQ,kBAAkB;IAC3C;IACA;;;KAGC,GACD,MAAM,eAAe,WACf,IAAM,QAAQ,GAAG,CAAC,CAAA,GAAA,yMAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,UAAU,YACzD,IAAM,QAAQ,OAAO;IAC3B;;;KAGC,GACD,MAAM,qBAAqB,cAAc,eAAe,IAAI,cAAc,eAAe,CAAC,IAAI,GACxF,CAAC,eAAe,CAAC;QACf,MAAM,EAAE,gBAAgB,CAAC,EAAE,eAAe,EAAE,gBAAgB,EAAG,GAAG;QAClE,OAAO,gBAAgB,eAAe,SAAS,cAAc,eAAe,iBAAiB,kBAAkB;IACnH,IACE,IAAM,QAAQ,OAAO;IAC3B;;;KAGC,GACD,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,IAAI,MAAM;QACN,MAAM,CAAC,OAAO,KAAK,GAAG,SAAS,mBACzB;YAAC;YAAc;SAAmB,GAClC;YAAC;YAAoB;SAAa;QACxC,OAAO,QAAQ,IAAI,CAAC,IAAM;IAC9B,OACK;QACD,OAAO,QAAQ,GAAG,CAAC;YAAC;YAAgB,mBAAmB,QAAQ,KAAK;SAAE;IAC1E;AACJ;AACA,SAAS,gBAAgB,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,gBAAgB,CAAC,EAAE,kBAAkB,CAAC,EAAE,mBAAmB,CAAC,EAAE,OAAO;IAC7H,MAAM,aAAa,EAAE;IACrB,MAAM,cAAc,cAAc,eAAe,CAAC,IAAI;IACtD,MAAM,qBAAqB,CAAC,cAAc,CAAC,IAAI;IAC/C,MAAM,kBAAkB,OAAO,kBAAkB;IACjD,MAAM,0BAA0B,kBAC1B,CAAC,IAAM,cAAc,GAAG,eAEtB,qBAAqB,IACf,CAAC,IAAI,CAAC,GAAK,IAAI,kBACf,CAAC,IAAI,CAAC,GAAK,qBAAqB,IAAI;IAClD,MAAM,IAAI,CAAC,cAAc,eAAe,EACnC,IAAI,CAAC,iBACL,OAAO,CAAC,CAAC,OAAO;QACjB,MAAM,MAAM,CAAC,kBAAkB;QAC/B,WAAW,IAAI,CAAC,eAAe,OAAO,SAAS;YAC3C,GAAG,OAAO;YACV,OAAO,QACH,CAAC,kBAAkB,IAAI,aAAa,IACpC,wBAAwB;QAChC,GAAG,IAAI,CAAC,IAAM,MAAM,MAAM,CAAC,qBAAqB;IACpD;IACA,OAAO,QAAQ,GAAG,CAAC;AACvB;AACA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IACzB,OAAO,EAAE,gBAAgB,CAAC;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/animation/interfaces/visual-element.mjs"], "sourcesContent": ["import { resolveVariant } from '../../render/utils/resolve-dynamic-variants.mjs';\nimport { animateTarget } from './visual-element-target.mjs';\nimport { animateVariant } from './visual-element-variant.mjs';\n\nfunction animateVisualElement(visualElement, definition, options = {}) {\n    visualElement.notify(\"AnimationStart\", definition);\n    let animation;\n    if (Array.isArray(definition)) {\n        const animations = definition.map((variant) => animateVariant(visualElement, variant, options));\n        animation = Promise.all(animations);\n    }\n    else if (typeof definition === \"string\") {\n        animation = animateVariant(visualElement, definition, options);\n    }\n    else {\n        const resolvedDefinition = typeof definition === \"function\"\n            ? resolveVariant(visualElement, definition, options.custom)\n            : definition;\n        animation = Promise.all(animateTarget(visualElement, resolvedDefinition, options));\n    }\n    return animation.then(() => {\n        visualElement.notify(\"AnimationComplete\", definition);\n    });\n}\n\nexport { animateVisualElement };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,qBAAqB,aAAa,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;IACjE,cAAc,MAAM,CAAC,kBAAkB;IACvC,IAAI;IACJ,IAAI,MAAM,OAAO,CAAC,aAAa;QAC3B,MAAM,aAAa,WAAW,GAAG,CAAC,CAAC,UAAY,CAAA,GAAA,0MAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,SAAS;QACtF,YAAY,QAAQ,GAAG,CAAC;IAC5B,OACK,IAAI,OAAO,eAAe,UAAU;QACrC,YAAY,CAAA,GAAA,0MAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,YAAY;IAC1D,OACK;QACD,MAAM,qBAAqB,OAAO,eAAe,aAC3C,CAAA,GAAA,oMAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,YAAY,QAAQ,MAAM,IACxD;QACN,YAAY,QAAQ,GAAG,CAAC,CAAA,GAAA,yMAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,oBAAoB;IAC7E;IACA,OAAO,UAAU,IAAI,CAAC;QAClB,cAAc,MAAM,CAAC,qBAAqB;IAC9C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 760, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/utils/shallow-compare.mjs"], "sourcesContent": ["function shallowCompare(next, prev) {\n    if (!Array.isArray(prev))\n        return false;\n    const prevLength = prev.length;\n    if (prevLength !== next.length)\n        return false;\n    for (let i = 0; i < prevLength; i++) {\n        if (prev[i] !== next[i])\n            return false;\n    }\n    return true;\n}\n\nexport { shallowCompare };\n"], "names": [], "mappings": ";;;AAAA,SAAS,eAAe,IAAI,EAAE,IAAI;IAC9B,IAAI,CAAC,MAAM,OAAO,CAAC,OACf,OAAO;IACX,MAAM,aAAa,KAAK,MAAM;IAC9B,IAAI,eAAe,KAAK,MAAM,EAC1B,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACjC,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EACnB,OAAO;IACf;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs"], "sourcesContent": ["/**\n * Decides if the supplied variable is variant label\n */\nfunction isVariantLabel(v) {\n    return typeof v === \"string\" || Array.isArray(v);\n}\n\nexport { isVariantLabel };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,SAAS,eAAe,CAAC;IACrB,OAAO,OAAO,MAAM,YAAY,MAAM,OAAO,CAAC;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/utils/variant-props.mjs"], "sourcesContent": ["const variantPriorityOrder = [\n    \"animate\",\n    \"whileInView\",\n    \"whileFocus\",\n    \"whileHover\",\n    \"whileTap\",\n    \"whileDrag\",\n    \"exit\",\n];\nconst variantProps = [\"initial\", ...variantPriorityOrder];\n\nexport { variantPriorityOrder, variantProps };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,uBAAuB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,MAAM,eAAe;IAAC;OAAc;CAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 818, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/utils/get-variant-context.mjs"], "sourcesContent": ["import { isVariantLabel } from './is-variant-label.mjs';\nimport { variantProps } from './variant-props.mjs';\n\nconst numVariantProps = variantProps.length;\nfunction getVariantContext(visualElement) {\n    if (!visualElement)\n        return undefined;\n    if (!visualElement.isControllingVariants) {\n        const context = visualElement.parent\n            ? getVariantContext(visualElement.parent) || {}\n            : {};\n        if (visualElement.props.initial !== undefined) {\n            context.initial = visualElement.props.initial;\n        }\n        return context;\n    }\n    const context = {};\n    for (let i = 0; i < numVariantProps; i++) {\n        const name = variantProps[i];\n        const prop = visualElement.props[name];\n        if (isVariantLabel(prop) || prop === false) {\n            context[name] = prop;\n        }\n    }\n    return context;\n}\n\nexport { getVariantContext };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,kBAAkB,sLAAA,CAAA,eAAY,CAAC,MAAM;AAC3C,SAAS,kBAAkB,aAAa;IACpC,IAAI,CAAC,eACD,OAAO;IACX,IAAI,CAAC,cAAc,qBAAqB,EAAE;QACtC,MAAM,UAAU,cAAc,MAAM,GAC9B,kBAAkB,cAAc,MAAM,KAAK,CAAC,IAC5C,CAAC;QACP,IAAI,cAAc,KAAK,CAAC,OAAO,KAAK,WAAW;YAC3C,QAAQ,OAAO,GAAG,cAAc,KAAK,CAAC,OAAO;QACjD;QACA,OAAO;IACX;IACA,MAAM,UAAU,CAAC;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;QACtC,MAAM,OAAO,sLAAA,CAAA,eAAY,CAAC,EAAE;QAC5B,MAAM,OAAO,cAAc,KAAK,CAAC,KAAK;QACtC,IAAI,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,SAAS,OAAO;YACxC,OAAO,CAAC,KAAK,GAAG;QACpB;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 852, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/utils/animation-state.mjs"], "sourcesContent": ["import { animateVisualElement } from '../../animation/interfaces/visual-element.mjs';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { shallowCompare } from '../../utils/shallow-compare.mjs';\nimport { getVariantContext } from './get-variant-context.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\nimport { variantPriorityOrder } from './variant-props.mjs';\n\nconst reversePriorityOrder = [...variantPriorityOrder].reverse();\nconst numAnimationTypes = variantPriorityOrder.length;\nfunction animateList(visualElement) {\n    return (animations) => Promise.all(animations.map(({ animation, options }) => animateVisualElement(visualElement, animation, options)));\n}\nfunction createAnimationState(visualElement) {\n    let animate = animateList(visualElement);\n    let state = createState();\n    let isInitialRender = true;\n    /**\n     * This function will be used to reduce the animation definitions for\n     * each active animation type into an object of resolved values for it.\n     */\n    const buildResolvedTypeValues = (type) => (acc, definition) => {\n        const resolved = resolveVariant(visualElement, definition, type === \"exit\"\n            ? visualElement.presenceContext?.custom\n            : undefined);\n        if (resolved) {\n            const { transition, transitionEnd, ...target } = resolved;\n            acc = { ...acc, ...target, ...transitionEnd };\n        }\n        return acc;\n    };\n    /**\n     * This just allows us to inject mocked animation functions\n     * @internal\n     */\n    function setAnimateFunction(makeAnimator) {\n        animate = makeAnimator(visualElement);\n    }\n    /**\n     * When we receive new props, we need to:\n     * 1. Create a list of protected keys for each type. This is a directory of\n     *    value keys that are currently being \"handled\" by types of a higher priority\n     *    so that whenever an animation is played of a given type, these values are\n     *    protected from being animated.\n     * 2. Determine if an animation type needs animating.\n     * 3. Determine if any values have been removed from a type and figure out\n     *    what to animate those to.\n     */\n    function animateChanges(changedActiveType) {\n        const { props } = visualElement;\n        const context = getVariantContext(visualElement.parent) || {};\n        /**\n         * A list of animations that we'll build into as we iterate through the animation\n         * types. This will get executed at the end of the function.\n         */\n        const animations = [];\n        /**\n         * Keep track of which values have been removed. Then, as we hit lower priority\n         * animation types, we can check if they contain removed values and animate to that.\n         */\n        const removedKeys = new Set();\n        /**\n         * A dictionary of all encountered keys. This is an object to let us build into and\n         * copy it without iteration. Each time we hit an animation type we set its protected\n         * keys - the keys its not allowed to animate - to the latest version of this object.\n         */\n        let encounteredKeys = {};\n        /**\n         * If a variant has been removed at a given index, and this component is controlling\n         * variant animations, we want to ensure lower-priority variants are forced to animate.\n         */\n        let removedVariantIndex = Infinity;\n        /**\n         * Iterate through all animation types in reverse priority order. For each, we want to\n         * detect which values it's handling and whether or not they've changed (and therefore\n         * need to be animated). If any values have been removed, we want to detect those in\n         * lower priority props and flag for animation.\n         */\n        for (let i = 0; i < numAnimationTypes; i++) {\n            const type = reversePriorityOrder[i];\n            const typeState = state[type];\n            const prop = props[type] !== undefined\n                ? props[type]\n                : context[type];\n            const propIsVariant = isVariantLabel(prop);\n            /**\n             * If this type has *just* changed isActive status, set activeDelta\n             * to that status. Otherwise set to null.\n             */\n            const activeDelta = type === changedActiveType ? typeState.isActive : null;\n            if (activeDelta === false)\n                removedVariantIndex = i;\n            /**\n             * If this prop is an inherited variant, rather than been set directly on the\n             * component itself, we want to make sure we allow the parent to trigger animations.\n             *\n             * TODO: Can probably change this to a !isControllingVariants check\n             */\n            let isInherited = prop === context[type] &&\n                prop !== props[type] &&\n                propIsVariant;\n            /**\n             *\n             */\n            if (isInherited &&\n                isInitialRender &&\n                visualElement.manuallyAnimateOnMount) {\n                isInherited = false;\n            }\n            /**\n             * Set all encountered keys so far as the protected keys for this type. This will\n             * be any key that has been animated or otherwise handled by active, higher-priortiy types.\n             */\n            typeState.protectedKeys = { ...encounteredKeys };\n            // Check if we can skip analysing this prop early\n            if (\n            // If it isn't active and hasn't *just* been set as inactive\n            (!typeState.isActive && activeDelta === null) ||\n                // If we didn't and don't have any defined prop for this animation type\n                (!prop && !typeState.prevProp) ||\n                // Or if the prop doesn't define an animation\n                isAnimationControls(prop) ||\n                typeof prop === \"boolean\") {\n                continue;\n            }\n            /**\n             * As we go look through the values defined on this type, if we detect\n             * a changed value or a value that was removed in a higher priority, we set\n             * this to true and add this prop to the animation list.\n             */\n            const variantDidChange = checkVariantsDidChange(typeState.prevProp, prop);\n            let shouldAnimateType = variantDidChange ||\n                // If we're making this variant active, we want to always make it active\n                (type === changedActiveType &&\n                    typeState.isActive &&\n                    !isInherited &&\n                    propIsVariant) ||\n                // If we removed a higher-priority variant (i is in reverse order)\n                (i > removedVariantIndex && propIsVariant);\n            let handledRemovedValues = false;\n            /**\n             * As animations can be set as variant lists, variants or target objects, we\n             * coerce everything to an array if it isn't one already\n             */\n            const definitionList = Array.isArray(prop) ? prop : [prop];\n            /**\n             * Build an object of all the resolved values. We'll use this in the subsequent\n             * animateChanges calls to determine whether a value has changed.\n             */\n            let resolvedValues = definitionList.reduce(buildResolvedTypeValues(type), {});\n            if (activeDelta === false)\n                resolvedValues = {};\n            /**\n             * Now we need to loop through all the keys in the prev prop and this prop,\n             * and decide:\n             * 1. If the value has changed, and needs animating\n             * 2. If it has been removed, and needs adding to the removedKeys set\n             * 3. If it has been removed in a higher priority type and needs animating\n             * 4. If it hasn't been removed in a higher priority but hasn't changed, and\n             *    needs adding to the type's protectedKeys list.\n             */\n            const { prevResolvedValues = {} } = typeState;\n            const allKeys = {\n                ...prevResolvedValues,\n                ...resolvedValues,\n            };\n            const markToAnimate = (key) => {\n                shouldAnimateType = true;\n                if (removedKeys.has(key)) {\n                    handledRemovedValues = true;\n                    removedKeys.delete(key);\n                }\n                typeState.needsAnimating[key] = true;\n                const motionValue = visualElement.getValue(key);\n                if (motionValue)\n                    motionValue.liveStyle = false;\n            };\n            for (const key in allKeys) {\n                const next = resolvedValues[key];\n                const prev = prevResolvedValues[key];\n                // If we've already handled this we can just skip ahead\n                if (encounteredKeys.hasOwnProperty(key))\n                    continue;\n                /**\n                 * If the value has changed, we probably want to animate it.\n                 */\n                let valueHasChanged = false;\n                if (isKeyframesTarget(next) && isKeyframesTarget(prev)) {\n                    valueHasChanged = !shallowCompare(next, prev);\n                }\n                else {\n                    valueHasChanged = next !== prev;\n                }\n                if (valueHasChanged) {\n                    if (next !== undefined && next !== null) {\n                        // If next is defined and doesn't equal prev, it needs animating\n                        markToAnimate(key);\n                    }\n                    else {\n                        // If it's undefined, it's been removed.\n                        removedKeys.add(key);\n                    }\n                }\n                else if (next !== undefined && removedKeys.has(key)) {\n                    /**\n                     * If next hasn't changed and it isn't undefined, we want to check if it's\n                     * been removed by a higher priority\n                     */\n                    markToAnimate(key);\n                }\n                else {\n                    /**\n                     * If it hasn't changed, we add it to the list of protected values\n                     * to ensure it doesn't get animated.\n                     */\n                    typeState.protectedKeys[key] = true;\n                }\n            }\n            /**\n             * Update the typeState so next time animateChanges is called we can compare the\n             * latest prop and resolvedValues to these.\n             */\n            typeState.prevProp = prop;\n            typeState.prevResolvedValues = resolvedValues;\n            /**\n             *\n             */\n            if (typeState.isActive) {\n                encounteredKeys = { ...encounteredKeys, ...resolvedValues };\n            }\n            if (isInitialRender && visualElement.blockInitialAnimation) {\n                shouldAnimateType = false;\n            }\n            /**\n             * If this is an inherited prop we want to skip this animation\n             * unless the inherited variants haven't changed on this render.\n             */\n            const willAnimateViaParent = isInherited && variantDidChange;\n            const needsAnimating = !willAnimateViaParent || handledRemovedValues;\n            if (shouldAnimateType && needsAnimating) {\n                animations.push(...definitionList.map((animation) => ({\n                    animation: animation,\n                    options: { type },\n                })));\n            }\n        }\n        /**\n         * If there are some removed value that haven't been dealt with,\n         * we need to create a new animation that falls back either to the value\n         * defined in the style prop, or the last read value.\n         */\n        if (removedKeys.size) {\n            const fallbackAnimation = {};\n            /**\n             * If the initial prop contains a transition we can use that, otherwise\n             * allow the animation function to use the visual element's default.\n             */\n            if (typeof props.initial !== \"boolean\") {\n                const initialTransition = resolveVariant(visualElement, Array.isArray(props.initial)\n                    ? props.initial[0]\n                    : props.initial);\n                if (initialTransition && initialTransition.transition) {\n                    fallbackAnimation.transition = initialTransition.transition;\n                }\n            }\n            removedKeys.forEach((key) => {\n                const fallbackTarget = visualElement.getBaseTarget(key);\n                const motionValue = visualElement.getValue(key);\n                if (motionValue)\n                    motionValue.liveStyle = true;\n                // @ts-expect-error - @mattgperry to figure if we should do something here\n                fallbackAnimation[key] = fallbackTarget ?? null;\n            });\n            animations.push({ animation: fallbackAnimation });\n        }\n        let shouldAnimate = Boolean(animations.length);\n        if (isInitialRender &&\n            (props.initial === false || props.initial === props.animate) &&\n            !visualElement.manuallyAnimateOnMount) {\n            shouldAnimate = false;\n        }\n        isInitialRender = false;\n        return shouldAnimate ? animate(animations) : Promise.resolve();\n    }\n    /**\n     * Change whether a certain animation type is active.\n     */\n    function setActive(type, isActive) {\n        // If the active state hasn't changed, we can safely do nothing here\n        if (state[type].isActive === isActive)\n            return Promise.resolve();\n        // Propagate active change to children\n        visualElement.variantChildren?.forEach((child) => child.animationState?.setActive(type, isActive));\n        state[type].isActive = isActive;\n        const animations = animateChanges(type);\n        for (const key in state) {\n            state[key].protectedKeys = {};\n        }\n        return animations;\n    }\n    return {\n        animateChanges,\n        setActive,\n        setAnimateFunction,\n        getState: () => state,\n        reset: () => {\n            state = createState();\n            isInitialRender = true;\n        },\n    };\n}\nfunction checkVariantsDidChange(prev, next) {\n    if (typeof next === \"string\") {\n        return next !== prev;\n    }\n    else if (Array.isArray(next)) {\n        return !shallowCompare(next, prev);\n    }\n    return false;\n}\nfunction createTypeState(isActive = false) {\n    return {\n        isActive,\n        protectedKeys: {},\n        needsAnimating: {},\n        prevResolvedValues: {},\n    };\n}\nfunction createState() {\n    return {\n        animate: createTypeState(true),\n        whileInView: createTypeState(),\n        whileHover: createTypeState(),\n        whileTap: createTypeState(),\n        whileDrag: createTypeState(),\n        whileFocus: createTypeState(),\n        exit: createTypeState(),\n    };\n}\n\nexport { checkVariantsDidChange, createAnimationState };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,uBAAuB;OAAI,sLAAA,CAAA,uBAAoB;CAAC,CAAC,OAAO;AAC9D,MAAM,oBAAoB,sLAAA,CAAA,uBAAoB,CAAC,MAAM;AACrD,SAAS,YAAY,aAAa;IAC9B,OAAO,CAAC,aAAe,QAAQ,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAK,CAAA,GAAA,+LAAA,CAAA,uBAAoB,AAAD,EAAE,eAAe,WAAW;AACjI;AACA,SAAS,qBAAqB,aAAa;IACvC,IAAI,UAAU,YAAY;IAC1B,IAAI,QAAQ;IACZ,IAAI,kBAAkB;IACtB;;;KAGC,GACD,MAAM,0BAA0B,CAAC,OAAS,CAAC,KAAK;YAC5C,MAAM,WAAW,CAAA,GAAA,oMAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,YAAY,SAAS,SAC9D,cAAc,eAAe,EAAE,SAC/B;YACN,IAAI,UAAU;gBACV,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,QAAQ,GAAG;gBACjD,MAAM;oBAAE,GAAG,GAAG;oBAAE,GAAG,MAAM;oBAAE,GAAG,aAAa;gBAAC;YAChD;YACA,OAAO;QACX;IACA;;;KAGC,GACD,SAAS,mBAAmB,YAAY;QACpC,UAAU,aAAa;IAC3B;IACA;;;;;;;;;KASC,GACD,SAAS,eAAe,iBAAiB;QACrC,MAAM,EAAE,KAAK,EAAE,GAAG;QAClB,MAAM,UAAU,CAAA,GAAA,+LAAA,CAAA,oBAAiB,AAAD,EAAE,cAAc,MAAM,KAAK,CAAC;QAC5D;;;SAGC,GACD,MAAM,aAAa,EAAE;QACrB;;;SAGC,GACD,MAAM,cAAc,IAAI;QACxB;;;;SAIC,GACD,IAAI,kBAAkB,CAAC;QACvB;;;SAGC,GACD,IAAI,sBAAsB;QAC1B;;;;;SAKC,GACD,IAAK,IAAI,IAAI,GAAG,IAAI,mBAAmB,IAAK;YACxC,MAAM,OAAO,oBAAoB,CAAC,EAAE;YACpC,MAAM,YAAY,KAAK,CAAC,KAAK;YAC7B,MAAM,OAAO,KAAK,CAAC,KAAK,KAAK,YACvB,KAAK,CAAC,KAAK,GACX,OAAO,CAAC,KAAK;YACnB,MAAM,gBAAgB,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE;YACrC;;;aAGC,GACD,MAAM,cAAc,SAAS,oBAAoB,UAAU,QAAQ,GAAG;YACtE,IAAI,gBAAgB,OAChB,sBAAsB;YAC1B;;;;;aAKC,GACD,IAAI,cAAc,SAAS,OAAO,CAAC,KAAK,IACpC,SAAS,KAAK,CAAC,KAAK,IACpB;YACJ;;aAEC,GACD,IAAI,eACA,mBACA,cAAc,sBAAsB,EAAE;gBACtC,cAAc;YAClB;YACA;;;aAGC,GACD,UAAU,aAAa,GAAG;gBAAE,GAAG,eAAe;YAAC;YAC/C,iDAAiD;YACjD,IACA,4DAA4D;YAC3D,CAAC,UAAU,QAAQ,IAAI,gBAAgB,QAEnC,CAAC,QAAQ,CAAC,UAAU,QAAQ,IAC7B,6CAA6C;YAC7C,CAAA,GAAA,oMAAA,CAAA,sBAAmB,AAAD,EAAE,SACpB,OAAO,SAAS,WAAW;gBAC3B;YACJ;YACA;;;;aAIC,GACD,MAAM,mBAAmB,uBAAuB,UAAU,QAAQ,EAAE;YACpE,IAAI,oBAAoB,oBAEnB,SAAS,qBACN,UAAU,QAAQ,IAClB,CAAC,eACD,iBAEH,IAAI,uBAAuB;YAChC,IAAI,uBAAuB;YAC3B;;;aAGC,GACD,MAAM,iBAAiB,MAAM,OAAO,CAAC,QAAQ,OAAO;gBAAC;aAAK;YAC1D;;;aAGC,GACD,IAAI,iBAAiB,eAAe,MAAM,CAAC,wBAAwB,OAAO,CAAC;YAC3E,IAAI,gBAAgB,OAChB,iBAAiB,CAAC;YACtB;;;;;;;;aAQC,GACD,MAAM,EAAE,qBAAqB,CAAC,CAAC,EAAE,GAAG;YACpC,MAAM,UAAU;gBACZ,GAAG,kBAAkB;gBACrB,GAAG,cAAc;YACrB;YACA,MAAM,gBAAgB,CAAC;gBACnB,oBAAoB;gBACpB,IAAI,YAAY,GAAG,CAAC,MAAM;oBACtB,uBAAuB;oBACvB,YAAY,MAAM,CAAC;gBACvB;gBACA,UAAU,cAAc,CAAC,IAAI,GAAG;gBAChC,MAAM,cAAc,cAAc,QAAQ,CAAC;gBAC3C,IAAI,aACA,YAAY,SAAS,GAAG;YAChC;YACA,IAAK,MAAM,OAAO,QAAS;gBACvB,MAAM,OAAO,cAAc,CAAC,IAAI;gBAChC,MAAM,OAAO,kBAAkB,CAAC,IAAI;gBACpC,uDAAuD;gBACvD,IAAI,gBAAgB,cAAc,CAAC,MAC/B;gBACJ;;iBAEC,GACD,IAAI,kBAAkB;gBACtB,IAAI,CAAA,GAAA,kMAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,CAAA,GAAA,kMAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;oBACpD,kBAAkB,CAAC,CAAA,GAAA,8KAAA,CAAA,iBAAc,AAAD,EAAE,MAAM;gBAC5C,OACK;oBACD,kBAAkB,SAAS;gBAC/B;gBACA,IAAI,iBAAiB;oBACjB,IAAI,SAAS,aAAa,SAAS,MAAM;wBACrC,gEAAgE;wBAChE,cAAc;oBAClB,OACK;wBACD,wCAAwC;wBACxC,YAAY,GAAG,CAAC;oBACpB;gBACJ,OACK,IAAI,SAAS,aAAa,YAAY,GAAG,CAAC,MAAM;oBACjD;;;qBAGC,GACD,cAAc;gBAClB,OACK;oBACD;;;qBAGC,GACD,UAAU,aAAa,CAAC,IAAI,GAAG;gBACnC;YACJ;YACA;;;aAGC,GACD,UAAU,QAAQ,GAAG;YACrB,UAAU,kBAAkB,GAAG;YAC/B;;aAEC,GACD,IAAI,UAAU,QAAQ,EAAE;gBACpB,kBAAkB;oBAAE,GAAG,eAAe;oBAAE,GAAG,cAAc;gBAAC;YAC9D;YACA,IAAI,mBAAmB,cAAc,qBAAqB,EAAE;gBACxD,oBAAoB;YACxB;YACA;;;aAGC,GACD,MAAM,uBAAuB,eAAe;YAC5C,MAAM,iBAAiB,CAAC,wBAAwB;YAChD,IAAI,qBAAqB,gBAAgB;gBACrC,WAAW,IAAI,IAAI,eAAe,GAAG,CAAC,CAAC,YAAc,CAAC;wBAClD,WAAW;wBACX,SAAS;4BAAE;wBAAK;oBACpB,CAAC;YACL;QACJ;QACA;;;;SAIC,GACD,IAAI,YAAY,IAAI,EAAE;YAClB,MAAM,oBAAoB,CAAC;YAC3B;;;aAGC,GACD,IAAI,OAAO,MAAM,OAAO,KAAK,WAAW;gBACpC,MAAM,oBAAoB,CAAA,GAAA,oMAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,MAAM,OAAO,CAAC,MAAM,OAAO,IAC7E,MAAM,OAAO,CAAC,EAAE,GAChB,MAAM,OAAO;gBACnB,IAAI,qBAAqB,kBAAkB,UAAU,EAAE;oBACnD,kBAAkB,UAAU,GAAG,kBAAkB,UAAU;gBAC/D;YACJ;YACA,YAAY,OAAO,CAAC,CAAC;gBACjB,MAAM,iBAAiB,cAAc,aAAa,CAAC;gBACnD,MAAM,cAAc,cAAc,QAAQ,CAAC;gBAC3C,IAAI,aACA,YAAY,SAAS,GAAG;gBAC5B,0EAA0E;gBAC1E,iBAAiB,CAAC,IAAI,GAAG,kBAAkB;YAC/C;YACA,WAAW,IAAI,CAAC;gBAAE,WAAW;YAAkB;QACnD;QACA,IAAI,gBAAgB,QAAQ,WAAW,MAAM;QAC7C,IAAI,mBACA,CAAC,MAAM,OAAO,KAAK,SAAS,MAAM,OAAO,KAAK,MAAM,OAAO,KAC3D,CAAC,cAAc,sBAAsB,EAAE;YACvC,gBAAgB;QACpB;QACA,kBAAkB;QAClB,OAAO,gBAAgB,QAAQ,cAAc,QAAQ,OAAO;IAChE;IACA;;KAEC,GACD,SAAS,UAAU,IAAI,EAAE,QAAQ;QAC7B,oEAAoE;QACpE,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,KAAK,UACzB,OAAO,QAAQ,OAAO;QAC1B,sCAAsC;QACtC,cAAc,eAAe,EAAE,QAAQ,CAAC,QAAU,MAAM,cAAc,EAAE,UAAU,MAAM;QACxF,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG;QACvB,MAAM,aAAa,eAAe;QAClC,IAAK,MAAM,OAAO,MAAO;YACrB,KAAK,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC;QAChC;QACA,OAAO;IACX;IACA,OAAO;QACH;QACA;QACA;QACA,UAAU,IAAM;QAChB,OAAO;YACH,QAAQ;YACR,kBAAkB;QACtB;IACJ;AACJ;AACA,SAAS,uBAAuB,IAAI,EAAE,IAAI;IACtC,IAAI,OAAO,SAAS,UAAU;QAC1B,OAAO,SAAS;IACpB,OACK,IAAI,MAAM,OAAO,CAAC,OAAO;QAC1B,OAAO,CAAC,CAAA,GAAA,8KAAA,CAAA,iBAAc,AAAD,EAAE,MAAM;IACjC;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,WAAW,KAAK;IACrC,OAAO;QACH;QACA,eAAe,CAAC;QAChB,gBAAgB,CAAC;QACjB,oBAAoB,CAAC;IACzB;AACJ;AACA,SAAS;IACL,OAAO;QACH,SAAS,gBAAgB;QACzB,aAAa;QACb,YAAY;QACZ,UAAU;QACV,WAAW;QACX,YAAY;QACZ,MAAM;IACV;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/features/Feature.mjs"], "sourcesContent": ["class Feature {\n    constructor(node) {\n        this.isMounted = false;\n        this.node = node;\n    }\n    update() { }\n}\n\nexport { Feature };\n"], "names": [], "mappings": ";;;AAAA,MAAM;IACF,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,SAAS,CAAE;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1184, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/features/animation/index.mjs"], "sourcesContent": ["import { isAnimationControls } from '../../../animation/utils/is-animation-controls.mjs';\nimport { createAnimationState } from '../../../render/utils/animation-state.mjs';\nimport { Feature } from '../Feature.mjs';\n\nclass AnimationFeature extends Feature {\n    /**\n     * We dynamically generate the AnimationState manager as it contains a reference\n     * to the underlying animation library. We only want to load that if we load this,\n     * so people can optionally code split it out using the `m` component.\n     */\n    constructor(node) {\n        super(node);\n        node.animationState || (node.animationState = createAnimationState(node));\n    }\n    updateAnimationControlsSubscription() {\n        const { animate } = this.node.getProps();\n        if (isAnimationControls(animate)) {\n            this.unmountControls = animate.subscribe(this.node);\n        }\n    }\n    /**\n     * Subscribe any provided AnimationControls to the component's VisualElement\n     */\n    mount() {\n        this.updateAnimationControlsSubscription();\n    }\n    update() {\n        const { animate } = this.node.getProps();\n        const { animate: prevAnimate } = this.node.prevProps || {};\n        if (animate !== prevAnimate) {\n            this.updateAnimationControlsSubscription();\n        }\n    }\n    unmount() {\n        this.node.animationState.reset();\n        this.unmountControls?.();\n    }\n}\n\nexport { AnimationFeature };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,yBAAyB,gLAAA,CAAA,UAAO;IAClC;;;;KAIC,GACD,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,KAAK,cAAc,IAAI,CAAC,KAAK,cAAc,GAAG,CAAA,GAAA,wLAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK;IAC5E;IACA,sCAAsC;QAClC,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtC,IAAI,CAAA,GAAA,oMAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU;YAC9B,IAAI,CAAC,eAAe,GAAG,QAAQ,SAAS,CAAC,IAAI,CAAC,IAAI;QACtD;IACJ;IACA;;KAEC,GACD,QAAQ;QACJ,IAAI,CAAC,mCAAmC;IAC5C;IACA,SAAS;QACL,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QACtC,MAAM,EAAE,SAAS,WAAW,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC;QACzD,IAAI,YAAY,aAAa;YACzB,IAAI,CAAC,mCAAmC;QAC5C;IACJ;IACA,UAAU;QACN,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK;QAC9B,IAAI,CAAC,eAAe;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs"], "sourcesContent": ["import { Feature } from '../Feature.mjs';\n\nlet id = 0;\nclass ExitAnimationFeature extends Feature {\n    constructor() {\n        super(...arguments);\n        this.id = id++;\n    }\n    update() {\n        if (!this.node.presenceContext)\n            return;\n        const { isPresent, onExitComplete } = this.node.presenceContext;\n        const { isPresent: prevIsPresent } = this.node.prevPresenceContext || {};\n        if (!this.node.animationState || isPresent === prevIsPresent) {\n            return;\n        }\n        const exitAnimation = this.node.animationState.setActive(\"exit\", !isPresent);\n        if (onExitComplete && !isPresent) {\n            exitAnimation.then(() => {\n                onExitComplete(this.id);\n            });\n        }\n    }\n    mount() {\n        const { register, onExitComplete } = this.node.presenceContext || {};\n        if (onExitComplete) {\n            onExitComplete(this.id);\n        }\n        if (register) {\n            this.unmount = register(this.id);\n        }\n    }\n    unmount() { }\n}\n\nexport { ExitAnimationFeature };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,IAAI,KAAK;AACT,MAAM,6BAA6B,gLAAA,CAAA,UAAO;IACtC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,EAAE,GAAG;IACd;IACA,SAAS;QACL,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAC1B;QACJ,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe;QAC/D,MAAM,EAAE,WAAW,aAAa,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,CAAC;QACvE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,cAAc,eAAe;YAC1D;QACJ;QACA,MAAM,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClE,IAAI,kBAAkB,CAAC,WAAW;YAC9B,cAAc,IAAI,CAAC;gBACf,eAAe,IAAI,CAAC,EAAE;YAC1B;QACJ;IACJ;IACA,QAAQ;QACJ,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC;QACnE,IAAI,gBAAgB;YAChB,eAAe,IAAI,CAAC,EAAE;QAC1B;QACA,IAAI,UAAU;YACV,IAAI,CAAC,OAAO,GAAG,SAAS,IAAI,CAAC,EAAE;QACnC;IACJ;IACA,UAAU,CAAE;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1275, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/features/animations.mjs"], "sourcesContent": ["import { AnimationFeature } from './animation/index.mjs';\nimport { ExitAnimationFeature } from './animation/exit.mjs';\n\nconst animations = {\n    animation: {\n        Feature: AnimationFeature,\n    },\n    exit: {\n        Feature: ExitAnimationFeature,\n    },\n};\n\nexport { animations };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,aAAa;IACf,WAAW;QACP,SAAS,2LAAA,CAAA,mBAAgB;IAC7B;IACA,MAAM;QACF,SAAS,0LAAA,CAAA,uBAAoB;IACjC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/events/add-dom-event.mjs"], "sourcesContent": ["function addDomEvent(target, eventName, handler, options = { passive: true }) {\n    target.addEventListener(eventName, handler, options);\n    return () => target.removeEventListener(eventName, handler);\n}\n\nexport { addDomEvent };\n"], "names": [], "mappings": ";;;AAAA,SAAS,YAAY,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU;IAAE,SAAS;AAAK,CAAC;IACxE,OAAO,gBAAgB,CAAC,WAAW,SAAS;IAC5C,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1313, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/events/event-info.mjs"], "sourcesContent": ["import { isPrimaryPointer } from 'motion-dom';\n\nfunction extractEventInfo(event) {\n    return {\n        point: {\n            x: event.pageX,\n            y: event.pageY,\n        },\n    };\n}\nconst addPointerInfo = (handler) => {\n    return (event) => isPrimaryPointer(event) && handler(event, extractEventInfo(event));\n};\n\nexport { addPointerInfo, extractEventInfo };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS,iBAAiB,KAAK;IAC3B,OAAO;QACH,OAAO;YACH,GAAG,MAAM,KAAK;YACd,GAAG,MAAM,KAAK;QAClB;IACJ;AACJ;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,CAAC,QAAU,CAAA,GAAA,6LAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,QAAQ,OAAO,iBAAiB;AACjF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1337, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs"], "sourcesContent": ["import { addDomEvent } from './add-dom-event.mjs';\nimport { addPointerInfo } from './event-info.mjs';\n\nfunction addPointerEvent(target, eventName, handler, options) {\n    return addDomEvent(target, eventName, addPointerInfo(handler), options);\n}\n\nexport { addPointerEvent };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,gBAAgB,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO;IACxD,OAAO,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,WAAW,CAAA,GAAA,0KAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;AACnE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1354, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs"], "sourcesContent": ["/**\n * Bounding boxes tend to be defined as top, left, right, bottom. For various operations\n * it's easier to consider each axis individually. This function returns a bounding box\n * as a map of single-axis min/max values.\n */\nfunction convertBoundingBoxToBox({ top, left, right, bottom, }) {\n    return {\n        x: { min: left, max: right },\n        y: { min: top, max: bottom },\n    };\n}\nfunction convertBoxToBoundingBox({ x, y }) {\n    return { top: y.min, right: x.max, bottom: y.max, left: x.min };\n}\n/**\n * Applies a TransformPoint function to a bounding box. TransformPoint is usually a function\n * provided by Framer to allow measured points to be corrected for device scaling. This is used\n * when measuring DOM elements and DOM event points.\n */\nfunction transformBoxPoints(point, transformPoint) {\n    if (!transformPoint)\n        return point;\n    const topLeft = transformPoint({ x: point.left, y: point.top });\n    const bottomRight = transformPoint({ x: point.right, y: point.bottom });\n    return {\n        top: topLeft.y,\n        left: topLeft.x,\n        bottom: bottomRight.y,\n        right: bottomRight.x,\n    };\n}\n\nexport { convertBoundingBoxToBox, convertBoxToBoundingBox, transformBoxPoints };\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;AACD,SAAS,wBAAwB,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAG;IAC1D,OAAO;QACH,GAAG;YAAE,KAAK;YAAM,KAAK;QAAM;QAC3B,GAAG;YAAE,KAAK;YAAK,KAAK;QAAO;IAC/B;AACJ;AACA,SAAS,wBAAwB,EAAE,CAAC,EAAE,CAAC,EAAE;IACrC,OAAO;QAAE,KAAK,EAAE,GAAG;QAAE,OAAO,EAAE,GAAG;QAAE,QAAQ,EAAE,GAAG;QAAE,MAAM,EAAE,GAAG;IAAC;AAClE;AACA;;;;CAIC,GACD,SAAS,mBAAmB,KAAK,EAAE,cAAc;IAC7C,IAAI,CAAC,gBACD,OAAO;IACX,MAAM,UAAU,eAAe;QAAE,GAAG,MAAM,IAAI;QAAE,GAAG,MAAM,GAAG;IAAC;IAC7D,MAAM,cAAc,eAAe;QAAE,GAAG,MAAM,KAAK;QAAE,GAAG,MAAM,MAAM;IAAC;IACrE,OAAO;QACH,KAAK,QAAQ,CAAC;QACd,MAAM,QAAQ,CAAC;QACf,QAAQ,YAAY,CAAC;QACrB,OAAO,YAAY,CAAC;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs"], "sourcesContent": ["import { mixNumber } from 'motion-dom';\n\nconst SCALE_PRECISION = 0.0001;\nconst SCALE_MIN = 1 - SCALE_PRECISION;\nconst SCALE_MAX = 1 + SCALE_PRECISION;\nconst TRANSLATE_PRECISION = 0.01;\nconst TRANSLATE_MIN = 0 - TRANSLATE_PRECISION;\nconst TRANSLATE_MAX = 0 + TRANSLATE_PRECISION;\nfunction calcLength(axis) {\n    return axis.max - axis.min;\n}\nfunction isNear(value, target, maxDistance) {\n    return Math.abs(value - target) <= maxDistance;\n}\nfunction calcAxisDelta(delta, source, target, origin = 0.5) {\n    delta.origin = origin;\n    delta.originPoint = mixNumber(source.min, source.max, delta.origin);\n    delta.scale = calcLength(target) / calcLength(source);\n    delta.translate =\n        mixNumber(target.min, target.max, delta.origin) - delta.originPoint;\n    if ((delta.scale >= SCALE_MIN && delta.scale <= SCALE_MAX) ||\n        isNaN(delta.scale)) {\n        delta.scale = 1.0;\n    }\n    if ((delta.translate >= TRANSLATE_MIN &&\n        delta.translate <= TRANSLATE_MAX) ||\n        isNaN(delta.translate)) {\n        delta.translate = 0.0;\n    }\n}\nfunction calcBoxDelta(delta, source, target, origin) {\n    calcAxisDelta(delta.x, source.x, target.x, origin ? origin.originX : undefined);\n    calcAxisDelta(delta.y, source.y, target.y, origin ? origin.originY : undefined);\n}\nfunction calcRelativeAxis(target, relative, parent) {\n    target.min = parent.min + relative.min;\n    target.max = target.min + calcLength(relative);\n}\nfunction calcRelativeBox(target, relative, parent) {\n    calcRelativeAxis(target.x, relative.x, parent.x);\n    calcRelativeAxis(target.y, relative.y, parent.y);\n}\nfunction calcRelativeAxisPosition(target, layout, parent) {\n    target.min = layout.min - parent.min;\n    target.max = target.min + calcLength(layout);\n}\nfunction calcRelativePosition(target, layout, parent) {\n    calcRelativeAxisPosition(target.x, layout.x, parent.x);\n    calcRelativeAxisPosition(target.y, layout.y, parent.y);\n}\n\nexport { calcAxisDelta, calcBoxDelta, calcLength, calcRelativeAxis, calcRelativeAxisPosition, calcRelativeBox, calcRelativePosition, isNear };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAEA,MAAM,kBAAkB;AACxB,MAAM,YAAY,IAAI;AACtB,MAAM,YAAY,IAAI;AACtB,MAAM,sBAAsB;AAC5B,MAAM,gBAAgB,IAAI;AAC1B,MAAM,gBAAgB,IAAI;AAC1B,SAAS,WAAW,IAAI;IACpB,OAAO,KAAK,GAAG,GAAG,KAAK,GAAG;AAC9B;AACA,SAAS,OAAO,KAAK,EAAE,MAAM,EAAE,WAAW;IACtC,OAAO,KAAK,GAAG,CAAC,QAAQ,WAAW;AACvC;AACA,SAAS,cAAc,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,GAAG;IACtD,MAAM,MAAM,GAAG;IACf,MAAM,WAAW,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,OAAO,GAAG,EAAE,OAAO,GAAG,EAAE,MAAM,MAAM;IAClE,MAAM,KAAK,GAAG,WAAW,UAAU,WAAW;IAC9C,MAAM,SAAS,GACX,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,OAAO,GAAG,EAAE,OAAO,GAAG,EAAE,MAAM,MAAM,IAAI,MAAM,WAAW;IACvE,IAAI,AAAC,MAAM,KAAK,IAAI,aAAa,MAAM,KAAK,IAAI,aAC5C,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,KAAK,GAAG;IAClB;IACA,IAAI,AAAC,MAAM,SAAS,IAAI,iBACpB,MAAM,SAAS,IAAI,iBACnB,MAAM,MAAM,SAAS,GAAG;QACxB,MAAM,SAAS,GAAG;IACtB;AACJ;AACA,SAAS,aAAa,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAC/C,cAAc,MAAM,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,OAAO,OAAO,GAAG;IACrE,cAAc,MAAM,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS,OAAO,OAAO,GAAG;AACzE;AACA,SAAS,iBAAiB,MAAM,EAAE,QAAQ,EAAE,MAAM;IAC9C,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,SAAS,GAAG;IACtC,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,WAAW;AACzC;AACA,SAAS,gBAAgB,MAAM,EAAE,QAAQ,EAAE,MAAM;IAC7C,iBAAiB,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC;IAC/C,iBAAiB,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC;AACnD;AACA,SAAS,yBAAyB,MAAM,EAAE,MAAM,EAAE,MAAM;IACpD,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,OAAO,GAAG;IACpC,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,WAAW;AACzC;AACA,SAAS,qBAAqB,MAAM,EAAE,MAAM,EAAE,MAAM;IAChD,yBAAyB,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC;IACrD,yBAAyB,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1474, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/geometry/models.mjs"], "sourcesContent": ["const createAxisDelta = () => ({\n    translate: 0,\n    scale: 1,\n    origin: 0,\n    originPoint: 0,\n});\nconst createDelta = () => ({\n    x: createAxisDelta(),\n    y: createAxisDelta(),\n});\nconst createAxis = () => ({ min: 0, max: 0 });\nconst createBox = () => ({\n    x: createAxis(),\n    y: createAxis(),\n});\n\nexport { createAxis, createAxisDelta, createBox, createDelta };\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,kBAAkB,IAAM,CAAC;QAC3B,WAAW;QACX,OAAO;QACP,QAAQ;QACR,aAAa;IACjB,CAAC;AACD,MAAM,cAAc,IAAM,CAAC;QACvB,GAAG;QACH,GAAG;IACP,CAAC;AACD,MAAM,aAAa,IAAM,CAAC;QAAE,KAAK;QAAG,KAAK;IAAE,CAAC;AAC5C,MAAM,YAAY,IAAM,CAAC;QACrB,GAAG;QACH,GAAG;IACP,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1505, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/utils/each-axis.mjs"], "sourcesContent": ["function eachAxis(callback) {\n    return [callback(\"x\"), callback(\"y\")];\n}\n\nexport { eachAxis };\n"], "names": [], "mappings": ";;;AAAA,SAAS,SAAS,QAAQ;IACtB,OAAO;QAAC,SAAS;QAAM,SAAS;KAAK;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1521, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs"], "sourcesContent": ["function isIdentityScale(scale) {\n    return scale === undefined || scale === 1;\n}\nfunction hasScale({ scale, scaleX, scaleY }) {\n    return (!isIdentityScale(scale) ||\n        !isIdentityScale(scaleX) ||\n        !isIdentityScale(scaleY));\n}\nfunction hasTransform(values) {\n    return (hasScale(values) ||\n        has2DTranslate(values) ||\n        values.z ||\n        values.rotate ||\n        values.rotateX ||\n        values.rotateY ||\n        values.skewX ||\n        values.skewY);\n}\nfunction has2DTranslate(values) {\n    return is2DTranslate(values.x) || is2DTranslate(values.y);\n}\nfunction is2DTranslate(value) {\n    return value && value !== \"0%\";\n}\n\nexport { has2DTranslate, hasScale, hasTransform };\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,gBAAgB,KAAK;IAC1B,OAAO,UAAU,aAAa,UAAU;AAC5C;AACA,SAAS,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;IACvC,OAAQ,CAAC,gBAAgB,UACrB,CAAC,gBAAgB,WACjB,CAAC,gBAAgB;AACzB;AACA,SAAS,aAAa,MAAM;IACxB,OAAQ,SAAS,WACb,eAAe,WACf,OAAO,CAAC,IACR,OAAO,MAAM,IACb,OAAO,OAAO,IACd,OAAO,OAAO,IACd,OAAO,KAAK,IACZ,OAAO,KAAK;AACpB;AACA,SAAS,eAAe,MAAM;IAC1B,OAAO,cAAc,OAAO,CAAC,KAAK,cAAc,OAAO,CAAC;AAC5D;AACA,SAAS,cAAc,KAAK;IACxB,OAAO,SAAS,UAAU;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1548, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs"], "sourcesContent": ["import { mixNumber } from 'motion-dom';\nimport { hasTransform } from '../utils/has-transform.mjs';\n\n/**\n * Scales a point based on a factor and an originPoint\n */\nfunction scalePoint(point, scale, originPoint) {\n    const distanceFromOrigin = point - originPoint;\n    const scaled = scale * distanceFromOrigin;\n    return originPoint + scaled;\n}\n/**\n * Applies a translate/scale delta to a point\n */\nfunction applyPointDelta(point, translate, scale, originPoint, boxScale) {\n    if (boxScale !== undefined) {\n        point = scalePoint(point, boxScale, originPoint);\n    }\n    return scalePoint(point, scale, originPoint) + translate;\n}\n/**\n * Applies a translate/scale delta to an axis\n */\nfunction applyAxisDelta(axis, translate = 0, scale = 1, originPoint, boxScale) {\n    axis.min = applyPointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = applyPointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Applies a translate/scale delta to a box\n */\nfunction applyBoxDelta(box, { x, y }) {\n    applyAxisDelta(box.x, x.translate, x.scale, x.originPoint);\n    applyAxisDelta(box.y, y.translate, y.scale, y.originPoint);\n}\nconst TREE_SCALE_SNAP_MIN = 0.999999999999;\nconst TREE_SCALE_SNAP_MAX = 1.0000000000001;\n/**\n * Apply a tree of deltas to a box. We do this to calculate the effect of all the transforms\n * in a tree upon our box before then calculating how to project it into our desired viewport-relative box\n *\n * This is the final nested loop within updateLayoutDelta for future refactoring\n */\nfunction applyTreeDeltas(box, treeScale, treePath, isSharedTransition = false) {\n    const treeLength = treePath.length;\n    if (!treeLength)\n        return;\n    // Reset the treeScale\n    treeScale.x = treeScale.y = 1;\n    let node;\n    let delta;\n    for (let i = 0; i < treeLength; i++) {\n        node = treePath[i];\n        delta = node.projectionDelta;\n        /**\n         * TODO: Prefer to remove this, but currently we have motion components with\n         * display: contents in Framer.\n         */\n        const { visualElement } = node.options;\n        if (visualElement &&\n            visualElement.props.style &&\n            visualElement.props.style.display === \"contents\") {\n            continue;\n        }\n        if (isSharedTransition &&\n            node.options.layoutScroll &&\n            node.scroll &&\n            node !== node.root) {\n            transformBox(box, {\n                x: -node.scroll.offset.x,\n                y: -node.scroll.offset.y,\n            });\n        }\n        if (delta) {\n            // Incoporate each ancestor's scale into a culmulative treeScale for this component\n            treeScale.x *= delta.x.scale;\n            treeScale.y *= delta.y.scale;\n            // Apply each ancestor's calculated delta into this component's recorded layout box\n            applyBoxDelta(box, delta);\n        }\n        if (isSharedTransition && hasTransform(node.latestValues)) {\n            transformBox(box, node.latestValues);\n        }\n    }\n    /**\n     * Snap tree scale back to 1 if it's within a non-perceivable threshold.\n     * This will help reduce useless scales getting rendered.\n     */\n    if (treeScale.x < TREE_SCALE_SNAP_MAX &&\n        treeScale.x > TREE_SCALE_SNAP_MIN) {\n        treeScale.x = 1.0;\n    }\n    if (treeScale.y < TREE_SCALE_SNAP_MAX &&\n        treeScale.y > TREE_SCALE_SNAP_MIN) {\n        treeScale.y = 1.0;\n    }\n}\nfunction translateAxis(axis, distance) {\n    axis.min = axis.min + distance;\n    axis.max = axis.max + distance;\n}\n/**\n * Apply a transform to an axis from the latest resolved motion values.\n * This function basically acts as a bridge between a flat motion value map\n * and applyAxisDelta\n */\nfunction transformAxis(axis, axisTranslate, axisScale, boxScale, axisOrigin = 0.5) {\n    const originPoint = mixNumber(axis.min, axis.max, axisOrigin);\n    // Apply the axis delta to the final axis\n    applyAxisDelta(axis, axisTranslate, axisScale, originPoint, boxScale);\n}\n/**\n * Apply a transform to a box from the latest resolved motion values.\n */\nfunction transformBox(box, transform) {\n    transformAxis(box.x, transform.x, transform.scaleX, transform.scale, transform.originX);\n    transformAxis(box.y, transform.y, transform.scaleY, transform.scale, transform.originY);\n}\n\nexport { applyAxisDelta, applyBoxDelta, applyPointDelta, applyTreeDeltas, scalePoint, transformAxis, transformBox, translateAxis };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEA;;CAEC,GACD,SAAS,WAAW,KAAK,EAAE,KAAK,EAAE,WAAW;IACzC,MAAM,qBAAqB,QAAQ;IACnC,MAAM,SAAS,QAAQ;IACvB,OAAO,cAAc;AACzB;AACA;;CAEC,GACD,SAAS,gBAAgB,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ;IACnE,IAAI,aAAa,WAAW;QACxB,QAAQ,WAAW,OAAO,UAAU;IACxC;IACA,OAAO,WAAW,OAAO,OAAO,eAAe;AACnD;AACA;;CAEC,GACD,SAAS,eAAe,IAAI,EAAE,YAAY,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW,EAAE,QAAQ;IACzE,KAAK,GAAG,GAAG,gBAAgB,KAAK,GAAG,EAAE,WAAW,OAAO,aAAa;IACpE,KAAK,GAAG,GAAG,gBAAgB,KAAK,GAAG,EAAE,WAAW,OAAO,aAAa;AACxE;AACA;;CAEC,GACD,SAAS,cAAc,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;IAChC,eAAe,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,EAAE,WAAW;IACzD,eAAe,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,EAAE,WAAW;AAC7D;AACA,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAC5B;;;;;CAKC,GACD,SAAS,gBAAgB,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,qBAAqB,KAAK;IACzE,MAAM,aAAa,SAAS,MAAM;IAClC,IAAI,CAAC,YACD;IACJ,sBAAsB;IACtB,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG;IAC5B,IAAI;IACJ,IAAI;IACJ,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACjC,OAAO,QAAQ,CAAC,EAAE;QAClB,QAAQ,KAAK,eAAe;QAC5B;;;SAGC,GACD,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,OAAO;QACtC,IAAI,iBACA,cAAc,KAAK,CAAC,KAAK,IACzB,cAAc,KAAK,CAAC,KAAK,CAAC,OAAO,KAAK,YAAY;YAClD;QACJ;QACA,IAAI,sBACA,KAAK,OAAO,CAAC,YAAY,IACzB,KAAK,MAAM,IACX,SAAS,KAAK,IAAI,EAAE;YACpB,aAAa,KAAK;gBACd,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;gBACxB,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;YAC5B;QACJ;QACA,IAAI,OAAO;YACP,mFAAmF;YACnF,UAAU,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK;YAC5B,UAAU,CAAC,IAAI,MAAM,CAAC,CAAC,KAAK;YAC5B,mFAAmF;YACnF,cAAc,KAAK;QACvB;QACA,IAAI,sBAAsB,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,KAAK,YAAY,GAAG;YACvD,aAAa,KAAK,KAAK,YAAY;QACvC;IACJ;IACA;;;KAGC,GACD,IAAI,UAAU,CAAC,GAAG,uBACd,UAAU,CAAC,GAAG,qBAAqB;QACnC,UAAU,CAAC,GAAG;IAClB;IACA,IAAI,UAAU,CAAC,GAAG,uBACd,UAAU,CAAC,GAAG,qBAAqB;QACnC,UAAU,CAAC,GAAG;IAClB;AACJ;AACA,SAAS,cAAc,IAAI,EAAE,QAAQ;IACjC,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG;IACtB,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG;AAC1B;AACA;;;;CAIC,GACD,SAAS,cAAc,IAAI,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,GAAG;IAC7E,MAAM,cAAc,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,EAAE;IAClD,yCAAyC;IACzC,eAAe,MAAM,eAAe,WAAW,aAAa;AAChE;AACA;;CAEC,GACD,SAAS,aAAa,GAAG,EAAE,SAAS;IAChC,cAAc,IAAI,CAAC,EAAE,UAAU,CAAC,EAAE,UAAU,MAAM,EAAE,UAAU,KAAK,EAAE,UAAU,OAAO;IACtF,cAAc,IAAI,CAAC,EAAE,UAAU,CAAC,EAAE,UAAU,MAAM,EAAE,UAAU,KAAK,EAAE,UAAU,OAAO;AAC1F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1666, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/utils/measure.mjs"], "sourcesContent": ["import { convertBoundingBoxToBox, transformBoxPoints } from '../geometry/conversion.mjs';\nimport { translateAxis } from '../geometry/delta-apply.mjs';\n\nfunction measureViewportBox(instance, transformPoint) {\n    return convertBoundingBoxToBox(transformBoxPoints(instance.getBoundingClientRect(), transformPoint));\n}\nfunction measurePageBox(element, rootProjectionNode, transformPagePoint) {\n    const viewportBox = measureViewportBox(element, transformPagePoint);\n    const { scroll } = rootProjectionNode;\n    if (scroll) {\n        translateAxis(viewportBox.x, scroll.offset.x);\n        translateAxis(viewportBox.y, scroll.offset.y);\n    }\n    return viewportBox;\n}\n\nexport { measurePageBox, measureViewportBox };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,SAAS,mBAAmB,QAAQ,EAAE,cAAc;IAChD,OAAO,CAAA,GAAA,uLAAA,CAAA,0BAAuB,AAAD,EAAE,CAAA,GAAA,uLAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,qBAAqB,IAAI;AACxF;AACA,SAAS,eAAe,OAAO,EAAE,kBAAkB,EAAE,kBAAkB;IACnE,MAAM,cAAc,mBAAmB,SAAS;IAChD,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,IAAI,QAAQ;QACR,CAAA,GAAA,2LAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC;QAC5C,CAAA,GAAA,2LAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC;IAChD;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1693, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/utils/get-context-window.mjs"], "sourcesContent": ["// Fixes https://github.com/motiondivision/motion/issues/2270\nconst getContextWindow = ({ current }) => {\n    return current ? current.ownerDocument.defaultView : null;\n};\n\nexport { getContextWindow };\n"], "names": [], "mappings": "AAAA,6DAA6D;;;;AAC7D,MAAM,mBAAmB,CAAC,EAAE,OAAO,EAAE;IACjC,OAAO,UAAU,QAAQ,aAAa,CAAC,WAAW,GAAG;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1707, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/utils/is-ref-object.mjs"], "sourcesContent": ["function isRefObject(ref) {\n    return (ref &&\n        typeof ref === \"object\" &&\n        Object.prototype.hasOwnProperty.call(ref, \"current\"));\n}\n\nexport { isRefObject };\n"], "names": [], "mappings": ";;;AAAA,SAAS,YAAY,GAAG;IACpB,OAAQ,OACJ,OAAO,QAAQ,YACf,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1720, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/utils/distance.mjs"], "sourcesContent": ["const distance = (a, b) => Math.abs(a - b);\nfunction distance2D(a, b) {\n    // Multi-dimensional\n    const xDelta = distance(a.x, b.x);\n    const yDelta = distance(a.y, b.y);\n    return Math.sqrt(xDelta ** 2 + yDelta ** 2);\n}\n\nexport { distance, distance2D };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,WAAW,CAAC,GAAG,IAAM,KAAK,GAAG,CAAC,IAAI;AACxC,SAAS,WAAW,CAAC,EAAE,CAAC;IACpB,oBAAoB;IACpB,MAAM,SAAS,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;IAChC,MAAM,SAAS,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;IAChC,OAAO,KAAK,IAAI,CAAC,UAAU,IAAI,UAAU;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1738, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs"], "sourcesContent": ["import { frame, isPrimaryPointer, cancelFrame, frameData } from 'motion-dom';\nimport { pipe, secondsToMilliseconds, millisecondsToSeconds } from 'motion-utils';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { distance2D } from '../../utils/distance.mjs';\n\n/**\n * @internal\n */\nclass PanSession {\n    constructor(event, handlers, { transformPagePoint, contextWindow = window, dragSnapToOrigin = false, distanceThreshold = 3, } = {}) {\n        /**\n         * @internal\n         */\n        this.startEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEvent = null;\n        /**\n         * @internal\n         */\n        this.lastMoveEventInfo = null;\n        /**\n         * @internal\n         */\n        this.handlers = {};\n        /**\n         * @internal\n         */\n        this.contextWindow = window;\n        this.updatePoint = () => {\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const info = getPanInfo(this.lastMoveEventInfo, this.history);\n            const isPanStarted = this.startEvent !== null;\n            // Only start panning if the offset is larger than 3 pixels. If we make it\n            // any larger than this we'll want to reset the pointer history\n            // on the first update to avoid visual snapping to the cursor.\n            const isDistancePastThreshold = distance2D(info.offset, { x: 0, y: 0 }) >= this.distanceThreshold;\n            if (!isPanStarted && !isDistancePastThreshold)\n                return;\n            const { point } = info;\n            const { timestamp } = frameData;\n            this.history.push({ ...point, timestamp });\n            const { onStart, onMove } = this.handlers;\n            if (!isPanStarted) {\n                onStart && onStart(this.lastMoveEvent, info);\n                this.startEvent = this.lastMoveEvent;\n            }\n            onMove && onMove(this.lastMoveEvent, info);\n        };\n        this.handlePointerMove = (event, info) => {\n            this.lastMoveEvent = event;\n            this.lastMoveEventInfo = transformPoint(info, this.transformPagePoint);\n            // Throttle mouse move event to once per frame\n            frame.update(this.updatePoint, true);\n        };\n        this.handlePointerUp = (event, info) => {\n            this.end();\n            const { onEnd, onSessionEnd, resumeAnimation } = this.handlers;\n            if (this.dragSnapToOrigin)\n                resumeAnimation && resumeAnimation();\n            if (!(this.lastMoveEvent && this.lastMoveEventInfo))\n                return;\n            const panInfo = getPanInfo(event.type === \"pointercancel\"\n                ? this.lastMoveEventInfo\n                : transformPoint(info, this.transformPagePoint), this.history);\n            if (this.startEvent && onEnd) {\n                onEnd(event, panInfo);\n            }\n            onSessionEnd && onSessionEnd(event, panInfo);\n        };\n        // If we have more than one touch, don't start detecting this gesture\n        if (!isPrimaryPointer(event))\n            return;\n        this.dragSnapToOrigin = dragSnapToOrigin;\n        this.handlers = handlers;\n        this.transformPagePoint = transformPagePoint;\n        this.distanceThreshold = distanceThreshold;\n        this.contextWindow = contextWindow || window;\n        const info = extractEventInfo(event);\n        const initialInfo = transformPoint(info, this.transformPagePoint);\n        const { point } = initialInfo;\n        const { timestamp } = frameData;\n        this.history = [{ ...point, timestamp }];\n        const { onSessionStart } = handlers;\n        onSessionStart &&\n            onSessionStart(event, getPanInfo(initialInfo, this.history));\n        this.removeListeners = pipe(addPointerEvent(this.contextWindow, \"pointermove\", this.handlePointerMove), addPointerEvent(this.contextWindow, \"pointerup\", this.handlePointerUp), addPointerEvent(this.contextWindow, \"pointercancel\", this.handlePointerUp));\n    }\n    updateHandlers(handlers) {\n        this.handlers = handlers;\n    }\n    end() {\n        this.removeListeners && this.removeListeners();\n        cancelFrame(this.updatePoint);\n    }\n}\nfunction transformPoint(info, transformPagePoint) {\n    return transformPagePoint ? { point: transformPagePoint(info.point) } : info;\n}\nfunction subtractPoint(a, b) {\n    return { x: a.x - b.x, y: a.y - b.y };\n}\nfunction getPanInfo({ point }, history) {\n    return {\n        point,\n        delta: subtractPoint(point, lastDevicePoint(history)),\n        offset: subtractPoint(point, startDevicePoint(history)),\n        velocity: getVelocity(history, 0.1),\n    };\n}\nfunction startDevicePoint(history) {\n    return history[0];\n}\nfunction lastDevicePoint(history) {\n    return history[history.length - 1];\n}\nfunction getVelocity(history, timeDelta) {\n    if (history.length < 2) {\n        return { x: 0, y: 0 };\n    }\n    let i = history.length - 1;\n    let timestampedPoint = null;\n    const lastPoint = lastDevicePoint(history);\n    while (i >= 0) {\n        timestampedPoint = history[i];\n        if (lastPoint.timestamp - timestampedPoint.timestamp >\n            secondsToMilliseconds(timeDelta)) {\n            break;\n        }\n        i--;\n    }\n    if (!timestampedPoint) {\n        return { x: 0, y: 0 };\n    }\n    const time = millisecondsToSeconds(lastPoint.timestamp - timestampedPoint.timestamp);\n    if (time === 0) {\n        return { x: 0, y: 0 };\n    }\n    const currentVelocity = {\n        x: (lastPoint.x - timestampedPoint.x) / time,\n        y: (lastPoint.y - timestampedPoint.y) / time,\n    };\n    if (currentVelocity.x === Infinity) {\n        currentVelocity.x = 0;\n    }\n    if (currentVelocity.y === Infinity) {\n        currentVelocity.y = 0;\n    }\n    return currentVelocity;\n}\n\nexport { PanSession };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AAEA;;CAEC,GACD,MAAM;IACF,YAAY,KAAK,EAAE,QAAQ,EAAE,EAAE,kBAAkB,EAAE,gBAAgB,MAAM,EAAE,mBAAmB,KAAK,EAAE,oBAAoB,CAAC,EAAG,GAAG,CAAC,CAAC,CAAE;QAChI;;SAEC,GACD,IAAI,CAAC,UAAU,GAAG;QAClB;;SAEC,GACD,IAAI,CAAC,aAAa,GAAG;QACrB;;SAEC,GACD,IAAI,CAAC,iBAAiB,GAAG;QACzB;;SAEC,GACD,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB;;SAEC,GACD,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,WAAW,GAAG;YACf,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,iBAAiB,GAC9C;YACJ,MAAM,OAAO,WAAW,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO;YAC5D,MAAM,eAAe,IAAI,CAAC,UAAU,KAAK;YACzC,0EAA0E;YAC1E,+DAA+D;YAC/D,8DAA8D;YAC9D,MAAM,0BAA0B,CAAA,GAAA,oKAAA,CAAA,aAAU,AAAD,EAAE,KAAK,MAAM,EAAE;gBAAE,GAAG;gBAAG,GAAG;YAAE,MAAM,IAAI,CAAC,iBAAiB;YACjG,IAAI,CAAC,gBAAgB,CAAC,yBAClB;YACJ,MAAM,EAAE,KAAK,EAAE,GAAG;YAClB,MAAM,EAAE,SAAS,EAAE,GAAG,kKAAA,CAAA,YAAS;YAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAAE,GAAG,KAAK;gBAAE;YAAU;YACxC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ;YACzC,IAAI,CAAC,cAAc;gBACf,WAAW,QAAQ,IAAI,CAAC,aAAa,EAAE;gBACvC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa;YACxC;YACA,UAAU,OAAO,IAAI,CAAC,aAAa,EAAE;QACzC;QACA,IAAI,CAAC,iBAAiB,GAAG,CAAC,OAAO;YAC7B,IAAI,CAAC,aAAa,GAAG;YACrB,IAAI,CAAC,iBAAiB,GAAG,eAAe,MAAM,IAAI,CAAC,kBAAkB;YACrE,8CAA8C;YAC9C,kKAAA,CAAA,QAAK,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;QACnC;QACA,IAAI,CAAC,eAAe,GAAG,CAAC,OAAO;YAC3B,IAAI,CAAC,GAAG;YACR,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,QAAQ;YAC9D,IAAI,IAAI,CAAC,gBAAgB,EACrB,mBAAmB;YACvB,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,iBAAiB,GAC9C;YACJ,MAAM,UAAU,WAAW,MAAM,IAAI,KAAK,kBACpC,IAAI,CAAC,iBAAiB,GACtB,eAAe,MAAM,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO;YACjE,IAAI,IAAI,CAAC,UAAU,IAAI,OAAO;gBAC1B,MAAM,OAAO;YACjB;YACA,gBAAgB,aAAa,OAAO;QACxC;QACA,qEAAqE;QACrE,IAAI,CAAC,CAAA,GAAA,6LAAA,CAAA,mBAAgB,AAAD,EAAE,QAClB;QACJ,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,aAAa,GAAG,iBAAiB;QACtC,MAAM,OAAO,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE;QAC9B,MAAM,cAAc,eAAe,MAAM,IAAI,CAAC,kBAAkB;QAChE,MAAM,EAAE,KAAK,EAAE,GAAG;QAClB,MAAM,EAAE,SAAS,EAAE,GAAG,kKAAA,CAAA,YAAS;QAC/B,IAAI,CAAC,OAAO,GAAG;YAAC;gBAAE,GAAG,KAAK;gBAAE;YAAU;SAAE;QACxC,MAAM,EAAE,cAAc,EAAE,GAAG;QAC3B,kBACI,eAAe,OAAO,WAAW,aAAa,IAAI,CAAC,OAAO;QAC9D,IAAI,CAAC,eAAe,GAAG,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE,eAAe,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE,aAAa,IAAI,CAAC,eAAe,GAAG,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE,iBAAiB,IAAI,CAAC,eAAe;IAC7P;IACA,eAAe,QAAQ,EAAE;QACrB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,MAAM;QACF,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe;QAC5C,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,WAAW;IAChC;AACJ;AACA,SAAS,eAAe,IAAI,EAAE,kBAAkB;IAC5C,OAAO,qBAAqB;QAAE,OAAO,mBAAmB,KAAK,KAAK;IAAE,IAAI;AAC5E;AACA,SAAS,cAAc,CAAC,EAAE,CAAC;IACvB,OAAO;QAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;QAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;IAAC;AACxC;AACA,SAAS,WAAW,EAAE,KAAK,EAAE,EAAE,OAAO;IAClC,OAAO;QACH;QACA,OAAO,cAAc,OAAO,gBAAgB;QAC5C,QAAQ,cAAc,OAAO,iBAAiB;QAC9C,UAAU,YAAY,SAAS;IACnC;AACJ;AACA,SAAS,iBAAiB,OAAO;IAC7B,OAAO,OAAO,CAAC,EAAE;AACrB;AACA,SAAS,gBAAgB,OAAO;IAC5B,OAAO,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;AACtC;AACA,SAAS,YAAY,OAAO,EAAE,SAAS;IACnC,IAAI,QAAQ,MAAM,GAAG,GAAG;QACpB,OAAO;YAAE,GAAG;YAAG,GAAG;QAAE;IACxB;IACA,IAAI,IAAI,QAAQ,MAAM,GAAG;IACzB,IAAI,mBAAmB;IACvB,MAAM,YAAY,gBAAgB;IAClC,MAAO,KAAK,EAAG;QACX,mBAAmB,OAAO,CAAC,EAAE;QAC7B,IAAI,UAAU,SAAS,GAAG,iBAAiB,SAAS,GAChD,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,YAAY;YAClC;QACJ;QACA;IACJ;IACA,IAAI,CAAC,kBAAkB;QACnB,OAAO;YAAE,GAAG;YAAG,GAAG;QAAE;IACxB;IACA,MAAM,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU,SAAS,GAAG,iBAAiB,SAAS;IACnF,IAAI,SAAS,GAAG;QACZ,OAAO;YAAE,GAAG;YAAG,GAAG;QAAE;IACxB;IACA,MAAM,kBAAkB;QACpB,GAAG,CAAC,UAAU,CAAC,GAAG,iBAAiB,CAAC,IAAI;QACxC,GAAG,CAAC,UAAU,CAAC,GAAG,iBAAiB,CAAC,IAAI;IAC5C;IACA,IAAI,gBAAgB,CAAC,KAAK,UAAU;QAChC,gBAAgB,CAAC,GAAG;IACxB;IACA,IAAI,gBAAgB,CAAC,KAAK,UAAU;QAChC,gBAAgB,CAAC,GAAG;IACxB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1917, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs"], "sourcesContent": ["import { mixNumber } from 'motion-dom';\nimport { progress, clamp } from 'motion-utils';\nimport { calcLength } from '../../../projection/geometry/delta-calc.mjs';\n\n/**\n * Apply constraints to a point. These constraints are both physical along an\n * axis, and an elastic factor that determines how much to constrain the point\n * by if it does lie outside the defined parameters.\n */\nfunction applyConstraints(point, { min, max }, elastic) {\n    if (min !== undefined && point < min) {\n        // If we have a min point defined, and this is outside of that, constrain\n        point = elastic\n            ? mixNumber(min, point, elastic.min)\n            : Math.max(point, min);\n    }\n    else if (max !== undefined && point > max) {\n        // If we have a max point defined, and this is outside of that, constrain\n        point = elastic\n            ? mixNumber(max, point, elastic.max)\n            : Math.min(point, max);\n    }\n    return point;\n}\n/**\n * Calculate constraints in terms of the viewport when defined relatively to the\n * measured axis. This is measured from the nearest edge, so a max constraint of 200\n * on an axis with a max value of 300 would return a constraint of 500 - axis length\n */\nfunction calcRelativeAxisConstraints(axis, min, max) {\n    return {\n        min: min !== undefined ? axis.min + min : undefined,\n        max: max !== undefined\n            ? axis.max + max - (axis.max - axis.min)\n            : undefined,\n    };\n}\n/**\n * Calculate constraints in terms of the viewport when\n * defined relatively to the measured bounding box.\n */\nfunction calcRelativeConstraints(layoutBox, { top, left, bottom, right }) {\n    return {\n        x: calcRelativeAxisConstraints(layoutBox.x, left, right),\n        y: calcRelativeAxisConstraints(layoutBox.y, top, bottom),\n    };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative axis\n */\nfunction calcViewportAxisConstraints(layoutAxis, constraintsAxis) {\n    let min = constraintsAxis.min - layoutAxis.min;\n    let max = constraintsAxis.max - layoutAxis.max;\n    // If the constraints axis is actually smaller than the layout axis then we can\n    // flip the constraints\n    if (constraintsAxis.max - constraintsAxis.min <\n        layoutAxis.max - layoutAxis.min) {\n        [min, max] = [max, min];\n    }\n    return { min, max };\n}\n/**\n * Calculate viewport constraints when defined as another viewport-relative box\n */\nfunction calcViewportConstraints(layoutBox, constraintsBox) {\n    return {\n        x: calcViewportAxisConstraints(layoutBox.x, constraintsBox.x),\n        y: calcViewportAxisConstraints(layoutBox.y, constraintsBox.y),\n    };\n}\n/**\n * Calculate a transform origin relative to the source axis, between 0-1, that results\n * in an asthetically pleasing scale/transform needed to project from source to target.\n */\nfunction calcOrigin(source, target) {\n    let origin = 0.5;\n    const sourceLength = calcLength(source);\n    const targetLength = calcLength(target);\n    if (targetLength > sourceLength) {\n        origin = progress(target.min, target.max - sourceLength, source.min);\n    }\n    else if (sourceLength > targetLength) {\n        origin = progress(source.min, source.max - targetLength, target.min);\n    }\n    return clamp(0, 1, origin);\n}\n/**\n * Rebase the calculated viewport constraints relative to the layout.min point.\n */\nfunction rebaseAxisConstraints(layout, constraints) {\n    const relativeConstraints = {};\n    if (constraints.min !== undefined) {\n        relativeConstraints.min = constraints.min - layout.min;\n    }\n    if (constraints.max !== undefined) {\n        relativeConstraints.max = constraints.max - layout.min;\n    }\n    return relativeConstraints;\n}\nconst defaultElastic = 0.35;\n/**\n * Accepts a dragElastic prop and returns resolved elastic values for each axis.\n */\nfunction resolveDragElastic(dragElastic = defaultElastic) {\n    if (dragElastic === false) {\n        dragElastic = 0;\n    }\n    else if (dragElastic === true) {\n        dragElastic = defaultElastic;\n    }\n    return {\n        x: resolveAxisElastic(dragElastic, \"left\", \"right\"),\n        y: resolveAxisElastic(dragElastic, \"top\", \"bottom\"),\n    };\n}\nfunction resolveAxisElastic(dragElastic, minLabel, maxLabel) {\n    return {\n        min: resolvePointElastic(dragElastic, minLabel),\n        max: resolvePointElastic(dragElastic, maxLabel),\n    };\n}\nfunction resolvePointElastic(dragElastic, label) {\n    return typeof dragElastic === \"number\"\n        ? dragElastic\n        : dragElastic[label] || 0;\n}\n\nexport { applyConstraints, calcOrigin, calcRelativeAxisConstraints, calcRelativeConstraints, calcViewportAxisConstraints, calcViewportConstraints, defaultElastic, rebaseAxisConstraints, resolveAxisElastic, resolveDragElastic, resolvePointElastic };\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AAAA;AACA;;;;AAEA;;;;CAIC,GACD,SAAS,iBAAiB,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,OAAO;IAClD,IAAI,QAAQ,aAAa,QAAQ,KAAK;QAClC,yEAAyE;QACzE,QAAQ,UACF,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,KAAK,OAAO,QAAQ,GAAG,IACjC,KAAK,GAAG,CAAC,OAAO;IAC1B,OACK,IAAI,QAAQ,aAAa,QAAQ,KAAK;QACvC,yEAAyE;QACzE,QAAQ,UACF,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,KAAK,OAAO,QAAQ,GAAG,IACjC,KAAK,GAAG,CAAC,OAAO;IAC1B;IACA,OAAO;AACX;AACA;;;;CAIC,GACD,SAAS,4BAA4B,IAAI,EAAE,GAAG,EAAE,GAAG;IAC/C,OAAO;QACH,KAAK,QAAQ,YAAY,KAAK,GAAG,GAAG,MAAM;QAC1C,KAAK,QAAQ,YACP,KAAK,GAAG,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG,KAAK,GAAG,IACrC;IACV;AACJ;AACA;;;CAGC,GACD,SAAS,wBAAwB,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE;IACpE,OAAO;QACH,GAAG,4BAA4B,UAAU,CAAC,EAAE,MAAM;QAClD,GAAG,4BAA4B,UAAU,CAAC,EAAE,KAAK;IACrD;AACJ;AACA;;CAEC,GACD,SAAS,4BAA4B,UAAU,EAAE,eAAe;IAC5D,IAAI,MAAM,gBAAgB,GAAG,GAAG,WAAW,GAAG;IAC9C,IAAI,MAAM,gBAAgB,GAAG,GAAG,WAAW,GAAG;IAC9C,+EAA+E;IAC/E,uBAAuB;IACvB,IAAI,gBAAgB,GAAG,GAAG,gBAAgB,GAAG,GACzC,WAAW,GAAG,GAAG,WAAW,GAAG,EAAE;QACjC,CAAC,KAAK,IAAI,GAAG;YAAC;YAAK;SAAI;IAC3B;IACA,OAAO;QAAE;QAAK;IAAI;AACtB;AACA;;CAEC,GACD,SAAS,wBAAwB,SAAS,EAAE,cAAc;IACtD,OAAO;QACH,GAAG,4BAA4B,UAAU,CAAC,EAAE,eAAe,CAAC;QAC5D,GAAG,4BAA4B,UAAU,CAAC,EAAE,eAAe,CAAC;IAChE;AACJ;AACA;;;CAGC,GACD,SAAS,WAAW,MAAM,EAAE,MAAM;IAC9B,IAAI,SAAS;IACb,MAAM,eAAe,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE;IAChC,MAAM,eAAe,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE;IAChC,IAAI,eAAe,cAAc;QAC7B,SAAS,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,GAAG,EAAE,OAAO,GAAG,GAAG,cAAc,OAAO,GAAG;IACvE,OACK,IAAI,eAAe,cAAc;QAClC,SAAS,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,GAAG,EAAE,OAAO,GAAG,GAAG,cAAc,OAAO,GAAG;IACvE;IACA,OAAO,CAAA,GAAA,uJAAA,CAAA,QAAK,AAAD,EAAE,GAAG,GAAG;AACvB;AACA;;CAEC,GACD,SAAS,sBAAsB,MAAM,EAAE,WAAW;IAC9C,MAAM,sBAAsB,CAAC;IAC7B,IAAI,YAAY,GAAG,KAAK,WAAW;QAC/B,oBAAoB,GAAG,GAAG,YAAY,GAAG,GAAG,OAAO,GAAG;IAC1D;IACA,IAAI,YAAY,GAAG,KAAK,WAAW;QAC/B,oBAAoB,GAAG,GAAG,YAAY,GAAG,GAAG,OAAO,GAAG;IAC1D;IACA,OAAO;AACX;AACA,MAAM,iBAAiB;AACvB;;CAEC,GACD,SAAS,mBAAmB,cAAc,cAAc;IACpD,IAAI,gBAAgB,OAAO;QACvB,cAAc;IAClB,OACK,IAAI,gBAAgB,MAAM;QAC3B,cAAc;IAClB;IACA,OAAO;QACH,GAAG,mBAAmB,aAAa,QAAQ;QAC3C,GAAG,mBAAmB,aAAa,OAAO;IAC9C;AACJ;AACA,SAAS,mBAAmB,WAAW,EAAE,QAAQ,EAAE,QAAQ;IACvD,OAAO;QACH,KAAK,oBAAoB,aAAa;QACtC,KAAK,oBAAoB,aAAa;IAC1C;AACJ;AACA,SAAS,oBAAoB,WAAW,EAAE,KAAK;IAC3C,OAAO,OAAO,gBAAgB,WACxB,cACA,WAAW,CAAC,MAAM,IAAI;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2052, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs"], "sourcesContent": ["import { frame, mixNumber, setDragLock, percent } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { animateMotionValue } from '../../animation/interfaces/motion-value.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { PanSession } from '../pan/PanSession.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, rebaseAxisConstraints, calcViewportConstraints, calcOrigin, defaultElastic } from './utils/constraints.mjs';\n\nconst elementDragControls = new WeakMap();\nclass VisualElementDragControls {\n    constructor(visualElement) {\n        this.openDragLock = null;\n        this.isDragging = false;\n        this.currentDirection = null;\n        this.originPoint = { x: 0, y: 0 };\n        /**\n         * The permitted boundaries of travel, in pixels.\n         */\n        this.constraints = false;\n        this.hasMutatedConstraints = false;\n        /**\n         * The per-axis resolved elastic values.\n         */\n        this.elastic = createBox();\n        /**\n         * The latest pointer event. Used as fallback when the `cancel` and `stop` functions are called without arguments.\n         */\n        this.latestPointerEvent = null;\n        /**\n         * The latest pan info. Used as fallback when the `cancel` and `stop` functions are called without arguments.\n         */\n        this.latestPanInfo = null;\n        this.visualElement = visualElement;\n    }\n    start(originEvent, { snapToCursor = false, distanceThreshold } = {}) {\n        /**\n         * Don't start dragging if this component is exiting\n         */\n        const { presenceContext } = this.visualElement;\n        if (presenceContext && presenceContext.isPresent === false)\n            return;\n        const onSessionStart = (event) => {\n            const { dragSnapToOrigin } = this.getProps();\n            // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n            // the component.\n            dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n            if (snapToCursor) {\n                this.snapToCursor(extractEventInfo(event).point);\n            }\n        };\n        const onStart = (event, info) => {\n            // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n            const { drag, dragPropagation, onDragStart } = this.getProps();\n            if (drag && !dragPropagation) {\n                if (this.openDragLock)\n                    this.openDragLock();\n                this.openDragLock = setDragLock(drag);\n                // If we don 't have the lock, don't start dragging\n                if (!this.openDragLock)\n                    return;\n            }\n            this.latestPointerEvent = event;\n            this.latestPanInfo = info;\n            this.isDragging = true;\n            this.currentDirection = null;\n            this.resolveConstraints();\n            if (this.visualElement.projection) {\n                this.visualElement.projection.isAnimationBlocked = true;\n                this.visualElement.projection.target = undefined;\n            }\n            /**\n             * Record gesture origin\n             */\n            eachAxis((axis) => {\n                let current = this.getAxisMotionValue(axis).get() || 0;\n                /**\n                 * If the MotionValue is a percentage value convert to px\n                 */\n                if (percent.test(current)) {\n                    const { projection } = this.visualElement;\n                    if (projection && projection.layout) {\n                        const measuredAxis = projection.layout.layoutBox[axis];\n                        if (measuredAxis) {\n                            const length = calcLength(measuredAxis);\n                            current = length * (parseFloat(current) / 100);\n                        }\n                    }\n                }\n                this.originPoint[axis] = current;\n            });\n            // Fire onDragStart event\n            if (onDragStart) {\n                frame.postRender(() => onDragStart(event, info));\n            }\n            addValueToWillChange(this.visualElement, \"transform\");\n            const { animationState } = this.visualElement;\n            animationState && animationState.setActive(\"whileDrag\", true);\n        };\n        const onMove = (event, info) => {\n            this.latestPointerEvent = event;\n            this.latestPanInfo = info;\n            const { dragPropagation, dragDirectionLock, onDirectionLock, onDrag, } = this.getProps();\n            // If we didn't successfully receive the gesture lock, early return.\n            if (!dragPropagation && !this.openDragLock)\n                return;\n            const { offset } = info;\n            // Attempt to detect drag direction if directionLock is true\n            if (dragDirectionLock && this.currentDirection === null) {\n                this.currentDirection = getCurrentDirection(offset);\n                // If we've successfully set a direction, notify listener\n                if (this.currentDirection !== null) {\n                    onDirectionLock && onDirectionLock(this.currentDirection);\n                }\n                return;\n            }\n            // Update each point with the latest position\n            this.updateAxis(\"x\", info.point, offset);\n            this.updateAxis(\"y\", info.point, offset);\n            /**\n             * Ideally we would leave the renderer to fire naturally at the end of\n             * this frame but if the element is about to change layout as the result\n             * of a re-render we want to ensure the browser can read the latest\n             * bounding box to ensure the pointer and element don't fall out of sync.\n             */\n            this.visualElement.render();\n            /**\n             * This must fire after the render call as it might trigger a state\n             * change which itself might trigger a layout update.\n             */\n            onDrag && onDrag(event, info);\n        };\n        const onSessionEnd = (event, info) => {\n            this.latestPointerEvent = event;\n            this.latestPanInfo = info;\n            this.stop(event, info);\n            this.latestPointerEvent = null;\n            this.latestPanInfo = null;\n        };\n        const resumeAnimation = () => eachAxis((axis) => this.getAnimationState(axis) === \"paused\" &&\n            this.getAxisMotionValue(axis).animation?.play());\n        const { dragSnapToOrigin } = this.getProps();\n        this.panSession = new PanSession(originEvent, {\n            onSessionStart,\n            onStart,\n            onMove,\n            onSessionEnd,\n            resumeAnimation,\n        }, {\n            transformPagePoint: this.visualElement.getTransformPagePoint(),\n            dragSnapToOrigin,\n            distanceThreshold,\n            contextWindow: getContextWindow(this.visualElement),\n        });\n    }\n    /**\n     * @internal\n     */\n    stop(event, panInfo) {\n        const finalEvent = event || this.latestPointerEvent;\n        const finalPanInfo = panInfo || this.latestPanInfo;\n        const isDragging = this.isDragging;\n        this.cancel();\n        if (!isDragging || !finalPanInfo || !finalEvent)\n            return;\n        const { velocity } = finalPanInfo;\n        this.startAnimation(velocity);\n        const { onDragEnd } = this.getProps();\n        if (onDragEnd) {\n            frame.postRender(() => onDragEnd(finalEvent, finalPanInfo));\n        }\n    }\n    /**\n     * @internal\n     */\n    cancel() {\n        this.isDragging = false;\n        const { projection, animationState } = this.visualElement;\n        if (projection) {\n            projection.isAnimationBlocked = false;\n        }\n        this.panSession && this.panSession.end();\n        this.panSession = undefined;\n        const { dragPropagation } = this.getProps();\n        if (!dragPropagation && this.openDragLock) {\n            this.openDragLock();\n            this.openDragLock = null;\n        }\n        animationState && animationState.setActive(\"whileDrag\", false);\n    }\n    updateAxis(axis, _point, offset) {\n        const { drag } = this.getProps();\n        // If we're not dragging this axis, do an early return.\n        if (!offset || !shouldDrag(axis, drag, this.currentDirection))\n            return;\n        const axisValue = this.getAxisMotionValue(axis);\n        let next = this.originPoint[axis] + offset[axis];\n        // Apply constraints\n        if (this.constraints && this.constraints[axis]) {\n            next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n        }\n        axisValue.set(next);\n    }\n    resolveConstraints() {\n        const { dragConstraints, dragElastic } = this.getProps();\n        const layout = this.visualElement.projection &&\n            !this.visualElement.projection.layout\n            ? this.visualElement.projection.measure(false)\n            : this.visualElement.projection?.layout;\n        const prevConstraints = this.constraints;\n        if (dragConstraints && isRefObject(dragConstraints)) {\n            if (!this.constraints) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        }\n        else {\n            if (dragConstraints && layout) {\n                this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n            }\n            else {\n                this.constraints = false;\n            }\n        }\n        this.elastic = resolveDragElastic(dragElastic);\n        /**\n         * If we're outputting to external MotionValues, we want to rebase the measured constraints\n         * from viewport-relative to component-relative.\n         */\n        if (prevConstraints !== this.constraints &&\n            layout &&\n            this.constraints &&\n            !this.hasMutatedConstraints) {\n            eachAxis((axis) => {\n                if (this.constraints !== false &&\n                    this.getAxisMotionValue(axis)) {\n                    this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n                }\n            });\n        }\n    }\n    resolveRefConstraints() {\n        const { dragConstraints: constraints, onMeasureDragConstraints } = this.getProps();\n        if (!constraints || !isRefObject(constraints))\n            return false;\n        const constraintsElement = constraints.current;\n        invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\", \"drag-constraints-ref\");\n        const { projection } = this.visualElement;\n        // TODO\n        if (!projection || !projection.layout)\n            return false;\n        const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n        let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n        /**\n         * If there's an onMeasureDragConstraints listener we call it and\n         * if different constraints are returned, set constraints to that\n         */\n        if (onMeasureDragConstraints) {\n            const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n            this.hasMutatedConstraints = !!userConstraints;\n            if (userConstraints) {\n                measuredConstraints = convertBoundingBoxToBox(userConstraints);\n            }\n        }\n        return measuredConstraints;\n    }\n    startAnimation(velocity) {\n        const { drag, dragMomentum, dragElastic, dragTransition, dragSnapToOrigin, onDragTransitionEnd, } = this.getProps();\n        const constraints = this.constraints || {};\n        const momentumAnimations = eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, this.currentDirection)) {\n                return;\n            }\n            let transition = (constraints && constraints[axis]) || {};\n            if (dragSnapToOrigin)\n                transition = { min: 0, max: 0 };\n            /**\n             * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n             * of spring animations so we should look into adding a disable spring option to `inertia`.\n             * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n             * using the value of `dragElastic`.\n             */\n            const bounceStiffness = dragElastic ? 200 : 1000000;\n            const bounceDamping = dragElastic ? 40 : 10000000;\n            const inertia = {\n                type: \"inertia\",\n                velocity: dragMomentum ? velocity[axis] : 0,\n                bounceStiffness,\n                bounceDamping,\n                timeConstant: 750,\n                restDelta: 1,\n                restSpeed: 10,\n                ...dragTransition,\n                ...transition,\n            };\n            // If we're not animating on an externally-provided `MotionValue` we can use the\n            // component's animation controls which will handle interactions with whileHover (etc),\n            // otherwise we just have to animate the `MotionValue` itself.\n            return this.startAxisValueAnimation(axis, inertia);\n        });\n        // Run all animations and then resolve the new drag constraints.\n        return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n    }\n    startAxisValueAnimation(axis, transition) {\n        const axisValue = this.getAxisMotionValue(axis);\n        addValueToWillChange(this.visualElement, axis);\n        return axisValue.start(animateMotionValue(axis, axisValue, 0, transition, this.visualElement, false));\n    }\n    stopAnimation() {\n        eachAxis((axis) => this.getAxisMotionValue(axis).stop());\n    }\n    pauseAnimation() {\n        eachAxis((axis) => this.getAxisMotionValue(axis).animation?.pause());\n    }\n    getAnimationState(axis) {\n        return this.getAxisMotionValue(axis).animation?.state;\n    }\n    /**\n     * Drag works differently depending on which props are provided.\n     *\n     * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n     * - Otherwise, we apply the delta to the x/y motion values.\n     */\n    getAxisMotionValue(axis) {\n        const dragKey = `_drag${axis.toUpperCase()}`;\n        const props = this.visualElement.getProps();\n        const externalMotionValue = props[dragKey];\n        return externalMotionValue\n            ? externalMotionValue\n            : this.visualElement.getValue(axis, (props.initial\n                ? props.initial[axis]\n                : undefined) || 0);\n    }\n    snapToCursor(point) {\n        eachAxis((axis) => {\n            const { drag } = this.getProps();\n            // If we're not dragging this axis, do an early return.\n            if (!shouldDrag(axis, drag, this.currentDirection))\n                return;\n            const { projection } = this.visualElement;\n            const axisValue = this.getAxisMotionValue(axis);\n            if (projection && projection.layout) {\n                const { min, max } = projection.layout.layoutBox[axis];\n                axisValue.set(point[axis] - mixNumber(min, max, 0.5));\n            }\n        });\n    }\n    /**\n     * When the viewport resizes we want to check if the measured constraints\n     * have changed and, if so, reposition the element within those new constraints\n     * relative to where it was before the resize.\n     */\n    scalePositionWithinConstraints() {\n        if (!this.visualElement.current)\n            return;\n        const { drag, dragConstraints } = this.getProps();\n        const { projection } = this.visualElement;\n        if (!isRefObject(dragConstraints) || !projection || !this.constraints)\n            return;\n        /**\n         * Stop current animations as there can be visual glitching if we try to do\n         * this mid-animation\n         */\n        this.stopAnimation();\n        /**\n         * Record the relative position of the dragged element relative to the\n         * constraints box and save as a progress value.\n         */\n        const boxProgress = { x: 0, y: 0 };\n        eachAxis((axis) => {\n            const axisValue = this.getAxisMotionValue(axis);\n            if (axisValue && this.constraints !== false) {\n                const latest = axisValue.get();\n                boxProgress[axis] = calcOrigin({ min: latest, max: latest }, this.constraints[axis]);\n            }\n        });\n        /**\n         * Update the layout of this element and resolve the latest drag constraints\n         */\n        const { transformTemplate } = this.visualElement.getProps();\n        this.visualElement.current.style.transform = transformTemplate\n            ? transformTemplate({}, \"\")\n            : \"none\";\n        projection.root && projection.root.updateScroll();\n        projection.updateLayout();\n        this.resolveConstraints();\n        /**\n         * For each axis, calculate the current progress of the layout axis\n         * within the new constraints.\n         */\n        eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, null))\n                return;\n            /**\n             * Calculate a new transform based on the previous box progress\n             */\n            const axisValue = this.getAxisMotionValue(axis);\n            const { min, max } = this.constraints[axis];\n            axisValue.set(mixNumber(min, max, boxProgress[axis]));\n        });\n    }\n    addListeners() {\n        if (!this.visualElement.current)\n            return;\n        elementDragControls.set(this.visualElement, this);\n        const element = this.visualElement.current;\n        /**\n         * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n         */\n        const stopPointerListener = addPointerEvent(element, \"pointerdown\", (event) => {\n            const { drag, dragListener = true } = this.getProps();\n            drag && dragListener && this.start(event);\n        });\n        const measureDragConstraints = () => {\n            const { dragConstraints } = this.getProps();\n            if (isRefObject(dragConstraints) && dragConstraints.current) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        };\n        const { projection } = this.visualElement;\n        const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n        if (projection && !projection.layout) {\n            projection.root && projection.root.updateScroll();\n            projection.updateLayout();\n        }\n        frame.read(measureDragConstraints);\n        /**\n         * Attach a window resize listener to scale the draggable target within its defined\n         * constraints as the window resizes.\n         */\n        const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n        /**\n         * If the element's layout changes, calculate the delta and apply that to\n         * the drag gesture's origin point.\n         */\n        const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", (({ delta, hasLayoutChanged }) => {\n            if (this.isDragging && hasLayoutChanged) {\n                eachAxis((axis) => {\n                    const motionValue = this.getAxisMotionValue(axis);\n                    if (!motionValue)\n                        return;\n                    this.originPoint[axis] += delta[axis].translate;\n                    motionValue.set(motionValue.get() + delta[axis].translate);\n                });\n                this.visualElement.render();\n            }\n        }));\n        return () => {\n            stopResizeListener();\n            stopPointerListener();\n            stopMeasureLayoutListener();\n            stopLayoutUpdateListener && stopLayoutUpdateListener();\n        };\n    }\n    getProps() {\n        const props = this.visualElement.getProps();\n        const { drag = false, dragDirectionLock = false, dragPropagation = false, dragConstraints = false, dragElastic = defaultElastic, dragMomentum = true, } = props;\n        return {\n            ...props,\n            drag,\n            dragDirectionLock,\n            dragPropagation,\n            dragConstraints,\n            dragElastic,\n            dragMomentum,\n        };\n    }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n    return ((drag === true || drag === direction) &&\n        (currentDirection === null || currentDirection === direction));\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n    let direction = null;\n    if (Math.abs(offset.y) > lockThreshold) {\n        direction = \"y\";\n    }\n    else if (Math.abs(offset.x) > lockThreshold) {\n        direction = \"x\";\n    }\n    return direction;\n}\n\nexport { VisualElementDragControls, elementDragControls };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAEA,MAAM,sBAAsB,IAAI;AAChC,MAAM;IACF,YAAY,aAAa,CAAE;QACvB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,WAAW,GAAG;YAAE,GAAG;YAAG,GAAG;QAAE;QAChC;;SAEC,GACD,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,qBAAqB,GAAG;QAC7B;;SAEC,GACD,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;QACvB;;SAEC,GACD,IAAI,CAAC,kBAAkB,GAAG;QAC1B;;SAEC,GACD,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,aAAa,GAAG;IACzB;IACA,MAAM,WAAW,EAAE,EAAE,eAAe,KAAK,EAAE,iBAAiB,EAAE,GAAG,CAAC,CAAC,EAAE;QACjE;;SAEC,GACD,MAAM,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,aAAa;QAC9C,IAAI,mBAAmB,gBAAgB,SAAS,KAAK,OACjD;QACJ,MAAM,iBAAiB,CAAC;YACpB,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,QAAQ;YAC1C,wGAAwG;YACxG,iBAAiB;YACjB,mBAAmB,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,aAAa;YAC7D,IAAI,cAAc;gBACd,IAAI,CAAC,YAAY,CAAC,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,KAAK;YACnD;QACJ;QACA,MAAM,UAAU,CAAC,OAAO;YACpB,oFAAoF;YACpF,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,QAAQ;YAC5D,IAAI,QAAQ,CAAC,iBAAiB;gBAC1B,IAAI,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,YAAY;gBACrB,IAAI,CAAC,YAAY,GAAG,CAAA,GAAA,0LAAA,CAAA,cAAW,AAAD,EAAE;gBAChC,mDAAmD;gBACnD,IAAI,CAAC,IAAI,CAAC,YAAY,EAClB;YACR;YACA,IAAI,CAAC,kBAAkB,GAAG;YAC1B,IAAI,CAAC,aAAa,GAAG;YACrB,IAAI,CAAC,UAAU,GAAG;YAClB,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,kBAAkB;YACvB,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;gBAC/B,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,kBAAkB,GAAG;gBACnD,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,GAAG;YAC3C;YACA;;aAEC,GACD,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;gBACN,IAAI,UAAU,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,MAAM;gBACrD;;iBAEC,GACD,IAAI,kLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,UAAU;oBACvB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,aAAa;oBACzC,IAAI,cAAc,WAAW,MAAM,EAAE;wBACjC,MAAM,eAAe,WAAW,MAAM,CAAC,SAAS,CAAC,KAAK;wBACtD,IAAI,cAAc;4BACd,MAAM,SAAS,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE;4BAC1B,UAAU,SAAS,CAAC,WAAW,WAAW,GAAG;wBACjD;oBACJ;gBACJ;gBACA,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG;YAC7B;YACA,yBAAyB;YACzB,IAAI,aAAa;gBACb,kKAAA,CAAA,QAAK,CAAC,UAAU,CAAC,IAAM,YAAY,OAAO;YAC9C;YACA,CAAA,GAAA,0MAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE;YACzC,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,aAAa;YAC7C,kBAAkB,eAAe,SAAS,CAAC,aAAa;QAC5D;QACA,MAAM,SAAS,CAAC,OAAO;YACnB,IAAI,CAAC,kBAAkB,GAAG;YAC1B,IAAI,CAAC,aAAa,GAAG;YACrB,MAAM,EAAE,eAAe,EAAE,iBAAiB,EAAE,eAAe,EAAE,MAAM,EAAG,GAAG,IAAI,CAAC,QAAQ;YACtF,oEAAoE;YACpE,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,EACtC;YACJ,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,4DAA4D;YAC5D,IAAI,qBAAqB,IAAI,CAAC,gBAAgB,KAAK,MAAM;gBACrD,IAAI,CAAC,gBAAgB,GAAG,oBAAoB;gBAC5C,yDAAyD;gBACzD,IAAI,IAAI,CAAC,gBAAgB,KAAK,MAAM;oBAChC,mBAAmB,gBAAgB,IAAI,CAAC,gBAAgB;gBAC5D;gBACA;YACJ;YACA,6CAA6C;YAC7C,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,KAAK,EAAE;YACjC,IAAI,CAAC,UAAU,CAAC,KAAK,KAAK,KAAK,EAAE;YACjC;;;;;aAKC,GACD,IAAI,CAAC,aAAa,CAAC,MAAM;YACzB;;;aAGC,GACD,UAAU,OAAO,OAAO;QAC5B;QACA,MAAM,eAAe,CAAC,OAAO;YACzB,IAAI,CAAC,kBAAkB,GAAG;YAC1B,IAAI,CAAC,aAAa,GAAG;YACrB,IAAI,CAAC,IAAI,CAAC,OAAO;YACjB,IAAI,CAAC,kBAAkB,GAAG;YAC1B,IAAI,CAAC,aAAa,GAAG;QACzB;QACA,MAAM,kBAAkB,IAAM,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,OAAS,IAAI,CAAC,iBAAiB,CAAC,UAAU,YAC9E,IAAI,CAAC,kBAAkB,CAAC,MAAM,SAAS,EAAE;QAC7C,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,QAAQ;QAC1C,IAAI,CAAC,UAAU,GAAG,IAAI,gLAAA,CAAA,aAAU,CAAC,aAAa;YAC1C;YACA;YACA;YACA;YACA;QACJ,GAAG;YACC,oBAAoB,IAAI,CAAC,aAAa,CAAC,qBAAqB;YAC5D;YACA;YACA,eAAe,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,aAAa;QACtD;IACJ;IACA;;KAEC,GACD,KAAK,KAAK,EAAE,OAAO,EAAE;QACjB,MAAM,aAAa,SAAS,IAAI,CAAC,kBAAkB;QACnD,MAAM,eAAe,WAAW,IAAI,CAAC,aAAa;QAClD,MAAM,aAAa,IAAI,CAAC,UAAU;QAClC,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,YACjC;QACJ,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,IAAI,CAAC,cAAc,CAAC;QACpB,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,QAAQ;QACnC,IAAI,WAAW;YACX,kKAAA,CAAA,QAAK,CAAC,UAAU,CAAC,IAAM,UAAU,YAAY;QACjD;IACJ;IACA;;KAEC,GACD,SAAS;QACL,IAAI,CAAC,UAAU,GAAG;QAClB,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,aAAa;QACzD,IAAI,YAAY;YACZ,WAAW,kBAAkB,GAAG;QACpC;QACA,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG;QACtC,IAAI,CAAC,UAAU,GAAG;QAClB,MAAM,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,QAAQ;QACzC,IAAI,CAAC,mBAAmB,IAAI,CAAC,YAAY,EAAE;YACvC,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,YAAY,GAAG;QACxB;QACA,kBAAkB,eAAe,SAAS,CAAC,aAAa;IAC5D;IACA,WAAW,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE;QAC7B,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ;QAC9B,uDAAuD;QACvD,IAAI,CAAC,UAAU,CAAC,WAAW,MAAM,MAAM,IAAI,CAAC,gBAAgB,GACxD;QACJ,MAAM,YAAY,IAAI,CAAC,kBAAkB,CAAC;QAC1C,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;QAChD,oBAAoB;QACpB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;YAC5C,OAAO,CAAA,GAAA,2LAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;QAC5E;QACA,UAAU,GAAG,CAAC;IAClB;IACA,qBAAqB;QACjB,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,QAAQ;QACtD,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,UAAU,IACxC,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,GACnC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,SACtC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;QACrC,MAAM,kBAAkB,IAAI,CAAC,WAAW;QACxC,IAAI,mBAAmB,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB;YACjD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACnB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB;YACjD;QACJ,OACK;YACD,IAAI,mBAAmB,QAAQ;gBAC3B,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,2LAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,SAAS,EAAE;YACjE,OACK;gBACD,IAAI,CAAC,WAAW,GAAG;YACvB;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,2LAAA,CAAA,qBAAkB,AAAD,EAAE;QAClC;;;SAGC,GACD,IAAI,oBAAoB,IAAI,CAAC,WAAW,IACpC,UACA,IAAI,CAAC,WAAW,IAChB,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC7B,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;gBACN,IAAI,IAAI,CAAC,WAAW,KAAK,SACrB,IAAI,CAAC,kBAAkB,CAAC,OAAO;oBAC/B,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,CAAA,GAAA,2LAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK;gBACjG;YACJ;QACJ;IACJ;IACA,wBAAwB;QACpB,MAAM,EAAE,iBAAiB,WAAW,EAAE,wBAAwB,EAAE,GAAG,IAAI,CAAC,QAAQ;QAChF,IAAI,CAAC,eAAe,CAAC,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD,EAAE,cAC7B,OAAO;QACX,MAAM,qBAAqB,YAAY,OAAO;QAC9C,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,uBAAuB,MAAM,0GAA0G;QACjJ,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,aAAa;QACzC,OAAO;QACP,IAAI,CAAC,cAAc,CAAC,WAAW,MAAM,EACjC,OAAO;QACX,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB,WAAW,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,qBAAqB;QACnH,IAAI,sBAAsB,CAAA,GAAA,2LAAA,CAAA,0BAAuB,AAAD,EAAE,WAAW,MAAM,CAAC,SAAS,EAAE;QAC/E;;;SAGC,GACD,IAAI,0BAA0B;YAC1B,MAAM,kBAAkB,yBAAyB,CAAA,GAAA,uLAAA,CAAA,0BAAuB,AAAD,EAAE;YACzE,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC;YAC/B,IAAI,iBAAiB;gBACjB,sBAAsB,CAAA,GAAA,uLAAA,CAAA,0BAAuB,AAAD,EAAE;YAClD;QACJ;QACA,OAAO;IACX;IACA,eAAe,QAAQ,EAAE;QACrB,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,gBAAgB,EAAE,mBAAmB,EAAG,GAAG,IAAI,CAAC,QAAQ;QACjH,MAAM,cAAc,IAAI,CAAC,WAAW,IAAI,CAAC;QACzC,MAAM,qBAAqB,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;YACjC,IAAI,CAAC,WAAW,MAAM,MAAM,IAAI,CAAC,gBAAgB,GAAG;gBAChD;YACJ;YACA,IAAI,aAAa,AAAC,eAAe,WAAW,CAAC,KAAK,IAAK,CAAC;YACxD,IAAI,kBACA,aAAa;gBAAE,KAAK;gBAAG,KAAK;YAAE;YAClC;;;;;aAKC,GACD,MAAM,kBAAkB,cAAc,MAAM;YAC5C,MAAM,gBAAgB,cAAc,KAAK;YACzC,MAAM,UAAU;gBACZ,MAAM;gBACN,UAAU,eAAe,QAAQ,CAAC,KAAK,GAAG;gBAC1C;gBACA;gBACA,cAAc;gBACd,WAAW;gBACX,WAAW;gBACX,GAAG,cAAc;gBACjB,GAAG,UAAU;YACjB;YACA,gFAAgF;YAChF,uFAAuF;YACvF,8DAA8D;YAC9D,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM;QAC9C;QACA,gEAAgE;QAChE,OAAO,QAAQ,GAAG,CAAC,oBAAoB,IAAI,CAAC;IAChD;IACA,wBAAwB,IAAI,EAAE,UAAU,EAAE;QACtC,MAAM,YAAY,IAAI,CAAC,kBAAkB,CAAC;QAC1C,CAAA,GAAA,0MAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE;QACzC,OAAO,UAAU,KAAK,CAAC,CAAA,GAAA,6LAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,WAAW,GAAG,YAAY,IAAI,CAAC,aAAa,EAAE;IAClG;IACA,gBAAgB;QACZ,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,OAAS,IAAI,CAAC,kBAAkB,CAAC,MAAM,IAAI;IACzD;IACA,iBAAiB;QACb,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,OAAS,IAAI,CAAC,kBAAkB,CAAC,MAAM,SAAS,EAAE;IAChE;IACA,kBAAkB,IAAI,EAAE;QACpB,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,SAAS,EAAE;IACpD;IACA;;;;;KAKC,GACD,mBAAmB,IAAI,EAAE;QACrB,MAAM,UAAU,CAAC,KAAK,EAAE,KAAK,WAAW,IAAI;QAC5C,MAAM,QAAQ,IAAI,CAAC,aAAa,CAAC,QAAQ;QACzC,MAAM,sBAAsB,KAAK,CAAC,QAAQ;QAC1C,OAAO,sBACD,sBACA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,OAAO,GAC5C,MAAM,OAAO,CAAC,KAAK,GACnB,SAAS,KAAK;IAC5B;IACA,aAAa,KAAK,EAAE;QAChB,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;YACN,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ;YAC9B,uDAAuD;YACvD,IAAI,CAAC,WAAW,MAAM,MAAM,IAAI,CAAC,gBAAgB,GAC7C;YACJ,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,aAAa;YACzC,MAAM,YAAY,IAAI,CAAC,kBAAkB,CAAC;YAC1C,IAAI,cAAc,WAAW,MAAM,EAAE;gBACjC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,WAAW,MAAM,CAAC,SAAS,CAAC,KAAK;gBACtD,UAAU,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,KAAK,KAAK;YACpD;QACJ;IACJ;IACA;;;;KAIC,GACD,iCAAiC;QAC7B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAC3B;QACJ,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,QAAQ;QAC/C,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,aAAa;QACzC,IAAI,CAAC,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD,EAAE,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EACjE;QACJ;;;SAGC,GACD,IAAI,CAAC,aAAa;QAClB;;;SAGC,GACD,MAAM,cAAc;YAAE,GAAG;YAAG,GAAG;QAAE;QACjC,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;YACN,MAAM,YAAY,IAAI,CAAC,kBAAkB,CAAC;YAC1C,IAAI,aAAa,IAAI,CAAC,WAAW,KAAK,OAAO;gBACzC,MAAM,SAAS,UAAU,GAAG;gBAC5B,WAAW,CAAC,KAAK,GAAG,CAAA,GAAA,2LAAA,CAAA,aAAU,AAAD,EAAE;oBAAE,KAAK;oBAAQ,KAAK;gBAAO,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK;YACvF;QACJ;QACA;;SAEC,GACD,MAAM,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ;QACzD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,oBACvC,kBAAkB,CAAC,GAAG,MACtB;QACN,WAAW,IAAI,IAAI,WAAW,IAAI,CAAC,YAAY;QAC/C,WAAW,YAAY;QACvB,IAAI,CAAC,kBAAkB;QACvB;;;SAGC,GACD,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;YACN,IAAI,CAAC,WAAW,MAAM,MAAM,OACxB;YACJ;;aAEC,GACD,MAAM,YAAY,IAAI,CAAC,kBAAkB,CAAC;YAC1C,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK;YAC3C,UAAU,GAAG,CAAC,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,KAAK,KAAK,WAAW,CAAC,KAAK;QACvD;IACJ;IACA,eAAe;QACX,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAC3B;QACJ,oBAAoB,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI;QAChD,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,OAAO;QAC1C;;SAEC,GACD,MAAM,sBAAsB,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,eAAe,CAAC;YACjE,MAAM,EAAE,IAAI,EAAE,eAAe,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ;YACnD,QAAQ,gBAAgB,IAAI,CAAC,KAAK,CAAC;QACvC;QACA,MAAM,yBAAyB;YAC3B,MAAM,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,QAAQ;YACzC,IAAI,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD,EAAE,oBAAoB,gBAAgB,OAAO,EAAE;gBACzD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,qBAAqB;YACjD;QACJ;QACA,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,aAAa;QACzC,MAAM,4BAA4B,WAAW,gBAAgB,CAAC,WAAW;QACzE,IAAI,cAAc,CAAC,WAAW,MAAM,EAAE;YAClC,WAAW,IAAI,IAAI,WAAW,IAAI,CAAC,YAAY;YAC/C,WAAW,YAAY;QAC3B;QACA,kKAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QACX;;;SAGC,GACD,MAAM,qBAAqB,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,UAAU,IAAM,IAAI,CAAC,8BAA8B;QAClG;;;SAGC,GACD,MAAM,2BAA2B,WAAW,gBAAgB,CAAC,aAAc,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE;YACnG,IAAI,IAAI,CAAC,UAAU,IAAI,kBAAkB;gBACrC,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;oBACN,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC;oBAC5C,IAAI,CAAC,aACD;oBACJ,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,SAAS;oBAC/C,YAAY,GAAG,CAAC,YAAY,GAAG,KAAK,KAAK,CAAC,KAAK,CAAC,SAAS;gBAC7D;gBACA,IAAI,CAAC,aAAa,CAAC,MAAM;YAC7B;QACJ;QACA,OAAO;YACH;YACA;YACA;YACA,4BAA4B;QAChC;IACJ;IACA,WAAW;QACP,MAAM,QAAQ,IAAI,CAAC,aAAa,CAAC,QAAQ;QACzC,MAAM,EAAE,OAAO,KAAK,EAAE,oBAAoB,KAAK,EAAE,kBAAkB,KAAK,EAAE,kBAAkB,KAAK,EAAE,cAAc,2LAAA,CAAA,iBAAc,EAAE,eAAe,IAAI,EAAG,GAAG;QAC1J,OAAO;YACH,GAAG,KAAK;YACR;YACA;YACA;YACA;YACA;YACA;QACJ;IACJ;AACJ;AACA,SAAS,WAAW,SAAS,EAAE,IAAI,EAAE,gBAAgB;IACjD,OAAQ,CAAC,SAAS,QAAQ,SAAS,SAAS,KACxC,CAAC,qBAAqB,QAAQ,qBAAqB,SAAS;AACpE;AACA;;;;;;CAMC,GACD,SAAS,oBAAoB,MAAM,EAAE,gBAAgB,EAAE;IACnD,IAAI,YAAY;IAChB,IAAI,KAAK,GAAG,CAAC,OAAO,CAAC,IAAI,eAAe;QACpC,YAAY;IAChB,OACK,IAAI,KAAK,GAAG,CAAC,OAAO,CAAC,IAAI,eAAe;QACzC,YAAY;IAChB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2531, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/gestures/drag/index.mjs"], "sourcesContent": ["import { Feature } from '../../motion/features/Feature.mjs';\nimport { noop } from 'motion-utils';\nimport { VisualElementDragControls } from './VisualElementDragControls.mjs';\n\nclass DragGesture extends Feature {\n    constructor(node) {\n        super(node);\n        this.removeGroupControls = noop;\n        this.removeListeners = noop;\n        this.controls = new VisualElementDragControls(node);\n    }\n    mount() {\n        // If we've been provided a DragControls for manual control over the drag gesture,\n        // subscribe this component to it on mount.\n        const { dragControls } = this.node.getProps();\n        if (dragControls) {\n            this.removeGroupControls = dragControls.subscribe(this.controls);\n        }\n        this.removeListeners = this.controls.addListeners() || noop;\n    }\n    unmount() {\n        this.removeGroupControls();\n        this.removeListeners();\n    }\n}\n\nexport { DragGesture };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,oBAAoB,gLAAA,CAAA,UAAO;IAC7B,YAAY,IAAI,CAAE;QACd,KAAK,CAAC;QACN,IAAI,CAAC,mBAAmB,GAAG,sJAAA,CAAA,OAAI;QAC/B,IAAI,CAAC,eAAe,GAAG,sJAAA,CAAA,OAAI;QAC3B,IAAI,CAAC,QAAQ,GAAG,IAAI,gMAAA,CAAA,4BAAyB,CAAC;IAClD;IACA,QAAQ;QACJ,kFAAkF;QAClF,2CAA2C;QAC3C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QAC3C,IAAI,cAAc;YACd,IAAI,CAAC,mBAAmB,GAAG,aAAa,SAAS,CAAC,IAAI,CAAC,QAAQ;QACnE;QACA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,MAAM,sJAAA,CAAA,OAAI;IAC/D;IACA,UAAU;QACN,IAAI,CAAC,mBAAmB;QACxB,IAAI,CAAC,eAAe;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2568, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/gestures/pan/index.mjs"], "sourcesContent": ["import { frame } from 'motion-dom';\nimport { noop } from 'motion-utils';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { Feature } from '../../motion/features/Feature.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { PanSession } from './PanSession.mjs';\n\nconst asyncHandler = (handler) => (event, info) => {\n    if (handler) {\n        frame.postRender(() => handler(event, info));\n    }\n};\nclass PanGesture extends Feature {\n    constructor() {\n        super(...arguments);\n        this.removePointerDownListener = noop;\n    }\n    onPointerDown(pointerDownEvent) {\n        this.session = new PanSession(pointerDownEvent, this.createPanHandlers(), {\n            transformPagePoint: this.node.getTransformPagePoint(),\n            contextWindow: getContextWindow(this.node),\n        });\n    }\n    createPanHandlers() {\n        const { onPanSessionStart, onPanStart, onPan, onPanEnd } = this.node.getProps();\n        return {\n            onSessionStart: asyncHandler(onPanSessionStart),\n            onStart: asyncHandler(onPanStart),\n            onMove: onPan,\n            onEnd: (event, info) => {\n                delete this.session;\n                if (onPanEnd) {\n                    frame.postRender(() => onPanEnd(event, info));\n                }\n            },\n        };\n    }\n    mount() {\n        this.removePointerDownListener = addPointerEvent(this.node.current, \"pointerdown\", (event) => this.onPointerDown(event));\n    }\n    update() {\n        this.session && this.session.updateHandlers(this.createPanHandlers());\n    }\n    unmount() {\n        this.removePointerDownListener();\n        this.session && this.session.end();\n    }\n}\n\nexport { PanGesture };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,eAAe,CAAC,UAAY,CAAC,OAAO;QACtC,IAAI,SAAS;YACT,kKAAA,CAAA,QAAK,CAAC,UAAU,CAAC,IAAM,QAAQ,OAAO;QAC1C;IACJ;AACA,MAAM,mBAAmB,gLAAA,CAAA,UAAO;IAC5B,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,yBAAyB,GAAG,sJAAA,CAAA,OAAI;IACzC;IACA,cAAc,gBAAgB,EAAE;QAC5B,IAAI,CAAC,OAAO,GAAG,IAAI,gLAAA,CAAA,aAAU,CAAC,kBAAkB,IAAI,CAAC,iBAAiB,IAAI;YACtE,oBAAoB,IAAI,CAAC,IAAI,CAAC,qBAAqB;YACnD,eAAe,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,IAAI;QAC7C;IACJ;IACA,oBAAoB;QAChB,MAAM,EAAE,iBAAiB,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QAC7E,OAAO;YACH,gBAAgB,aAAa;YAC7B,SAAS,aAAa;YACtB,QAAQ;YACR,OAAO,CAAC,OAAO;gBACX,OAAO,IAAI,CAAC,OAAO;gBACnB,IAAI,UAAU;oBACV,kKAAA,CAAA,QAAK,CAAC,UAAU,CAAC,IAAM,SAAS,OAAO;gBAC3C;YACJ;QACJ;IACJ;IACA,QAAQ;QACJ,IAAI,CAAC,yBAAyB,GAAG,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,QAAU,IAAI,CAAC,aAAa,CAAC;IACrH;IACA,SAAS;QACL,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB;IACtE;IACA,UAAU;QACN,IAAI,CAAC,yBAAyB;QAC9B,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG;IACpC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2631, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/context/PresenceContext.mjs"], "sourcesContent": ["\"use client\";\nimport { createContext } from 'react';\n\n/**\n * @public\n */\nconst PresenceContext = \n/* @__PURE__ */ createContext(null);\n\nexport { PresenceContext };\n"], "names": [], "mappings": ";;;AACA;AADA;;AAGA;;CAEC,GACD,MAAM,kBACN,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2647, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs"], "sourcesContent": ["import { useContext, useId, useEffect, useCallback } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\n\n/**\n * When a component is the child of `AnimatePresence`, it can use `usePresence`\n * to access information about whether it's still present in the React tree.\n *\n * ```jsx\n * import { usePresence } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const [isPresent, safeToRemove] = usePresence()\n *\n *   useEffect(() => {\n *     !isPresent && setTimeout(safeToRemove, 1000)\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * If `isPresent` is `false`, it means that a component has been removed the tree, but\n * `AnimatePresence` won't really remove it until `safeToRemove` has been called.\n *\n * @public\n */\nfunction usePresence(subscribe = true) {\n    const context = useContext(PresenceContext);\n    if (context === null)\n        return [true, null];\n    const { isPresent, onExitComplete, register } = context;\n    // It's safe to call the following hooks conditionally (after an early return) because the context will always\n    // either be null or non-null for the lifespan of the component.\n    const id = useId();\n    useEffect(() => {\n        if (subscribe) {\n            return register(id);\n        }\n    }, [subscribe]);\n    const safeToRemove = useCallback(() => subscribe && onExitComplete && onExitComplete(id), [id, onExitComplete, subscribe]);\n    return !isPresent && onExitComplete ? [false, safeToRemove] : [true];\n}\n/**\n * Similar to `usePresence`, except `useIsPresent` simply returns whether or not the component is present.\n * There is no `safeToRemove` function.\n *\n * ```jsx\n * import { useIsPresent } from \"framer-motion\"\n *\n * export const Component = () => {\n *   const isPresent = useIsPresent()\n *\n *   useEffect(() => {\n *     !isPresent && console.log(\"I've been removed!\")\n *   }, [isPresent])\n *\n *   return <div />\n * }\n * ```\n *\n * @public\n */\nfunction useIsPresent() {\n    return isPresent(useContext(PresenceContext));\n}\nfunction isPresent(context) {\n    return context === null ? true : context.isPresent;\n}\n\nexport { isPresent, useIsPresent, usePresence };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,SAAS,YAAY,YAAY,IAAI;IACjC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,6KAAA,CAAA,kBAAe;IAC1C,IAAI,YAAY,MACZ,OAAO;QAAC;QAAM;KAAK;IACvB,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,QAAQ,EAAE,GAAG;IAChD,8GAA8G;IAC9G,gEAAgE;IAChE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,WAAW;YACX,OAAO,SAAS;QACpB;IACJ,GAAG;QAAC;KAAU;IACd,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,aAAa,kBAAkB,eAAe,KAAK;QAAC;QAAI;QAAgB;KAAU;IACzH,OAAO,CAAC,aAAa,iBAAiB;QAAC;QAAO;KAAa,GAAG;QAAC;KAAK;AACxE;AACA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,SAAS;IACL,OAAO,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,6KAAA,CAAA,kBAAe;AAC/C;AACA,SAAS,UAAU,OAAO;IACtB,OAAO,YAAY,OAAO,OAAO,QAAQ,SAAS;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2739, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs"], "sourcesContent": ["\"use client\";\nimport { createContext } from 'react';\n\nconst LayoutGroupContext = createContext({});\n\nexport { LayoutGroupContext };\n"], "names": [], "mappings": ";;;AACA;AADA;;AAGA,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2753, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs"], "sourcesContent": ["\"use client\";\nimport { createContext } from 'react';\n\n/**\n * Internal, exported only for usage in Framer\n */\nconst SwitchLayoutGroupContext = createContext({});\n\nexport { SwitchLayoutGroupContext };\n"], "names": [], "mappings": ";;;AACA;AADA;;AAGA;;CAEC,GACD,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2769, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/node/state.mjs"], "sourcesContent": ["/**\n * This should only ever be modified on the client otherwise it'll\n * persist through server requests. If we need instanced states we\n * could lazy-init via root.\n */\nconst globalProjectionState = {\n    /**\n     * Global flag as to whether the tree has animated since the last time\n     * we resized the window\n     */\n    hasAnimatedSinceResize: true,\n    /**\n     * We set this to true once, on the first update. Any nodes added to the tree beyond that\n     * update will be given a `data-projection-id` attribute.\n     */\n    hasEverUpdated: false,\n};\n\nexport { globalProjectionState };\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACD,MAAM,wBAAwB;IAC1B;;;KAGC,GACD,wBAAwB;IACxB;;;KAGC,GACD,gBAAgB;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2793, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/styles/scale-border-radius.mjs"], "sourcesContent": ["import { px } from 'motion-dom';\n\nfunction pixelsToPercent(pixels, axis) {\n    if (axis.max === axis.min)\n        return 0;\n    return (pixels / (axis.max - axis.min)) * 100;\n}\n/**\n * We always correct borderRadius as a percentage rather than pixels to reduce paints.\n * For example, if you are projecting a box that is 100px wide with a 10px borderRadius\n * into a box that is 200px wide with a 20px borderRadius, that is actually a 10%\n * borderRadius in both states. If we animate between the two in pixels that will trigger\n * a paint each time. If we animate between the two in percentage we'll avoid a paint.\n */\nconst correctBorderRadius = {\n    correct: (latest, node) => {\n        if (!node.target)\n            return latest;\n        /**\n         * If latest is a string, if it's a percentage we can return immediately as it's\n         * going to be stretched appropriately. Otherwise, if it's a pixel, convert it to a number.\n         */\n        if (typeof latest === \"string\") {\n            if (px.test(latest)) {\n                latest = parseFloat(latest);\n            }\n            else {\n                return latest;\n            }\n        }\n        /**\n         * If latest is a number, it's a pixel value. We use the current viewportBox to calculate that\n         * pixel value as a percentage of each axis\n         */\n        const x = pixelsToPercent(latest, node.target.x);\n        const y = pixelsToPercent(latest, node.target.y);\n        return `${x}% ${y}%`;\n    },\n};\n\nexport { correctBorderRadius, pixelsToPercent };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,SAAS,gBAAgB,MAAM,EAAE,IAAI;IACjC,IAAI,KAAK,GAAG,KAAK,KAAK,GAAG,EACrB,OAAO;IACX,OAAO,AAAC,SAAS,CAAC,KAAK,GAAG,GAAG,KAAK,GAAG,IAAK;AAC9C;AACA;;;;;;CAMC,GACD,MAAM,sBAAsB;IACxB,SAAS,CAAC,QAAQ;QACd,IAAI,CAAC,KAAK,MAAM,EACZ,OAAO;QACX;;;SAGC,GACD,IAAI,OAAO,WAAW,UAAU;YAC5B,IAAI,kLAAA,CAAA,KAAE,CAAC,IAAI,CAAC,SAAS;gBACjB,SAAS,WAAW;YACxB,OACK;gBACD,OAAO;YACX;QACJ;QACA;;;SAGC,GACD,MAAM,IAAI,gBAAgB,QAAQ,KAAK,MAAM,CAAC,CAAC;QAC/C,MAAM,IAAI,gBAAgB,QAAQ,KAAK,MAAM,CAAC,CAAC;QAC/C,OAAO,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2837, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/styles/scale-box-shadow.mjs"], "sourcesContent": ["import { complex, mixNumber } from 'motion-dom';\n\nconst correctBoxShadow = {\n    correct: (latest, { treeScale, projectionDelta }) => {\n        const original = latest;\n        const shadow = complex.parse(latest);\n        // TODO: Doesn't support multiple shadows\n        if (shadow.length > 5)\n            return original;\n        const template = complex.createTransformer(latest);\n        const offset = typeof shadow[0] !== \"number\" ? 1 : 0;\n        // Calculate the overall context scale\n        const xScale = projectionDelta.x.scale * treeScale.x;\n        const yScale = projectionDelta.y.scale * treeScale.y;\n        shadow[0 + offset] /= xScale;\n        shadow[1 + offset] /= yScale;\n        /**\n         * Ideally we'd correct x and y scales individually, but because blur and\n         * spread apply to both we have to take a scale average and apply that instead.\n         * We could potentially improve the outcome of this by incorporating the ratio between\n         * the two scales.\n         */\n        const averageScale = mixNumber(xScale, yScale, 0.5);\n        // Blur\n        if (typeof shadow[2 + offset] === \"number\")\n            shadow[2 + offset] /= averageScale;\n        // Spread\n        if (typeof shadow[3 + offset] === \"number\")\n            shadow[3 + offset] /= averageScale;\n        return template(shadow);\n    },\n};\n\nexport { correctBoxShadow };\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,MAAM,mBAAmB;IACrB,SAAS,CAAC,QAAQ,EAAE,SAAS,EAAE,eAAe,EAAE;QAC5C,MAAM,WAAW;QACjB,MAAM,SAAS,kLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAC7B,yCAAyC;QACzC,IAAI,OAAO,MAAM,GAAG,GAChB,OAAO;QACX,MAAM,WAAW,kLAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC;QAC3C,MAAM,SAAS,OAAO,MAAM,CAAC,EAAE,KAAK,WAAW,IAAI;QACnD,sCAAsC;QACtC,MAAM,SAAS,gBAAgB,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC;QACpD,MAAM,SAAS,gBAAgB,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC;QACpD,MAAM,CAAC,IAAI,OAAO,IAAI;QACtB,MAAM,CAAC,IAAI,OAAO,IAAI;QACtB;;;;;SAKC,GACD,MAAM,eAAe,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,QAAQ;QAC/C,OAAO;QACP,IAAI,OAAO,MAAM,CAAC,IAAI,OAAO,KAAK,UAC9B,MAAM,CAAC,IAAI,OAAO,IAAI;QAC1B,SAAS;QACT,IAAI,OAAO,MAAM,CAAC,IAAI,OAAO,KAAK,UAC9B,MAAM,CAAC,IAAI,OAAO,IAAI;QAC1B,OAAO,SAAS;IACpB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2876, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs"], "sourcesContent": ["import { isCSSVariableName } from 'motion-dom';\n\nconst scaleCorrectors = {};\nfunction addScaleCorrector(correctors) {\n    for (const key in correctors) {\n        scaleCorrectors[key] = correctors[key];\n        if (isCSSVariableName(key)) {\n            scaleCorrectors[key].isCSSVariable = true;\n        }\n    }\n}\n\nexport { addScaleCorrector, scaleCorrectors };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB,CAAC;AACzB,SAAS,kBAAkB,UAAU;IACjC,IAAK,MAAM,OAAO,WAAY;QAC1B,eAAe,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI;QACtC,IAAI,CAAA,GAAA,2LAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM;YACxB,eAAe,CAAC,IAAI,CAAC,aAAa,GAAG;QACzC;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2898, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport { frame, microtask } from 'motion-dom';\nimport { useContext, Component } from 'react';\nimport { usePresence } from '../../../components/AnimatePresence/use-presence.mjs';\nimport { LayoutGroupContext } from '../../../context/LayoutGroupContext.mjs';\nimport { SwitchLayoutGroupContext } from '../../../context/SwitchLayoutGroupContext.mjs';\nimport { globalProjectionState } from '../../../projection/node/state.mjs';\nimport { correctBorderRadius } from '../../../projection/styles/scale-border-radius.mjs';\nimport { correctBoxShadow } from '../../../projection/styles/scale-box-shadow.mjs';\nimport { addScaleCorrector } from '../../../projection/styles/scale-correction.mjs';\n\n/**\n * Track whether we've taken any snapshots yet. If not,\n * we can safely skip notification of didUpdate.\n */\nlet hasTakenAnySnapshot = false;\nclass MeasureLayoutWithContext extends Component {\n    /**\n     * This only mounts projection nodes for components that\n     * need measuring, we might want to do it for all components\n     * in order to incorporate transforms\n     */\n    componentDidMount() {\n        const { visualElement, layoutGroup, switchLayoutGroup, layoutId } = this.props;\n        const { projection } = visualElement;\n        addScaleCorrector(defaultScaleCorrectors);\n        if (projection) {\n            if (layoutGroup.group)\n                layoutGroup.group.add(projection);\n            if (switchLayoutGroup && switchLayoutGroup.register && layoutId) {\n                switchLayoutGroup.register(projection);\n            }\n            if (hasTakenAnySnapshot) {\n                projection.root.didUpdate();\n            }\n            projection.addEventListener(\"animationComplete\", () => {\n                this.safeToRemove();\n            });\n            projection.setOptions({\n                ...projection.options,\n                onExitComplete: () => this.safeToRemove(),\n            });\n        }\n        globalProjectionState.hasEverUpdated = true;\n    }\n    getSnapshotBeforeUpdate(prevProps) {\n        const { layoutDependency, visualElement, drag, isPresent } = this.props;\n        const { projection } = visualElement;\n        if (!projection)\n            return null;\n        /**\n         * TODO: We use this data in relegate to determine whether to\n         * promote a previous element. There's no guarantee its presence data\n         * will have updated by this point - if a bug like this arises it will\n         * have to be that we markForRelegation and then find a new lead some other way,\n         * perhaps in didUpdate\n         */\n        projection.isPresent = isPresent;\n        hasTakenAnySnapshot = true;\n        if (drag ||\n            prevProps.layoutDependency !== layoutDependency ||\n            layoutDependency === undefined ||\n            prevProps.isPresent !== isPresent) {\n            projection.willUpdate();\n        }\n        else {\n            this.safeToRemove();\n        }\n        if (prevProps.isPresent !== isPresent) {\n            if (isPresent) {\n                projection.promote();\n            }\n            else if (!projection.relegate()) {\n                /**\n                 * If there's another stack member taking over from this one,\n                 * it's in charge of the exit animation and therefore should\n                 * be in charge of the safe to remove. Otherwise we call it here.\n                 */\n                frame.postRender(() => {\n                    const stack = projection.getStack();\n                    if (!stack || !stack.members.length) {\n                        this.safeToRemove();\n                    }\n                });\n            }\n        }\n        return null;\n    }\n    componentDidUpdate() {\n        const { projection } = this.props.visualElement;\n        if (projection) {\n            projection.root.didUpdate();\n            microtask.postRender(() => {\n                if (!projection.currentAnimation && projection.isLead()) {\n                    this.safeToRemove();\n                }\n            });\n        }\n    }\n    componentWillUnmount() {\n        const { visualElement, layoutGroup, switchLayoutGroup: promoteContext, } = this.props;\n        const { projection } = visualElement;\n        if (projection) {\n            projection.scheduleCheckAfterUnmount();\n            if (layoutGroup && layoutGroup.group)\n                layoutGroup.group.remove(projection);\n            if (promoteContext && promoteContext.deregister)\n                promoteContext.deregister(projection);\n        }\n    }\n    safeToRemove() {\n        const { safeToRemove } = this.props;\n        safeToRemove && safeToRemove();\n    }\n    render() {\n        return null;\n    }\n}\nfunction MeasureLayout(props) {\n    const [isPresent, safeToRemove] = usePresence();\n    const layoutGroup = useContext(LayoutGroupContext);\n    return (jsx(MeasureLayoutWithContext, { ...props, layoutGroup: layoutGroup, switchLayoutGroup: useContext(SwitchLayoutGroupContext), isPresent: isPresent, safeToRemove: safeToRemove }));\n}\nconst defaultScaleCorrectors = {\n    borderRadius: {\n        ...correctBorderRadius,\n        applyTo: [\n            \"borderTopLeftRadius\",\n            \"borderTopRightRadius\",\n            \"borderBottomLeftRadius\",\n            \"borderBottomRightRadius\",\n        ],\n    },\n    borderTopLeftRadius: correctBorderRadius,\n    borderTopRightRadius: correctBorderRadius,\n    borderBottomLeftRadius: correctBorderRadius,\n    borderBottomRightRadius: correctBorderRadius,\n    boxShadow: correctBoxShadow,\n};\n\nexport { MeasureLayout };\n"], "names": [], "mappings": ";;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYA;;;CAGC,GACD,IAAI,sBAAsB;AAC1B,MAAM,iCAAiC,qMAAA,CAAA,YAAS;IAC5C;;;;KAIC,GACD,oBAAoB;QAChB,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,iBAAiB,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,KAAK;QAC9E,MAAM,EAAE,UAAU,EAAE,GAAG;QACvB,CAAA,GAAA,8LAAA,CAAA,oBAAiB,AAAD,EAAE;QAClB,IAAI,YAAY;YACZ,IAAI,YAAY,KAAK,EACjB,YAAY,KAAK,CAAC,GAAG,CAAC;YAC1B,IAAI,qBAAqB,kBAAkB,QAAQ,IAAI,UAAU;gBAC7D,kBAAkB,QAAQ,CAAC;YAC/B;YACA,IAAI,qBAAqB;gBACrB,WAAW,IAAI,CAAC,SAAS;YAC7B;YACA,WAAW,gBAAgB,CAAC,qBAAqB;gBAC7C,IAAI,CAAC,YAAY;YACrB;YACA,WAAW,UAAU,CAAC;gBAClB,GAAG,WAAW,OAAO;gBACrB,gBAAgB,IAAM,IAAI,CAAC,YAAY;YAC3C;QACJ;QACA,8KAAA,CAAA,wBAAqB,CAAC,cAAc,GAAG;IAC3C;IACA,wBAAwB,SAAS,EAAE;QAC/B,MAAM,EAAE,gBAAgB,EAAE,aAAa,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,KAAK;QACvE,MAAM,EAAE,UAAU,EAAE,GAAG;QACvB,IAAI,CAAC,YACD,OAAO;QACX;;;;;;SAMC,GACD,WAAW,SAAS,GAAG;QACvB,sBAAsB;QACtB,IAAI,QACA,UAAU,gBAAgB,KAAK,oBAC/B,qBAAqB,aACrB,UAAU,SAAS,KAAK,WAAW;YACnC,WAAW,UAAU;QACzB,OACK;YACD,IAAI,CAAC,YAAY;QACrB;QACA,IAAI,UAAU,SAAS,KAAK,WAAW;YACnC,IAAI,WAAW;gBACX,WAAW,OAAO;YACtB,OACK,IAAI,CAAC,WAAW,QAAQ,IAAI;gBAC7B;;;;iBAIC,GACD,kKAAA,CAAA,QAAK,CAAC,UAAU,CAAC;oBACb,MAAM,QAAQ,WAAW,QAAQ;oBACjC,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC,MAAM,EAAE;wBACjC,IAAI,CAAC,YAAY;oBACrB;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,qBAAqB;QACjB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa;QAC/C,IAAI,YAAY;YACZ,WAAW,IAAI,CAAC,SAAS;YACzB,sKAAA,CAAA,YAAS,CAAC,UAAU,CAAC;gBACjB,IAAI,CAAC,WAAW,gBAAgB,IAAI,WAAW,MAAM,IAAI;oBACrD,IAAI,CAAC,YAAY;gBACrB;YACJ;QACJ;IACJ;IACA,uBAAuB;QACnB,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,mBAAmB,cAAc,EAAG,GAAG,IAAI,CAAC,KAAK;QACrF,MAAM,EAAE,UAAU,EAAE,GAAG;QACvB,IAAI,YAAY;YACZ,WAAW,yBAAyB;YACpC,IAAI,eAAe,YAAY,KAAK,EAChC,YAAY,KAAK,CAAC,MAAM,CAAC;YAC7B,IAAI,kBAAkB,eAAe,UAAU,EAC3C,eAAe,UAAU,CAAC;QAClC;IACJ;IACA,eAAe;QACX,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,KAAK;QACnC,gBAAgB;IACpB;IACA,SAAS;QACL,OAAO;IACX;AACJ;AACA,SAAS,cAAc,KAAK;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,mMAAA,CAAA,cAAW,AAAD;IAC5C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gLAAA,CAAA,qBAAkB;IACjD,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,0BAA0B;QAAE,GAAG,KAAK;QAAE,aAAa;QAAa,mBAAmB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,sLAAA,CAAA,2BAAwB;QAAG,WAAW;QAAW,cAAc;IAAa;AAC1L;AACA,MAAM,yBAAyB;IAC3B,cAAc;QACV,GAAG,oMAAA,CAAA,sBAAmB;QACtB,SAAS;YACL;YACA;YACA;YACA;SACH;IACL;IACA,qBAAqB,oMAAA,CAAA,sBAAmB;IACxC,sBAAsB,oMAAA,CAAA,sBAAmB;IACzC,wBAAwB,oMAAA,CAAA,sBAAmB;IAC3C,yBAAyB,oMAAA,CAAA,sBAAmB;IAC5C,WAAW,iMAAA,CAAA,mBAAgB;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3051, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/animation/animate/single-value.mjs"], "sourcesContent": ["import { isMotionValue, motionValue } from 'motion-dom';\nimport { animateMotionValue } from '../interfaces/motion-value.mjs';\n\nfunction animateSingleValue(value, keyframes, options) {\n    const motionValue$1 = isMotionValue(value) ? value : motionValue(value);\n    motionValue$1.start(animateMotionValue(\"\", motionValue$1, keyframes, options));\n    return motionValue$1.animation;\n}\n\nexport { animateSingleValue };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA,SAAS,mBAAmB,KAAK,EAAE,SAAS,EAAE,OAAO;IACjD,MAAM,gBAAgB,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,QAAQ,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACjE,cAAc,KAAK,CAAC,CAAA,GAAA,6LAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,eAAe,WAAW;IACrE,OAAO,cAAc,SAAS;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3071, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs"], "sourcesContent": ["const compareByDepth = (a, b) => a.depth - b.depth;\n\nexport { compareByDepth };\n"], "names": [], "mappings": ";;;AAAA,MAAM,iBAAiB,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3082, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/utils/flat-tree.mjs"], "sourcesContent": ["import { addUniqueItem, removeItem } from 'motion-utils';\nimport { compareByDepth } from './compare-by-depth.mjs';\n\nclass FlatTree {\n    constructor() {\n        this.children = [];\n        this.isDirty = false;\n    }\n    add(child) {\n        addUniqueItem(this.children, child);\n        this.isDirty = true;\n    }\n    remove(child) {\n        removeItem(this.children, child);\n        this.isDirty = true;\n    }\n    forEach(callback) {\n        this.isDirty && this.children.sort(compareByDepth);\n        this.isDirty = false;\n        this.children.forEach(callback);\n    }\n}\n\nexport { FlatTree };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM;IACF,aAAc;QACV,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,IAAI,KAAK,EAAE;QACP,CAAA,GAAA,uJAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,QAAQ,EAAE;QAC7B,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,OAAO,KAAK,EAAE;QACV,CAAA,GAAA,uJAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,QAAQ,EAAE;QAC1B,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,QAAQ,QAAQ,EAAE;QACd,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,4LAAA,CAAA,iBAAc;QACjD,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;IAC1B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3115, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/utils/delay.mjs"], "sourcesContent": ["import { time, frame, cancelFrame } from 'motion-dom';\nimport { secondsToMilliseconds } from 'motion-utils';\n\n/**\n * Timeout defined in ms\n */\nfunction delay(callback, timeout) {\n    const start = time.now();\n    const checkElapsed = ({ timestamp }) => {\n        const elapsed = timestamp - start;\n        if (elapsed >= timeout) {\n            cancelFrame(checkElapsed);\n            callback(elapsed - timeout);\n        }\n    };\n    frame.setup(checkElapsed, true);\n    return () => cancelFrame(checkElapsed);\n}\nfunction delayInSeconds(callback, timeout) {\n    return delay(callback, secondsToMilliseconds(timeout));\n}\n\nexport { delay, delayInSeconds };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA;;CAEC,GACD,SAAS,MAAM,QAAQ,EAAE,OAAO;IAC5B,MAAM,QAAQ,yKAAA,CAAA,OAAI,CAAC,GAAG;IACtB,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE;QAC/B,MAAM,UAAU,YAAY;QAC5B,IAAI,WAAW,SAAS;YACpB,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE;YACZ,SAAS,UAAU;QACvB;IACJ;IACA,kKAAA,CAAA,QAAK,CAAC,KAAK,CAAC,cAAc;IAC1B,OAAO,IAAM,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE;AAC7B;AACA,SAAS,eAAe,QAAQ,EAAE,OAAO;IACrC,OAAO,MAAM,UAAU,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3148, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs"], "sourcesContent": ["import { isMotionValue } from 'motion-dom';\n\n/**\n * If the provided value is a MotionValue, this returns the actual value, otherwise just the value itself\n *\n * TODO: Remove and move to library\n */\nfunction resolveMotionValue(value) {\n    return isMotionValue(value) ? value.get() : value;\n}\n\nexport { resolveMotionValue };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;;CAIC,GACD,SAAS,mBAAmB,KAAK;IAC7B,OAAO,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,MAAM,GAAG,KAAK;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3167, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs"], "sourcesContent": ["import { mixNumber, percent, px } from 'motion-dom';\nimport { progress, circOut, noop } from 'motion-utils';\n\nconst borders = [\"TopLeft\", \"TopRight\", \"BottomLeft\", \"BottomRight\"];\nconst numBorders = borders.length;\nconst asNumber = (value) => typeof value === \"string\" ? parseFloat(value) : value;\nconst isPx = (value) => typeof value === \"number\" || px.test(value);\nfunction mixValues(target, follow, lead, progress, shouldCrossfadeOpacity, isOnlyMember) {\n    if (shouldCrossfadeOpacity) {\n        target.opacity = mixNumber(0, lead.opacity ?? 1, easeCrossfadeIn(progress));\n        target.opacityExit = mixNumber(follow.opacity ?? 1, 0, easeCrossfadeOut(progress));\n    }\n    else if (isOnlyMember) {\n        target.opacity = mixNumber(follow.opacity ?? 1, lead.opacity ?? 1, progress);\n    }\n    /**\n     * Mix border radius\n     */\n    for (let i = 0; i < numBorders; i++) {\n        const borderLabel = `border${borders[i]}Radius`;\n        let followRadius = getRadius(follow, borderLabel);\n        let leadRadius = getRadius(lead, borderLabel);\n        if (followRadius === undefined && leadRadius === undefined)\n            continue;\n        followRadius || (followRadius = 0);\n        leadRadius || (leadRadius = 0);\n        const canMix = followRadius === 0 ||\n            leadRadius === 0 ||\n            isPx(followRadius) === isPx(leadRadius);\n        if (canMix) {\n            target[borderLabel] = Math.max(mixNumber(asNumber(followRadius), asNumber(leadRadius), progress), 0);\n            if (percent.test(leadRadius) || percent.test(followRadius)) {\n                target[borderLabel] += \"%\";\n            }\n        }\n        else {\n            target[borderLabel] = leadRadius;\n        }\n    }\n    /**\n     * Mix rotation\n     */\n    if (follow.rotate || lead.rotate) {\n        target.rotate = mixNumber(follow.rotate || 0, lead.rotate || 0, progress);\n    }\n}\nfunction getRadius(values, radiusName) {\n    return values[radiusName] !== undefined\n        ? values[radiusName]\n        : values.borderRadius;\n}\n// /**\n//  * We only want to mix the background color if there's a follow element\n//  * that we're not crossfading opacity between. For instance with switch\n//  * AnimateSharedLayout animations, this helps the illusion of a continuous\n//  * element being animated but also cuts down on the number of paints triggered\n//  * for elements where opacity is doing that work for us.\n//  */\n// if (\n//     !hasFollowElement &&\n//     latestLeadValues.backgroundColor &&\n//     latestFollowValues.backgroundColor\n// ) {\n//     /**\n//      * This isn't ideal performance-wise as mixColor is creating a new function every frame.\n//      * We could probably create a mixer that runs at the start of the animation but\n//      * the idea behind the crossfader is that it runs dynamically between two potentially\n//      * changing targets (ie opacity or borderRadius may be animating independently via variants)\n//      */\n//     leadState.backgroundColor = followState.backgroundColor = mixColor(\n//         latestFollowValues.backgroundColor as string,\n//         latestLeadValues.backgroundColor as string\n//     )(p)\n// }\nconst easeCrossfadeIn = /*@__PURE__*/ compress(0, 0.5, circOut);\nconst easeCrossfadeOut = /*@__PURE__*/ compress(0.5, 0.95, noop);\nfunction compress(min, max, easing) {\n    return (p) => {\n        // Could replace ifs with clamp\n        if (p < min)\n            return 0;\n        if (p > max)\n            return 1;\n        return easing(progress(min, max, p));\n    };\n}\n\nexport { mixValues };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;AAAA;;;AAEA,MAAM,UAAU;IAAC;IAAW;IAAY;IAAc;CAAc;AACpE,MAAM,aAAa,QAAQ,MAAM;AACjC,MAAM,WAAW,CAAC,QAAU,OAAO,UAAU,WAAW,WAAW,SAAS;AAC5E,MAAM,OAAO,CAAC,QAAU,OAAO,UAAU,YAAY,kLAAA,CAAA,KAAE,CAAC,IAAI,CAAC;AAC7D,SAAS,UAAU,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,sBAAsB,EAAE,YAAY;IACnF,IAAI,wBAAwB;QACxB,OAAO,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,GAAG,KAAK,OAAO,IAAI,GAAG,gBAAgB;QACjE,OAAO,WAAW,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO,IAAI,GAAG,GAAG,iBAAiB;IAC5E,OACK,IAAI,cAAc;QACnB,OAAO,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,OAAO,OAAO,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG;IACvE;IACA;;KAEC,GACD,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACjC,MAAM,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC;QAC/C,IAAI,eAAe,UAAU,QAAQ;QACrC,IAAI,aAAa,UAAU,MAAM;QACjC,IAAI,iBAAiB,aAAa,eAAe,WAC7C;QACJ,gBAAgB,CAAC,eAAe,CAAC;QACjC,cAAc,CAAC,aAAa,CAAC;QAC7B,MAAM,SAAS,iBAAiB,KAC5B,eAAe,KACf,KAAK,kBAAkB,KAAK;QAChC,IAAI,QAAQ;YACR,MAAM,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,SAAS,eAAe,SAAS,aAAa,WAAW;YAClG,IAAI,kLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,eAAe,kLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,eAAe;gBACxD,MAAM,CAAC,YAAY,IAAI;YAC3B;QACJ,OACK;YACD,MAAM,CAAC,YAAY,GAAG;QAC1B;IACJ;IACA;;KAEC,GACD,IAAI,OAAO,MAAM,IAAI,KAAK,MAAM,EAAE;QAC9B,OAAO,MAAM,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,OAAO,MAAM,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG;IACpE;AACJ;AACA,SAAS,UAAU,MAAM,EAAE,UAAU;IACjC,OAAO,MAAM,CAAC,WAAW,KAAK,YACxB,MAAM,CAAC,WAAW,GAClB,OAAO,YAAY;AAC7B;AACA,MAAM;AACN,0EAA0E;AAC1E,0EAA0E;AAC1E,6EAA6E;AAC7E,iFAAiF;AACjF,2DAA2D;AAC3D,MAAM;AACN,OAAO;AACP,2BAA2B;AAC3B,0CAA0C;AAC1C,yCAAyC;AACzC,MAAM;AACN,UAAU;AACV,+FAA+F;AAC/F,sFAAsF;AACtF,4FAA4F;AAC5F,mGAAmG;AACnG,UAAU;AACV,0EAA0E;AAC1E,wDAAwD;AACxD,qDAAqD;AACrD,WAAW;AACX,IAAI;AACJ,MAAM,kBAAkB,WAAW,GAAG,SAAS,GAAG,KAAK,gKAAA,CAAA,UAAO;AAC9D,MAAM,mBAAmB,WAAW,GAAG,SAAS,KAAK,MAAM,sJAAA,CAAA,OAAI;AAC/D,SAAS,SAAS,GAAG,EAAE,GAAG,EAAE,MAAM;IAC9B,OAAO,CAAC;QACJ,+BAA+B;QAC/B,IAAI,IAAI,KACJ,OAAO;QACX,IAAI,IAAI,KACJ,OAAO;QACX,OAAO,OAAO,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK;IACrC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3261, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/geometry/copy.mjs"], "sourcesContent": ["/**\n * Reset an axis to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisInto(axis, originAxis) {\n    axis.min = originAxis.min;\n    axis.max = originAxis.max;\n}\n/**\n * Reset a box to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyBoxInto(box, originBox) {\n    copyAxisInto(box.x, originBox.x);\n    copyAxisInto(box.y, originBox.y);\n}\n/**\n * Reset a delta to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisDeltaInto(delta, originDelta) {\n    delta.translate = originDelta.translate;\n    delta.scale = originDelta.scale;\n    delta.originPoint = originDelta.originPoint;\n    delta.origin = originDelta.origin;\n}\n\nexport { copyAxisDeltaInto, copyAxisInto, copyBoxInto };\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;AACD,SAAS,aAAa,IAAI,EAAE,UAAU;IAClC,KAAK,GAAG,GAAG,WAAW,GAAG;IACzB,KAAK,GAAG,GAAG,WAAW,GAAG;AAC7B;AACA;;;;CAIC,GACD,SAAS,YAAY,GAAG,EAAE,SAAS;IAC/B,aAAa,IAAI,CAAC,EAAE,UAAU,CAAC;IAC/B,aAAa,IAAI,CAAC,EAAE,UAAU,CAAC;AACnC;AACA;;;;CAIC,GACD,SAAS,kBAAkB,KAAK,EAAE,WAAW;IACzC,MAAM,SAAS,GAAG,YAAY,SAAS;IACvC,MAAM,KAAK,GAAG,YAAY,KAAK;IAC/B,MAAM,WAAW,GAAG,YAAY,WAAW;IAC3C,MAAM,MAAM,GAAG,YAAY,MAAM;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3299, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs"], "sourcesContent": ["import { percent, mixNumber } from 'motion-dom';\nimport { scalePoint } from './delta-apply.mjs';\n\n/**\n * Remove a delta from a point. This is essentially the steps of applyPointDelta in reverse\n */\nfunction removePointDelta(point, translate, scale, originPoint, boxScale) {\n    point -= translate;\n    point = scalePoint(point, 1 / scale, originPoint);\n    if (boxScale !== undefined) {\n        point = scalePoint(point, 1 / boxScale, originPoint);\n    }\n    return point;\n}\n/**\n * Remove a delta from an axis. This is essentially the steps of applyAxisDelta in reverse\n */\nfunction removeAxisDelta(axis, translate = 0, scale = 1, origin = 0.5, boxScale, originAxis = axis, sourceAxis = axis) {\n    if (percent.test(translate)) {\n        translate = parseFloat(translate);\n        const relativeProgress = mixNumber(sourceAxis.min, sourceAxis.max, translate / 100);\n        translate = relativeProgress - sourceAxis.min;\n    }\n    if (typeof translate !== \"number\")\n        return;\n    let originPoint = mixNumber(originAxis.min, originAxis.max, origin);\n    if (axis === originAxis)\n        originPoint -= translate;\n    axis.min = removePointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = removePointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Remove a transforms from an axis. This is essentially the steps of applyAxisTransforms in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeAxisTransforms(axis, transforms, [key, scaleKey, originKey], origin, sourceAxis) {\n    removeAxisDelta(axis, transforms[key], transforms[scaleKey], transforms[originKey], transforms.scale, origin, sourceAxis);\n}\n/**\n * The names of the motion values we want to apply as translation, scale and origin.\n */\nconst xKeys = [\"x\", \"scaleX\", \"originX\"];\nconst yKeys = [\"y\", \"scaleY\", \"originY\"];\n/**\n * Remove a transforms from an box. This is essentially the steps of applyAxisBox in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeBoxTransforms(box, transforms, originBox, sourceBox) {\n    removeAxisTransforms(box.x, transforms, xKeys, originBox ? originBox.x : undefined, sourceBox ? sourceBox.x : undefined);\n    removeAxisTransforms(box.y, transforms, yKeys, originBox ? originBox.y : undefined, sourceBox ? sourceBox.y : undefined);\n}\n\nexport { removeAxisDelta, removeAxisTransforms, removeBoxTransforms, removePointDelta };\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;;;AAEA;;CAEC,GACD,SAAS,iBAAiB,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ;IACpE,SAAS;IACT,QAAQ,CAAA,GAAA,2LAAA,CAAA,aAAU,AAAD,EAAE,OAAO,IAAI,OAAO;IACrC,IAAI,aAAa,WAAW;QACxB,QAAQ,CAAA,GAAA,2LAAA,CAAA,aAAU,AAAD,EAAE,OAAO,IAAI,UAAU;IAC5C;IACA,OAAO;AACX;AACA;;CAEC,GACD,SAAS,gBAAgB,IAAI,EAAE,YAAY,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS,GAAG,EAAE,QAAQ,EAAE,aAAa,IAAI,EAAE,aAAa,IAAI;IACjH,IAAI,kLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,YAAY;QACzB,YAAY,WAAW;QACvB,MAAM,mBAAmB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,WAAW,GAAG,EAAE,WAAW,GAAG,EAAE,YAAY;QAC/E,YAAY,mBAAmB,WAAW,GAAG;IACjD;IACA,IAAI,OAAO,cAAc,UACrB;IACJ,IAAI,cAAc,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,WAAW,GAAG,EAAE,WAAW,GAAG,EAAE;IAC5D,IAAI,SAAS,YACT,eAAe;IACnB,KAAK,GAAG,GAAG,iBAAiB,KAAK,GAAG,EAAE,WAAW,OAAO,aAAa;IACrE,KAAK,GAAG,GAAG,iBAAiB,KAAK,GAAG,EAAE,WAAW,OAAO,aAAa;AACzE;AACA;;;CAGC,GACD,SAAS,qBAAqB,IAAI,EAAE,UAAU,EAAE,CAAC,KAAK,UAAU,UAAU,EAAE,MAAM,EAAE,UAAU;IAC1F,gBAAgB,MAAM,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,UAAU,EAAE,WAAW,KAAK,EAAE,QAAQ;AAClH;AACA;;CAEC,GACD,MAAM,QAAQ;IAAC;IAAK;IAAU;CAAU;AACxC,MAAM,QAAQ;IAAC;IAAK;IAAU;CAAU;AACxC;;;CAGC,GACD,SAAS,oBAAoB,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS;IAC9D,qBAAqB,IAAI,CAAC,EAAE,YAAY,OAAO,YAAY,UAAU,CAAC,GAAG,WAAW,YAAY,UAAU,CAAC,GAAG;IAC9G,qBAAqB,IAAI,CAAC,EAAE,YAAY,OAAO,YAAY,UAAU,CAAC,GAAG,WAAW,YAAY,UAAU,CAAC,GAAG;AAClH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3366, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/geometry/utils.mjs"], "sourcesContent": ["import { calcLength } from './delta-calc.mjs';\n\nfunction isAxisDeltaZero(delta) {\n    return delta.translate === 0 && delta.scale === 1;\n}\nfunction isDeltaZero(delta) {\n    return isAxisDeltaZero(delta.x) && isAxisDeltaZero(delta.y);\n}\nfunction axisEquals(a, b) {\n    return a.min === b.min && a.max === b.max;\n}\nfunction boxEquals(a, b) {\n    return axisEquals(a.x, b.x) && axisEquals(a.y, b.y);\n}\nfunction axisEqualsRounded(a, b) {\n    return (Math.round(a.min) === Math.round(b.min) &&\n        Math.round(a.max) === Math.round(b.max));\n}\nfunction boxEqualsRounded(a, b) {\n    return axisEqualsRounded(a.x, b.x) && axisEqualsRounded(a.y, b.y);\n}\nfunction aspectRatio(box) {\n    return calcLength(box.x) / calcLength(box.y);\n}\nfunction axisDeltaEquals(a, b) {\n    return (a.translate === b.translate &&\n        a.scale === b.scale &&\n        a.originPoint === b.originPoint);\n}\n\nexport { aspectRatio, axisDeltaEquals, axisEquals, axisEqualsRounded, boxEquals, boxEqualsRounded, isDeltaZero };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA,SAAS,gBAAgB,KAAK;IAC1B,OAAO,MAAM,SAAS,KAAK,KAAK,MAAM,KAAK,KAAK;AACpD;AACA,SAAS,YAAY,KAAK;IACtB,OAAO,gBAAgB,MAAM,CAAC,KAAK,gBAAgB,MAAM,CAAC;AAC9D;AACA,SAAS,WAAW,CAAC,EAAE,CAAC;IACpB,OAAO,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;AAC7C;AACA,SAAS,UAAU,CAAC,EAAE,CAAC;IACnB,OAAO,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC,KAAK,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;AACtD;AACA,SAAS,kBAAkB,CAAC,EAAE,CAAC;IAC3B,OAAQ,KAAK,KAAK,CAAC,EAAE,GAAG,MAAM,KAAK,KAAK,CAAC,EAAE,GAAG,KAC1C,KAAK,KAAK,CAAC,EAAE,GAAG,MAAM,KAAK,KAAK,CAAC,EAAE,GAAG;AAC9C;AACA,SAAS,iBAAiB,CAAC,EAAE,CAAC;IAC1B,OAAO,kBAAkB,EAAE,CAAC,EAAE,EAAE,CAAC,KAAK,kBAAkB,EAAE,CAAC,EAAE,EAAE,CAAC;AACpE;AACA,SAAS,YAAY,GAAG;IACpB,OAAO,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,IAAI,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC;AAC/C;AACA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IACzB,OAAQ,EAAE,SAAS,KAAK,EAAE,SAAS,IAC/B,EAAE,KAAK,KAAK,EAAE,KAAK,IACnB,EAAE,WAAW,KAAK,EAAE,WAAW;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3408, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/shared/stack.mjs"], "sourcesContent": ["import { addUniqueItem, removeItem } from 'motion-utils';\n\nclass NodeStack {\n    constructor() {\n        this.members = [];\n    }\n    add(node) {\n        addUniqueItem(this.members, node);\n        node.scheduleRender();\n    }\n    remove(node) {\n        removeItem(this.members, node);\n        if (node === this.prevLead) {\n            this.prevLead = undefined;\n        }\n        if (node === this.lead) {\n            const prevLead = this.members[this.members.length - 1];\n            if (prevLead) {\n                this.promote(prevLead);\n            }\n        }\n    }\n    relegate(node) {\n        const indexOfNode = this.members.findIndex((member) => node === member);\n        if (indexOfNode === 0)\n            return false;\n        /**\n         * Find the next projection node that is present\n         */\n        let prevLead;\n        for (let i = indexOfNode; i >= 0; i--) {\n            const member = this.members[i];\n            if (member.isPresent !== false) {\n                prevLead = member;\n                break;\n            }\n        }\n        if (prevLead) {\n            this.promote(prevLead);\n            return true;\n        }\n        else {\n            return false;\n        }\n    }\n    promote(node, preserveFollowOpacity) {\n        const prevLead = this.lead;\n        if (node === prevLead)\n            return;\n        this.prevLead = prevLead;\n        this.lead = node;\n        node.show();\n        if (prevLead) {\n            prevLead.instance && prevLead.scheduleRender();\n            node.scheduleRender();\n            node.resumeFrom = prevLead;\n            if (preserveFollowOpacity) {\n                node.resumeFrom.preserveOpacity = true;\n            }\n            if (prevLead.snapshot) {\n                node.snapshot = prevLead.snapshot;\n                node.snapshot.latestValues =\n                    prevLead.animationValues || prevLead.latestValues;\n            }\n            if (node.root && node.root.isUpdating) {\n                node.isLayoutDirty = true;\n            }\n            const { crossfade } = node.options;\n            if (crossfade === false) {\n                prevLead.hide();\n            }\n            /**\n             * TODO:\n             *   - Test border radius when previous node was deleted\n             *   - boxShadow mixing\n             *   - Shared between element A in scrolled container and element B (scroll stays the same or changes)\n             *   - Shared between element A in transformed container and element B (transform stays the same or changes)\n             *   - Shared between element A in scrolled page and element B (scroll stays the same or changes)\n             * ---\n             *   - Crossfade opacity of root nodes\n             *   - layoutId changes after animation\n             *   - layoutId changes mid animation\n             */\n        }\n    }\n    exitAnimationComplete() {\n        this.members.forEach((node) => {\n            const { options, resumingFrom } = node;\n            options.onExitComplete && options.onExitComplete();\n            if (resumingFrom) {\n                resumingFrom.options.onExitComplete &&\n                    resumingFrom.options.onExitComplete();\n            }\n        });\n    }\n    scheduleRender() {\n        this.members.forEach((node) => {\n            node.instance && node.scheduleRender(false);\n        });\n    }\n    /**\n     * Clear any leads that have been removed this render to prevent them from being\n     * used in future animations and to prevent memory leaks\n     */\n    removeLeadSnapshot() {\n        if (this.lead && this.lead.snapshot) {\n            this.lead.snapshot = undefined;\n        }\n    }\n}\n\nexport { NodeStack };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;IACF,aAAc;QACV,IAAI,CAAC,OAAO,GAAG,EAAE;IACrB;IACA,IAAI,IAAI,EAAE;QACN,CAAA,GAAA,uJAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE;QAC5B,KAAK,cAAc;IACvB;IACA,OAAO,IAAI,EAAE;QACT,CAAA,GAAA,uJAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE;QACzB,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE;YACxB,IAAI,CAAC,QAAQ,GAAG;QACpB;QACA,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;YACpB,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE;YACtD,IAAI,UAAU;gBACV,IAAI,CAAC,OAAO,CAAC;YACjB;QACJ;IACJ;IACA,SAAS,IAAI,EAAE;QACX,MAAM,cAAc,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,SAAW,SAAS;QAChE,IAAI,gBAAgB,GAChB,OAAO;QACX;;SAEC,GACD,IAAI;QACJ,IAAK,IAAI,IAAI,aAAa,KAAK,GAAG,IAAK;YACnC,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE;YAC9B,IAAI,OAAO,SAAS,KAAK,OAAO;gBAC5B,WAAW;gBACX;YACJ;QACJ;QACA,IAAI,UAAU;YACV,IAAI,CAAC,OAAO,CAAC;YACb,OAAO;QACX,OACK;YACD,OAAO;QACX;IACJ;IACA,QAAQ,IAAI,EAAE,qBAAqB,EAAE;QACjC,MAAM,WAAW,IAAI,CAAC,IAAI;QAC1B,IAAI,SAAS,UACT;QACJ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,GAAG;QACZ,KAAK,IAAI;QACT,IAAI,UAAU;YACV,SAAS,QAAQ,IAAI,SAAS,cAAc;YAC5C,KAAK,cAAc;YACnB,KAAK,UAAU,GAAG;YAClB,IAAI,uBAAuB;gBACvB,KAAK,UAAU,CAAC,eAAe,GAAG;YACtC;YACA,IAAI,SAAS,QAAQ,EAAE;gBACnB,KAAK,QAAQ,GAAG,SAAS,QAAQ;gBACjC,KAAK,QAAQ,CAAC,YAAY,GACtB,SAAS,eAAe,IAAI,SAAS,YAAY;YACzD;YACA,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,UAAU,EAAE;gBACnC,KAAK,aAAa,GAAG;YACzB;YACA,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,OAAO;YAClC,IAAI,cAAc,OAAO;gBACrB,SAAS,IAAI;YACjB;QACA;;;;;;;;;;;aAWC,GACL;IACJ;IACA,wBAAwB;QACpB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAClB,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG;YAClC,QAAQ,cAAc,IAAI,QAAQ,cAAc;YAChD,IAAI,cAAc;gBACd,aAAa,OAAO,CAAC,cAAc,IAC/B,aAAa,OAAO,CAAC,cAAc;YAC3C;QACJ;IACJ;IACA,iBAAiB;QACb,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAClB,KAAK,QAAQ,IAAI,KAAK,cAAc,CAAC;QACzC;IACJ;IACA;;;KAGC,GACD,qBAAqB;QACjB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACjC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG;QACzB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3520, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/styles/transform.mjs"], "sourcesContent": ["function buildProjectionTransform(delta, treeScale, latestTransform) {\n    let transform = \"\";\n    /**\n     * The translations we use to calculate are always relative to the viewport coordinate space.\n     * But when we apply scales, we also scale the coordinate space of an element and its children.\n     * For instance if we have a treeScale (the culmination of all parent scales) of 0.5 and we need\n     * to move an element 100 pixels, we actually need to move it 200 in within that scaled space.\n     */\n    const xTranslate = delta.x.translate / treeScale.x;\n    const yTranslate = delta.y.translate / treeScale.y;\n    const zTranslate = latestTransform?.z || 0;\n    if (xTranslate || yTranslate || zTranslate) {\n        transform = `translate3d(${xTranslate}px, ${yTranslate}px, ${zTranslate}px) `;\n    }\n    /**\n     * Apply scale correction for the tree transform.\n     * This will apply scale to the screen-orientated axes.\n     */\n    if (treeScale.x !== 1 || treeScale.y !== 1) {\n        transform += `scale(${1 / treeScale.x}, ${1 / treeScale.y}) `;\n    }\n    if (latestTransform) {\n        const { transformPerspective, rotate, rotateX, rotateY, skewX, skewY } = latestTransform;\n        if (transformPerspective)\n            transform = `perspective(${transformPerspective}px) ${transform}`;\n        if (rotate)\n            transform += `rotate(${rotate}deg) `;\n        if (rotateX)\n            transform += `rotateX(${rotateX}deg) `;\n        if (rotateY)\n            transform += `rotateY(${rotateY}deg) `;\n        if (skewX)\n            transform += `skewX(${skewX}deg) `;\n        if (skewY)\n            transform += `skewY(${skewY}deg) `;\n    }\n    /**\n     * Apply scale to match the size of the element to the size we want it.\n     * This will apply scale to the element-orientated axes.\n     */\n    const elementScaleX = delta.x.scale * treeScale.x;\n    const elementScaleY = delta.y.scale * treeScale.y;\n    if (elementScaleX !== 1 || elementScaleY !== 1) {\n        transform += `scale(${elementScaleX}, ${elementScaleY})`;\n    }\n    return transform || \"none\";\n}\n\nexport { buildProjectionTransform };\n"], "names": [], "mappings": ";;;AAAA,SAAS,yBAAyB,KAAK,EAAE,SAAS,EAAE,eAAe;IAC/D,IAAI,YAAY;IAChB;;;;;KAKC,GACD,MAAM,aAAa,MAAM,CAAC,CAAC,SAAS,GAAG,UAAU,CAAC;IAClD,MAAM,aAAa,MAAM,CAAC,CAAC,SAAS,GAAG,UAAU,CAAC;IAClD,MAAM,aAAa,iBAAiB,KAAK;IACzC,IAAI,cAAc,cAAc,YAAY;QACxC,YAAY,CAAC,YAAY,EAAE,WAAW,IAAI,EAAE,WAAW,IAAI,EAAE,WAAW,IAAI,CAAC;IACjF;IACA;;;KAGC,GACD,IAAI,UAAU,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,GAAG;QACxC,aAAa,CAAC,MAAM,EAAE,IAAI,UAAU,CAAC,CAAC,EAAE,EAAE,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC;IACjE;IACA,IAAI,iBAAiB;QACjB,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG;QACzE,IAAI,sBACA,YAAY,CAAC,YAAY,EAAE,qBAAqB,IAAI,EAAE,WAAW;QACrE,IAAI,QACA,aAAa,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;QACxC,IAAI,SACA,aAAa,CAAC,QAAQ,EAAE,QAAQ,KAAK,CAAC;QAC1C,IAAI,SACA,aAAa,CAAC,QAAQ,EAAE,QAAQ,KAAK,CAAC;QAC1C,IAAI,OACA,aAAa,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC;QACtC,IAAI,OACA,aAAa,CAAC,MAAM,EAAE,MAAM,KAAK,CAAC;IAC1C;IACA;;;KAGC,GACD,MAAM,gBAAgB,MAAM,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC;IACjD,MAAM,gBAAgB,MAAM,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC;IACjD,IAAI,kBAAkB,KAAK,kBAAkB,GAAG;QAC5C,aAAa,CAAC,MAAM,EAAE,cAAc,EAAE,EAAE,cAAc,CAAC,CAAC;IAC5D;IACA,OAAO,aAAa;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3568, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs"], "sourcesContent": ["import { statsBuffer, isSVGElement, isSVGSVGElement, frame, getValueTransition, cancelFrame, time, frameData, frameSteps, microtask, activeAnimations, motionValue, mixNumber } from 'motion-dom';\nimport { SubscriptionManager, clamp, noop } from 'motion-utils';\nimport { animateSingleValue } from '../../animation/animate/single-value.mjs';\nimport { getOptimisedAppearId } from '../../animation/optimized-appear/get-appear-id.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto, copyAxisDeltaInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcLength, calcRelativePosition, calcRelativeBox, calcBoxDelta, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { boxEqualsRounded, isDeltaZero, axisDeltaEquals, aspectRatio, boxEquals } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { globalProjectionState } from './state.mjs';\n\nconst metrics = {\n    nodes: 0,\n    calculatedTargetDeltas: 0,\n    calculatedProjections: 0,\n};\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\nfunction resetDistortingTransform(key, visualElement, values, sharedAnimationValues) {\n    const { latestValues } = visualElement;\n    // Record the distorting transform and then temporarily set it to 0\n    if (latestValues[key]) {\n        values[key] = latestValues[key];\n        visualElement.setStaticValue(key, 0);\n        if (sharedAnimationValues) {\n            sharedAnimationValues[key] = 0;\n        }\n    }\n}\nfunction cancelTreeOptimisedTransformAnimations(projectionNode) {\n    projectionNode.hasCheckedOptimisedAppear = true;\n    if (projectionNode.root === projectionNode)\n        return;\n    const { visualElement } = projectionNode.options;\n    if (!visualElement)\n        return;\n    const appearId = getOptimisedAppearId(visualElement);\n    if (window.MotionHasOptimisedAnimation(appearId, \"transform\")) {\n        const { layout, layoutId } = projectionNode.options;\n        window.MotionCancelOptimisedAnimation(appearId, \"transform\", frame, !(layout || layoutId));\n    }\n    const { parent } = projectionNode;\n    if (parent && !parent.hasCheckedOptimisedAppear) {\n        cancelTreeOptimisedTransformAnimations(parent);\n    }\n}\nfunction createProjectionNode({ attachResizeListener, defaultParent, measureScroll, checkIsScrollRoot, resetTransform, }) {\n    return class ProjectionNode {\n        constructor(latestValues = {}, parent = defaultParent?.()) {\n            /**\n             * A unique ID generated for every projection node.\n             */\n            this.id = id++;\n            /**\n             * An id that represents a unique session instigated by startUpdate.\n             */\n            this.animationId = 0;\n            this.animationCommitId = 0;\n            /**\n             * A Set containing all this component's children. This is used to iterate\n             * through the children.\n             *\n             * TODO: This could be faster to iterate as a flat array stored on the root node.\n             */\n            this.children = new Set();\n            /**\n             * Options for the node. We use this to configure what kind of layout animations\n             * we should perform (if any).\n             */\n            this.options = {};\n            /**\n             * We use this to detect when its safe to shut down part of a projection tree.\n             * We have to keep projecting children for scale correction and relative projection\n             * until all their parents stop performing layout animations.\n             */\n            this.isTreeAnimating = false;\n            this.isAnimationBlocked = false;\n            /**\n             * Flag to true if we think this layout has been changed. We can't always know this,\n             * currently we set it to true every time a component renders, or if it has a layoutDependency\n             * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n             * and if one node is dirtied, they all are.\n             */\n            this.isLayoutDirty = false;\n            /**\n             * Flag to true if we think the projection calculations for this node needs\n             * recalculating as a result of an updated transform or layout animation.\n             */\n            this.isProjectionDirty = false;\n            /**\n             * Flag to true if the layout *or* transform has changed. This then gets propagated\n             * throughout the projection tree, forcing any element below to recalculate on the next frame.\n             */\n            this.isSharedProjectionDirty = false;\n            /**\n             * Flag transform dirty. This gets propagated throughout the whole tree but is only\n             * respected by shared nodes.\n             */\n            this.isTransformDirty = false;\n            /**\n             * Block layout updates for instant layout transitions throughout the tree.\n             */\n            this.updateManuallyBlocked = false;\n            this.updateBlockedByResize = false;\n            /**\n             * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n             * call.\n             */\n            this.isUpdating = false;\n            /**\n             * If this is an SVG element we currently disable projection transforms\n             */\n            this.isSVG = false;\n            /**\n             * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n             * its projection styles.\n             */\n            this.needsReset = false;\n            /**\n             * Flags whether this node should have its transform reset prior to measuring.\n             */\n            this.shouldResetTransform = false;\n            /**\n             * Store whether this node has been checked for optimised appear animations. As\n             * effects fire bottom-up, and we want to look up the tree for appear animations,\n             * this makes sure we only check each path once, stopping at nodes that\n             * have already been checked.\n             */\n            this.hasCheckedOptimisedAppear = false;\n            /**\n             * An object representing the calculated contextual/accumulated/tree scale.\n             * This will be used to scale calculcated projection transforms, as these are\n             * calculated in screen-space but need to be scaled for elements to layoutly\n             * make it to their calculated destinations.\n             *\n             * TODO: Lazy-init\n             */\n            this.treeScale = { x: 1, y: 1 };\n            /**\n             *\n             */\n            this.eventHandlers = new Map();\n            this.hasTreeAnimated = false;\n            // Note: Currently only running on root node\n            this.updateScheduled = false;\n            this.scheduleUpdate = () => this.update();\n            this.projectionUpdateScheduled = false;\n            this.checkUpdateFailed = () => {\n                if (this.isUpdating) {\n                    this.isUpdating = false;\n                    this.clearAllSnapshots();\n                }\n            };\n            /**\n             * This is a multi-step process as shared nodes might be of different depths. Nodes\n             * are sorted by depth order, so we need to resolve the entire tree before moving to\n             * the next step.\n             */\n            this.updateProjection = () => {\n                this.projectionUpdateScheduled = false;\n                /**\n                 * Reset debug counts. Manually resetting rather than creating a new\n                 * object each frame.\n                 */\n                if (statsBuffer.value) {\n                    metrics.nodes =\n                        metrics.calculatedTargetDeltas =\n                            metrics.calculatedProjections =\n                                0;\n                }\n                this.nodes.forEach(propagateDirtyNodes);\n                this.nodes.forEach(resolveTargetDelta);\n                this.nodes.forEach(calcProjection);\n                this.nodes.forEach(cleanDirtyNodes);\n                if (statsBuffer.addProjectionMetrics) {\n                    statsBuffer.addProjectionMetrics(metrics);\n                }\n            };\n            /**\n             * Frame calculations\n             */\n            this.resolvedRelativeTargetAt = 0.0;\n            this.hasProjected = false;\n            this.isVisible = true;\n            this.animationProgress = 0;\n            /**\n             * Shared layout\n             */\n            // TODO Only running on root node\n            this.sharedNodes = new Map();\n            this.latestValues = latestValues;\n            this.root = parent ? parent.root || parent : this;\n            this.path = parent ? [...parent.path, parent] : [];\n            this.parent = parent;\n            this.depth = parent ? parent.depth + 1 : 0;\n            for (let i = 0; i < this.path.length; i++) {\n                this.path[i].shouldResetTransform = true;\n            }\n            if (this.root === this)\n                this.nodes = new FlatTree();\n        }\n        addEventListener(name, handler) {\n            if (!this.eventHandlers.has(name)) {\n                this.eventHandlers.set(name, new SubscriptionManager());\n            }\n            return this.eventHandlers.get(name).add(handler);\n        }\n        notifyListeners(name, ...args) {\n            const subscriptionManager = this.eventHandlers.get(name);\n            subscriptionManager && subscriptionManager.notify(...args);\n        }\n        hasListeners(name) {\n            return this.eventHandlers.has(name);\n        }\n        /**\n         * Lifecycles\n         */\n        mount(instance) {\n            if (this.instance)\n                return;\n            this.isSVG = isSVGElement(instance) && !isSVGSVGElement(instance);\n            this.instance = instance;\n            const { layoutId, layout, visualElement } = this.options;\n            if (visualElement && !visualElement.current) {\n                visualElement.mount(instance);\n            }\n            this.root.nodes.add(this);\n            this.parent && this.parent.children.add(this);\n            if (this.root.hasTreeAnimated && (layout || layoutId)) {\n                this.isLayoutDirty = true;\n            }\n            if (attachResizeListener) {\n                let cancelDelay;\n                let innerWidth = 0;\n                const resizeUnblockUpdate = () => (this.root.updateBlockedByResize = false);\n                // Set initial innerWidth in a frame.read callback to batch the read\n                frame.read(() => {\n                    innerWidth = window.innerWidth;\n                });\n                attachResizeListener(instance, () => {\n                    const newInnerWidth = window.innerWidth;\n                    if (newInnerWidth === innerWidth)\n                        return;\n                    innerWidth = newInnerWidth;\n                    this.root.updateBlockedByResize = true;\n                    cancelDelay && cancelDelay();\n                    cancelDelay = delay(resizeUnblockUpdate, 250);\n                    if (globalProjectionState.hasAnimatedSinceResize) {\n                        globalProjectionState.hasAnimatedSinceResize = false;\n                        this.nodes.forEach(finishAnimation);\n                    }\n                });\n            }\n            if (layoutId) {\n                this.root.registerSharedNode(layoutId, this);\n            }\n            // Only register the handler if it requires layout animation\n            if (this.options.animate !== false &&\n                visualElement &&\n                (layoutId || layout)) {\n                this.addEventListener(\"didUpdate\", ({ delta, hasLayoutChanged, hasRelativeLayoutChanged, layout: newLayout, }) => {\n                    if (this.isTreeAnimationBlocked()) {\n                        this.target = undefined;\n                        this.relativeTarget = undefined;\n                        return;\n                    }\n                    // TODO: Check here if an animation exists\n                    const layoutTransition = this.options.transition ||\n                        visualElement.getDefaultTransition() ||\n                        defaultLayoutTransition;\n                    const { onLayoutAnimationStart, onLayoutAnimationComplete, } = visualElement.getProps();\n                    /**\n                     * The target layout of the element might stay the same,\n                     * but its position relative to its parent has changed.\n                     */\n                    const hasTargetChanged = !this.targetLayout ||\n                        !boxEqualsRounded(this.targetLayout, newLayout);\n                    /*\n                     * Note: Disabled to fix relative animations always triggering new\n                     * layout animations. If this causes further issues, we can try\n                     * a different approach to detecting relative target changes.\n                     */\n                    // || hasRelativeLayoutChanged\n                    /**\n                     * If the layout hasn't seemed to have changed, it might be that the\n                     * element is visually in the same place in the document but its position\n                     * relative to its parent has indeed changed. So here we check for that.\n                     */\n                    const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeLayoutChanged;\n                    if (this.options.layoutRoot ||\n                        this.resumeFrom ||\n                        hasOnlyRelativeTargetChanged ||\n                        (hasLayoutChanged &&\n                            (hasTargetChanged || !this.currentAnimation))) {\n                        if (this.resumeFrom) {\n                            this.resumingFrom = this.resumeFrom;\n                            this.resumingFrom.resumingFrom = undefined;\n                        }\n                        const animationOptions = {\n                            ...getValueTransition(layoutTransition, \"layout\"),\n                            onPlay: onLayoutAnimationStart,\n                            onComplete: onLayoutAnimationComplete,\n                        };\n                        if (visualElement.shouldReduceMotion ||\n                            this.options.layoutRoot) {\n                            animationOptions.delay = 0;\n                            animationOptions.type = false;\n                        }\n                        this.startAnimation(animationOptions);\n                        /**\n                         * Set animation origin after starting animation to avoid layout jump\n                         * caused by stopping previous layout animation\n                         */\n                        this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n                    }\n                    else {\n                        /**\n                         * If the layout hasn't changed and we have an animation that hasn't started yet,\n                         * finish it immediately. Otherwise it will be animating from a location\n                         * that was probably never commited to screen and look like a jumpy box.\n                         */\n                        if (!hasLayoutChanged) {\n                            finishAnimation(this);\n                        }\n                        if (this.isLead() && this.options.onExitComplete) {\n                            this.options.onExitComplete();\n                        }\n                    }\n                    this.targetLayout = newLayout;\n                });\n            }\n        }\n        unmount() {\n            this.options.layoutId && this.willUpdate();\n            this.root.nodes.remove(this);\n            const stack = this.getStack();\n            stack && stack.remove(this);\n            this.parent && this.parent.children.delete(this);\n            this.instance = undefined;\n            this.eventHandlers.clear();\n            cancelFrame(this.updateProjection);\n        }\n        // only on the root\n        blockUpdate() {\n            this.updateManuallyBlocked = true;\n        }\n        unblockUpdate() {\n            this.updateManuallyBlocked = false;\n        }\n        isUpdateBlocked() {\n            return this.updateManuallyBlocked || this.updateBlockedByResize;\n        }\n        isTreeAnimationBlocked() {\n            return (this.isAnimationBlocked ||\n                (this.parent && this.parent.isTreeAnimationBlocked()) ||\n                false);\n        }\n        // Note: currently only running on root node\n        startUpdate() {\n            if (this.isUpdateBlocked())\n                return;\n            this.isUpdating = true;\n            this.nodes && this.nodes.forEach(resetSkewAndRotation);\n            this.animationId++;\n        }\n        getTransformTemplate() {\n            const { visualElement } = this.options;\n            return visualElement && visualElement.getProps().transformTemplate;\n        }\n        willUpdate(shouldNotifyListeners = true) {\n            this.root.hasTreeAnimated = true;\n            if (this.root.isUpdateBlocked()) {\n                this.options.onExitComplete && this.options.onExitComplete();\n                return;\n            }\n            /**\n             * If we're running optimised appear animations then these must be\n             * cancelled before measuring the DOM. This is so we can measure\n             * the true layout of the element rather than the WAAPI animation\n             * which will be unaffected by the resetSkewAndRotate step.\n             *\n             * Note: This is a DOM write. Worst case scenario is this is sandwiched\n             * between other snapshot reads which will cause unnecessary style recalculations.\n             * This has to happen here though, as we don't yet know which nodes will need\n             * snapshots in startUpdate(), but we only want to cancel optimised animations\n             * if a layout animation measurement is actually going to be affected by them.\n             */\n            if (window.MotionCancelOptimisedAnimation &&\n                !this.hasCheckedOptimisedAppear) {\n                cancelTreeOptimisedTransformAnimations(this);\n            }\n            !this.root.isUpdating && this.root.startUpdate();\n            if (this.isLayoutDirty)\n                return;\n            this.isLayoutDirty = true;\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                node.shouldResetTransform = true;\n                node.updateScroll(\"snapshot\");\n                if (node.options.layoutRoot) {\n                    node.willUpdate(false);\n                }\n            }\n            const { layoutId, layout } = this.options;\n            if (layoutId === undefined && !layout)\n                return;\n            const transformTemplate = this.getTransformTemplate();\n            this.prevTransformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            this.updateSnapshot();\n            shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n        }\n        update() {\n            this.updateScheduled = false;\n            const updateWasBlocked = this.isUpdateBlocked();\n            // When doing an instant transition, we skip the layout update,\n            // but should still clean up the measurements so that the next\n            // snapshot could be taken correctly.\n            if (updateWasBlocked) {\n                this.unblockUpdate();\n                this.clearAllSnapshots();\n                this.nodes.forEach(clearMeasurements);\n                return;\n            }\n            /**\n             * If this is a repeat of didUpdate then ignore the animation.\n             */\n            if (this.animationId <= this.animationCommitId) {\n                this.nodes.forEach(clearIsLayoutDirty);\n                return;\n            }\n            this.animationCommitId = this.animationId;\n            if (!this.isUpdating) {\n                this.nodes.forEach(clearIsLayoutDirty);\n            }\n            else {\n                this.isUpdating = false;\n                /**\n                 * Write\n                 */\n                this.nodes.forEach(resetTransformStyle);\n                /**\n                 * Read ==================\n                 */\n                // Update layout measurements of updated children\n                this.nodes.forEach(updateLayout);\n                /**\n                 * Write\n                 */\n                // Notify listeners that the layout is updated\n                this.nodes.forEach(notifyLayoutUpdate);\n            }\n            this.clearAllSnapshots();\n            /**\n             * Manually flush any pending updates. Ideally\n             * we could leave this to the following requestAnimationFrame but this seems\n             * to leave a flash of incorrectly styled content.\n             */\n            const now = time.now();\n            frameData.delta = clamp(0, 1000 / 60, now - frameData.timestamp);\n            frameData.timestamp = now;\n            frameData.isProcessing = true;\n            frameSteps.update.process(frameData);\n            frameSteps.preRender.process(frameData);\n            frameSteps.render.process(frameData);\n            frameData.isProcessing = false;\n        }\n        didUpdate() {\n            if (!this.updateScheduled) {\n                this.updateScheduled = true;\n                microtask.read(this.scheduleUpdate);\n            }\n        }\n        clearAllSnapshots() {\n            this.nodes.forEach(clearSnapshot);\n            this.sharedNodes.forEach(removeLeadSnapshots);\n        }\n        scheduleUpdateProjection() {\n            if (!this.projectionUpdateScheduled) {\n                this.projectionUpdateScheduled = true;\n                frame.preRender(this.updateProjection, false, true);\n            }\n        }\n        scheduleCheckAfterUnmount() {\n            /**\n             * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n             * we manually call didUpdate to give a chance to the siblings to animate.\n             * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n             */\n            frame.postRender(() => {\n                if (this.isLayoutDirty) {\n                    this.root.didUpdate();\n                }\n                else {\n                    this.root.checkUpdateFailed();\n                }\n            });\n        }\n        /**\n         * Update measurements\n         */\n        updateSnapshot() {\n            if (this.snapshot || !this.instance)\n                return;\n            this.snapshot = this.measure();\n            if (this.snapshot &&\n                !calcLength(this.snapshot.measuredBox.x) &&\n                !calcLength(this.snapshot.measuredBox.y)) {\n                this.snapshot = undefined;\n            }\n        }\n        updateLayout() {\n            if (!this.instance)\n                return;\n            this.updateScroll();\n            if (!(this.options.alwaysMeasureLayout && this.isLead()) &&\n                !this.isLayoutDirty) {\n                return;\n            }\n            /**\n             * When a node is mounted, it simply resumes from the prevLead's\n             * snapshot instead of taking a new one, but the ancestors scroll\n             * might have updated while the prevLead is unmounted. We need to\n             * update the scroll again to make sure the layout we measure is\n             * up to date.\n             */\n            if (this.resumeFrom && !this.resumeFrom.instance) {\n                for (let i = 0; i < this.path.length; i++) {\n                    const node = this.path[i];\n                    node.updateScroll();\n                }\n            }\n            const prevLayout = this.layout;\n            this.layout = this.measure(false);\n            this.layoutCorrected = createBox();\n            this.isLayoutDirty = false;\n            this.projectionDelta = undefined;\n            this.notifyListeners(\"measure\", this.layout.layoutBox);\n            const { visualElement } = this.options;\n            visualElement &&\n                visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n        }\n        updateScroll(phase = \"measure\") {\n            let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n            if (this.scroll &&\n                this.scroll.animationId === this.root.animationId &&\n                this.scroll.phase === phase) {\n                needsMeasurement = false;\n            }\n            if (needsMeasurement && this.instance) {\n                const isRoot = checkIsScrollRoot(this.instance);\n                this.scroll = {\n                    animationId: this.root.animationId,\n                    phase,\n                    isRoot,\n                    offset: measureScroll(this.instance),\n                    wasRoot: this.scroll ? this.scroll.isRoot : isRoot,\n                };\n            }\n        }\n        resetTransform() {\n            if (!resetTransform)\n                return;\n            const isResetRequested = this.isLayoutDirty ||\n                this.shouldResetTransform ||\n                this.options.alwaysMeasureLayout;\n            const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n            const transformTemplate = this.getTransformTemplate();\n            const transformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n            if (isResetRequested &&\n                this.instance &&\n                (hasProjection ||\n                    hasTransform(this.latestValues) ||\n                    transformTemplateHasChanged)) {\n                resetTransform(this.instance, transformTemplateValue);\n                this.shouldResetTransform = false;\n                this.scheduleRender();\n            }\n        }\n        measure(removeTransform = true) {\n            const pageBox = this.measurePageBox();\n            let layoutBox = this.removeElementScroll(pageBox);\n            /**\n             * Measurements taken during the pre-render stage\n             * still have transforms applied so we remove them\n             * via calculation.\n             */\n            if (removeTransform) {\n                layoutBox = this.removeTransform(layoutBox);\n            }\n            roundBox(layoutBox);\n            return {\n                animationId: this.root.animationId,\n                measuredBox: pageBox,\n                layoutBox,\n                latestValues: {},\n                source: this.id,\n            };\n        }\n        measurePageBox() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return createBox();\n            const box = visualElement.measureViewportBox();\n            const wasInScrollRoot = this.scroll?.wasRoot || this.path.some(checkNodeWasScrollRoot);\n            if (!wasInScrollRoot) {\n                // Remove viewport scroll to give page-relative coordinates\n                const { scroll } = this.root;\n                if (scroll) {\n                    translateAxis(box.x, scroll.offset.x);\n                    translateAxis(box.y, scroll.offset.y);\n                }\n            }\n            return box;\n        }\n        removeElementScroll(box) {\n            const boxWithoutScroll = createBox();\n            copyBoxInto(boxWithoutScroll, box);\n            if (this.scroll?.wasRoot) {\n                return boxWithoutScroll;\n            }\n            /**\n             * Performance TODO: Keep a cumulative scroll offset down the tree\n             * rather than loop back up the path.\n             */\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                const { scroll, options } = node;\n                if (node !== this.root && scroll && options.layoutScroll) {\n                    /**\n                     * If this is a new scroll root, we want to remove all previous scrolls\n                     * from the viewport box.\n                     */\n                    if (scroll.wasRoot) {\n                        copyBoxInto(boxWithoutScroll, box);\n                    }\n                    translateAxis(boxWithoutScroll.x, scroll.offset.x);\n                    translateAxis(boxWithoutScroll.y, scroll.offset.y);\n                }\n            }\n            return boxWithoutScroll;\n        }\n        applyTransform(box, transformOnly = false) {\n            const withTransforms = createBox();\n            copyBoxInto(withTransforms, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!transformOnly &&\n                    node.options.layoutScroll &&\n                    node.scroll &&\n                    node !== node.root) {\n                    transformBox(withTransforms, {\n                        x: -node.scroll.offset.x,\n                        y: -node.scroll.offset.y,\n                    });\n                }\n                if (!hasTransform(node.latestValues))\n                    continue;\n                transformBox(withTransforms, node.latestValues);\n            }\n            if (hasTransform(this.latestValues)) {\n                transformBox(withTransforms, this.latestValues);\n            }\n            return withTransforms;\n        }\n        removeTransform(box) {\n            const boxWithoutTransform = createBox();\n            copyBoxInto(boxWithoutTransform, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!node.instance)\n                    continue;\n                if (!hasTransform(node.latestValues))\n                    continue;\n                hasScale(node.latestValues) && node.updateSnapshot();\n                const sourceBox = createBox();\n                const nodeBox = node.measurePageBox();\n                copyBoxInto(sourceBox, nodeBox);\n                removeBoxTransforms(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n            }\n            if (hasTransform(this.latestValues)) {\n                removeBoxTransforms(boxWithoutTransform, this.latestValues);\n            }\n            return boxWithoutTransform;\n        }\n        setTargetDelta(delta) {\n            this.targetDelta = delta;\n            this.root.scheduleUpdateProjection();\n            this.isProjectionDirty = true;\n        }\n        setOptions(options) {\n            this.options = {\n                ...this.options,\n                ...options,\n                crossfade: options.crossfade !== undefined ? options.crossfade : true,\n            };\n        }\n        clearMeasurements() {\n            this.scroll = undefined;\n            this.layout = undefined;\n            this.snapshot = undefined;\n            this.prevTransformTemplateValue = undefined;\n            this.targetDelta = undefined;\n            this.target = undefined;\n            this.isLayoutDirty = false;\n        }\n        forceRelativeParentToResolveTarget() {\n            if (!this.relativeParent)\n                return;\n            /**\n             * If the parent target isn't up-to-date, force it to update.\n             * This is an unfortunate de-optimisation as it means any updating relative\n             * projection will cause all the relative parents to recalculate back\n             * up the tree.\n             */\n            if (this.relativeParent.resolvedRelativeTargetAt !==\n                frameData.timestamp) {\n                this.relativeParent.resolveTargetDelta(true);\n            }\n        }\n        resolveTargetDelta(forceRecalculation = false) {\n            /**\n             * Once the dirty status of nodes has been spread through the tree, we also\n             * need to check if we have a shared node of a different depth that has itself\n             * been dirtied.\n             */\n            const lead = this.getLead();\n            this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n            this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n            this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            /**\n             * We don't use transform for this step of processing so we don't\n             * need to check whether any nodes have changed transform.\n             */\n            const canSkip = !(forceRecalculation ||\n                (isShared && this.isSharedProjectionDirty) ||\n                this.isProjectionDirty ||\n                this.parent?.isProjectionDirty ||\n                this.attemptToResolveRelativeTarget ||\n                this.root.updateBlockedByResize);\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If we have no layout, we can't perform projection, so early return\n             */\n            if (!this.layout || !(layout || layoutId))\n                return;\n            this.resolvedRelativeTargetAt = frameData.timestamp;\n            /**\n             * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n             * a relativeParent. This will allow a component to perform scale correction\n             * even if no animation has started.\n             */\n            if (!this.targetDelta && !this.relativeTarget) {\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    relativeParent.layout &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * If we have no relative target or no target delta our target isn't valid\n             * for this frame.\n             */\n            if (!this.relativeTarget && !this.targetDelta)\n                return;\n            /**\n             * Lazy-init target data structure\n             */\n            if (!this.target) {\n                this.target = createBox();\n                this.targetWithTransforms = createBox();\n            }\n            /**\n             * If we've got a relative box for this component, resolve it into a target relative to the parent.\n             */\n            if (this.relativeTarget &&\n                this.relativeTargetOrigin &&\n                this.relativeParent &&\n                this.relativeParent.target) {\n                this.forceRelativeParentToResolveTarget();\n                calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n                /**\n                 * If we've only got a targetDelta, resolve it into a target\n                 */\n            }\n            else if (this.targetDelta) {\n                if (Boolean(this.resumingFrom)) {\n                    // TODO: This is creating a new object every frame\n                    this.target = this.applyTransform(this.layout.layoutBox);\n                }\n                else {\n                    copyBoxInto(this.target, this.layout.layoutBox);\n                }\n                applyBoxDelta(this.target, this.targetDelta);\n            }\n            else {\n                /**\n                 * If no target, use own layout as target\n                 */\n                copyBoxInto(this.target, this.layout.layoutBox);\n            }\n            /**\n             * If we've been told to attempt to resolve a relative target, do so.\n             */\n            if (this.attemptToResolveRelativeTarget) {\n                this.attemptToResolveRelativeTarget = false;\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    Boolean(relativeParent.resumingFrom) ===\n                        Boolean(this.resumingFrom) &&\n                    !relativeParent.options.layoutScroll &&\n                    relativeParent.target &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * Increase debug counter for resolved target deltas\n             */\n            if (statsBuffer.value) {\n                metrics.calculatedTargetDeltas++;\n            }\n        }\n        getClosestProjectingParent() {\n            if (!this.parent ||\n                hasScale(this.parent.latestValues) ||\n                has2DTranslate(this.parent.latestValues)) {\n                return undefined;\n            }\n            if (this.parent.isProjecting()) {\n                return this.parent;\n            }\n            else {\n                return this.parent.getClosestProjectingParent();\n            }\n        }\n        isProjecting() {\n            return Boolean((this.relativeTarget ||\n                this.targetDelta ||\n                this.options.layoutRoot) &&\n                this.layout);\n        }\n        calcProjection() {\n            const lead = this.getLead();\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            let canSkip = true;\n            /**\n             * If this is a normal layout animation and neither this node nor its nearest projecting\n             * is dirty then we can't skip.\n             */\n            if (this.isProjectionDirty || this.parent?.isProjectionDirty) {\n                canSkip = false;\n            }\n            /**\n             * If this is a shared layout animation and this node's shared projection is dirty then\n             * we can't skip.\n             */\n            if (isShared &&\n                (this.isSharedProjectionDirty || this.isTransformDirty)) {\n                canSkip = false;\n            }\n            /**\n             * If we have resolved the target this frame we must recalculate the\n             * projection to ensure it visually represents the internal calculations.\n             */\n            if (this.resolvedRelativeTargetAt === frameData.timestamp) {\n                canSkip = false;\n            }\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If this section of the tree isn't animating we can\n             * delete our target sources for the following frame.\n             */\n            this.isTreeAnimating = Boolean((this.parent && this.parent.isTreeAnimating) ||\n                this.currentAnimation ||\n                this.pendingAnimation);\n            if (!this.isTreeAnimating) {\n                this.targetDelta = this.relativeTarget = undefined;\n            }\n            if (!this.layout || !(layout || layoutId))\n                return;\n            /**\n             * Reset the corrected box with the latest values from box, as we're then going\n             * to perform mutative operations on it.\n             */\n            copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n            /**\n             * Record previous tree scales before updating.\n             */\n            const prevTreeScaleX = this.treeScale.x;\n            const prevTreeScaleY = this.treeScale.y;\n            /**\n             * Apply all the parent deltas to this box to produce the corrected box. This\n             * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n             */\n            applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n            /**\n             * If this layer needs to perform scale correction but doesn't have a target,\n             * use the layout as the target.\n             */\n            if (lead.layout &&\n                !lead.target &&\n                (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n                lead.target = lead.layout.layoutBox;\n                lead.targetWithTransforms = createBox();\n            }\n            const { target } = lead;\n            if (!target) {\n                /**\n                 * If we don't have a target to project into, but we were previously\n                 * projecting, we want to remove the stored transform and schedule\n                 * a render to ensure the elements reflect the removed transform.\n                 */\n                if (this.prevProjectionDelta) {\n                    this.createProjectionDeltas();\n                    this.scheduleRender();\n                }\n                return;\n            }\n            if (!this.projectionDelta || !this.prevProjectionDelta) {\n                this.createProjectionDeltas();\n            }\n            else {\n                copyAxisDeltaInto(this.prevProjectionDelta.x, this.projectionDelta.x);\n                copyAxisDeltaInto(this.prevProjectionDelta.y, this.projectionDelta.y);\n            }\n            /**\n             * Update the delta between the corrected box and the target box before user-set transforms were applied.\n             * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n             * for our layout reprojection, but still allow them to be scaled correctly by the user.\n             * It might be that to simplify this we may want to accept that user-set scale is also corrected\n             * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n             * to allow people to choose whether these styles are corrected based on just the\n             * layout reprojection or the final bounding box.\n             */\n            calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n            if (this.treeScale.x !== prevTreeScaleX ||\n                this.treeScale.y !== prevTreeScaleY ||\n                !axisDeltaEquals(this.projectionDelta.x, this.prevProjectionDelta.x) ||\n                !axisDeltaEquals(this.projectionDelta.y, this.prevProjectionDelta.y)) {\n                this.hasProjected = true;\n                this.scheduleRender();\n                this.notifyListeners(\"projectionUpdate\", target);\n            }\n            /**\n             * Increase debug counter for recalculated projections\n             */\n            if (statsBuffer.value) {\n                metrics.calculatedProjections++;\n            }\n        }\n        hide() {\n            this.isVisible = false;\n            // TODO: Schedule render\n        }\n        show() {\n            this.isVisible = true;\n            // TODO: Schedule render\n        }\n        scheduleRender(notifyAll = true) {\n            this.options.visualElement?.scheduleRender();\n            if (notifyAll) {\n                const stack = this.getStack();\n                stack && stack.scheduleRender();\n            }\n            if (this.resumingFrom && !this.resumingFrom.instance) {\n                this.resumingFrom = undefined;\n            }\n        }\n        createProjectionDeltas() {\n            this.prevProjectionDelta = createDelta();\n            this.projectionDelta = createDelta();\n            this.projectionDeltaWithTransform = createDelta();\n        }\n        setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n            const snapshot = this.snapshot;\n            const snapshotLatestValues = snapshot ? snapshot.latestValues : {};\n            const mixedValues = { ...this.latestValues };\n            const targetDelta = createDelta();\n            if (!this.relativeParent ||\n                !this.relativeParent.options.layoutRoot) {\n                this.relativeTarget = this.relativeTargetOrigin = undefined;\n            }\n            this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n            const relativeLayout = createBox();\n            const snapshotSource = snapshot ? snapshot.source : undefined;\n            const layoutSource = this.layout ? this.layout.source : undefined;\n            const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n            const stack = this.getStack();\n            const isOnlyMember = !stack || stack.members.length <= 1;\n            const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation &&\n                !isOnlyMember &&\n                this.options.crossfade === true &&\n                !this.path.some(hasOpacityCrossfade));\n            this.animationProgress = 0;\n            let prevRelativeTarget;\n            this.mixTargetDelta = (latest) => {\n                const progress = latest / 1000;\n                mixAxisDelta(targetDelta.x, delta.x, progress);\n                mixAxisDelta(targetDelta.y, delta.y, progress);\n                this.setTargetDelta(targetDelta);\n                if (this.relativeTarget &&\n                    this.relativeTargetOrigin &&\n                    this.layout &&\n                    this.relativeParent &&\n                    this.relativeParent.layout) {\n                    calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n                    mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n                    /**\n                     * If this is an unchanged relative target we can consider the\n                     * projection not dirty.\n                     */\n                    if (prevRelativeTarget &&\n                        boxEquals(this.relativeTarget, prevRelativeTarget)) {\n                        this.isProjectionDirty = false;\n                    }\n                    if (!prevRelativeTarget)\n                        prevRelativeTarget = createBox();\n                    copyBoxInto(prevRelativeTarget, this.relativeTarget);\n                }\n                if (isSharedLayoutAnimation) {\n                    this.animationValues = mixedValues;\n                    mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n                }\n                this.root.scheduleUpdateProjection();\n                this.scheduleRender();\n                this.animationProgress = progress;\n            };\n            this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n        }\n        startAnimation(options) {\n            this.notifyListeners(\"animationStart\");\n            this.currentAnimation?.stop();\n            this.resumingFrom?.currentAnimation?.stop();\n            if (this.pendingAnimation) {\n                cancelFrame(this.pendingAnimation);\n                this.pendingAnimation = undefined;\n            }\n            /**\n             * Start the animation in the next frame to have a frame with progress 0,\n             * where the target is the same as when the animation started, so we can\n             * calculate the relative positions correctly for instant transitions.\n             */\n            this.pendingAnimation = frame.update(() => {\n                globalProjectionState.hasAnimatedSinceResize = true;\n                activeAnimations.layout++;\n                this.motionValue || (this.motionValue = motionValue(0));\n                this.currentAnimation = animateSingleValue(this.motionValue, [0, 1000], {\n                    ...options,\n                    velocity: 0,\n                    isSync: true,\n                    onUpdate: (latest) => {\n                        this.mixTargetDelta(latest);\n                        options.onUpdate && options.onUpdate(latest);\n                    },\n                    onStop: () => {\n                        activeAnimations.layout--;\n                    },\n                    onComplete: () => {\n                        activeAnimations.layout--;\n                        options.onComplete && options.onComplete();\n                        this.completeAnimation();\n                    },\n                });\n                if (this.resumingFrom) {\n                    this.resumingFrom.currentAnimation = this.currentAnimation;\n                }\n                this.pendingAnimation = undefined;\n            });\n        }\n        completeAnimation() {\n            if (this.resumingFrom) {\n                this.resumingFrom.currentAnimation = undefined;\n                this.resumingFrom.preserveOpacity = undefined;\n            }\n            const stack = this.getStack();\n            stack && stack.exitAnimationComplete();\n            this.resumingFrom =\n                this.currentAnimation =\n                    this.animationValues =\n                        undefined;\n            this.notifyListeners(\"animationComplete\");\n        }\n        finishAnimation() {\n            if (this.currentAnimation) {\n                this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n                this.currentAnimation.stop();\n            }\n            this.completeAnimation();\n        }\n        applyTransformsToTarget() {\n            const lead = this.getLead();\n            let { targetWithTransforms, target, layout, latestValues } = lead;\n            if (!targetWithTransforms || !target || !layout)\n                return;\n            /**\n             * If we're only animating position, and this element isn't the lead element,\n             * then instead of projecting into the lead box we instead want to calculate\n             * a new target that aligns the two boxes but maintains the layout shape.\n             */\n            if (this !== lead &&\n                this.layout &&\n                layout &&\n                shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n                target = this.target || createBox();\n                const xLength = calcLength(this.layout.layoutBox.x);\n                target.x.min = lead.target.x.min;\n                target.x.max = target.x.min + xLength;\n                const yLength = calcLength(this.layout.layoutBox.y);\n                target.y.min = lead.target.y.min;\n                target.y.max = target.y.min + yLength;\n            }\n            copyBoxInto(targetWithTransforms, target);\n            /**\n             * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n             * This is the final box that we will then project into by calculating a transform delta and\n             * applying it to the corrected box.\n             */\n            transformBox(targetWithTransforms, latestValues);\n            /**\n             * Update the delta between the corrected box and the final target box, after\n             * user-set transforms are applied to it. This will be used by the renderer to\n             * create a transform style that will reproject the element from its layout layout\n             * into the desired bounding box.\n             */\n            calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n        }\n        registerSharedNode(layoutId, node) {\n            if (!this.sharedNodes.has(layoutId)) {\n                this.sharedNodes.set(layoutId, new NodeStack());\n            }\n            const stack = this.sharedNodes.get(layoutId);\n            stack.add(node);\n            const config = node.options.initialPromotionConfig;\n            node.promote({\n                transition: config ? config.transition : undefined,\n                preserveFollowOpacity: config && config.shouldPreserveFollowOpacity\n                    ? config.shouldPreserveFollowOpacity(node)\n                    : undefined,\n            });\n        }\n        isLead() {\n            const stack = this.getStack();\n            return stack ? stack.lead === this : true;\n        }\n        getLead() {\n            const { layoutId } = this.options;\n            return layoutId ? this.getStack()?.lead || this : this;\n        }\n        getPrevLead() {\n            const { layoutId } = this.options;\n            return layoutId ? this.getStack()?.prevLead : undefined;\n        }\n        getStack() {\n            const { layoutId } = this.options;\n            if (layoutId)\n                return this.root.sharedNodes.get(layoutId);\n        }\n        promote({ needsReset, transition, preserveFollowOpacity, } = {}) {\n            const stack = this.getStack();\n            if (stack)\n                stack.promote(this, preserveFollowOpacity);\n            if (needsReset) {\n                this.projectionDelta = undefined;\n                this.needsReset = true;\n            }\n            if (transition)\n                this.setOptions({ transition });\n        }\n        relegate() {\n            const stack = this.getStack();\n            if (stack) {\n                return stack.relegate(this);\n            }\n            else {\n                return false;\n            }\n        }\n        resetSkewAndRotation() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return;\n            // If there's no detected skew or rotation values, we can early return without a forced render.\n            let hasDistortingTransform = false;\n            /**\n             * An unrolled check for rotation values. Most elements don't have any rotation and\n             * skipping the nested loop and new object creation is 50% faster.\n             */\n            const { latestValues } = visualElement;\n            if (latestValues.z ||\n                latestValues.rotate ||\n                latestValues.rotateX ||\n                latestValues.rotateY ||\n                latestValues.rotateZ ||\n                latestValues.skewX ||\n                latestValues.skewY) {\n                hasDistortingTransform = true;\n            }\n            // If there's no distorting values, we don't need to do any more.\n            if (!hasDistortingTransform)\n                return;\n            const resetValues = {};\n            if (latestValues.z) {\n                resetDistortingTransform(\"z\", visualElement, resetValues, this.animationValues);\n            }\n            // Check the skew and rotate value of all axes and reset to 0\n            for (let i = 0; i < transformAxes.length; i++) {\n                resetDistortingTransform(`rotate${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n                resetDistortingTransform(`skew${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n            }\n            // Force a render of this element to apply the transform with all skews and rotations\n            // set to 0.\n            visualElement.render();\n            // Put back all the values we reset\n            for (const key in resetValues) {\n                visualElement.setStaticValue(key, resetValues[key]);\n                if (this.animationValues) {\n                    this.animationValues[key] = resetValues[key];\n                }\n            }\n            // Schedule a render for the next frame. This ensures we won't visually\n            // see the element with the reset rotate value applied.\n            visualElement.scheduleRender();\n        }\n        applyProjectionStyles(targetStyle, // CSSStyleDeclaration - doesn't allow numbers to be assigned to properties\n        styleProp) {\n            if (!this.instance || this.isSVG)\n                return;\n            if (!this.isVisible) {\n                targetStyle.visibility = \"hidden\";\n                return;\n            }\n            const transformTemplate = this.getTransformTemplate();\n            if (this.needsReset) {\n                this.needsReset = false;\n                targetStyle.visibility = \"\";\n                targetStyle.opacity = \"\";\n                targetStyle.pointerEvents =\n                    resolveMotionValue(styleProp?.pointerEvents) || \"\";\n                targetStyle.transform = transformTemplate\n                    ? transformTemplate(this.latestValues, \"\")\n                    : \"none\";\n                return;\n            }\n            const lead = this.getLead();\n            if (!this.projectionDelta || !this.layout || !lead.target) {\n                if (this.options.layoutId) {\n                    targetStyle.opacity =\n                        this.latestValues.opacity !== undefined\n                            ? this.latestValues.opacity\n                            : 1;\n                    targetStyle.pointerEvents =\n                        resolveMotionValue(styleProp?.pointerEvents) || \"\";\n                }\n                if (this.hasProjected && !hasTransform(this.latestValues)) {\n                    targetStyle.transform = transformTemplate\n                        ? transformTemplate({}, \"\")\n                        : \"none\";\n                    this.hasProjected = false;\n                }\n                return;\n            }\n            targetStyle.visibility = \"\";\n            const valuesToRender = lead.animationValues || lead.latestValues;\n            this.applyTransformsToTarget();\n            let transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n            if (transformTemplate) {\n                transform = transformTemplate(valuesToRender, transform);\n            }\n            targetStyle.transform = transform;\n            const { x, y } = this.projectionDelta;\n            targetStyle.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n            if (lead.animationValues) {\n                /**\n                 * If the lead component is animating, assign this either the entering/leaving\n                 * opacity\n                 */\n                targetStyle.opacity =\n                    lead === this\n                        ? valuesToRender.opacity ??\n                            this.latestValues.opacity ??\n                            1\n                        : this.preserveOpacity\n                            ? this.latestValues.opacity\n                            : valuesToRender.opacityExit;\n            }\n            else {\n                /**\n                 * Or we're not animating at all, set the lead component to its layout\n                 * opacity and other components to hidden.\n                 */\n                targetStyle.opacity =\n                    lead === this\n                        ? valuesToRender.opacity !== undefined\n                            ? valuesToRender.opacity\n                            : \"\"\n                        : valuesToRender.opacityExit !== undefined\n                            ? valuesToRender.opacityExit\n                            : 0;\n            }\n            /**\n             * Apply scale correction\n             */\n            for (const key in scaleCorrectors) {\n                if (valuesToRender[key] === undefined)\n                    continue;\n                const { correct, applyTo, isCSSVariable } = scaleCorrectors[key];\n                /**\n                 * Only apply scale correction to the value if we have an\n                 * active projection transform. Otherwise these values become\n                 * vulnerable to distortion if the element changes size without\n                 * a corresponding layout animation.\n                 */\n                const corrected = transform === \"none\"\n                    ? valuesToRender[key]\n                    : correct(valuesToRender[key], lead);\n                if (applyTo) {\n                    const num = applyTo.length;\n                    for (let i = 0; i < num; i++) {\n                        targetStyle[applyTo[i]] = corrected;\n                    }\n                }\n                else {\n                    // If this is a CSS variable, set it directly on the instance.\n                    // Replacing this function from creating styles to setting them\n                    // would be a good place to remove per frame object creation\n                    if (isCSSVariable) {\n                        this.options.visualElement.renderState.vars[key] = corrected;\n                    }\n                    else {\n                        targetStyle[key] = corrected;\n                    }\n                }\n            }\n            /**\n             * Disable pointer events on follow components. This is to ensure\n             * that if a follow component covers a lead component it doesn't block\n             * pointer events on the lead.\n             */\n            if (this.options.layoutId) {\n                targetStyle.pointerEvents =\n                    lead === this\n                        ? resolveMotionValue(styleProp?.pointerEvents) || \"\"\n                        : \"none\";\n            }\n        }\n        clearSnapshot() {\n            this.resumeFrom = this.snapshot = undefined;\n        }\n        // Only run on root\n        resetTree() {\n            this.root.nodes.forEach((node) => node.currentAnimation?.stop());\n            this.root.nodes.forEach(clearMeasurements);\n            this.root.sharedNodes.clear();\n        }\n    };\n}\nfunction updateLayout(node) {\n    node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n    const snapshot = node.resumeFrom?.snapshot || node.snapshot;\n    if (node.isLead() &&\n        node.layout &&\n        snapshot &&\n        node.hasListeners(\"didUpdate\")) {\n        const { layoutBox: layout, measuredBox: measuredLayout } = node.layout;\n        const { animationType } = node.options;\n        const isShared = snapshot.source !== node.layout.source;\n        // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n        // animations for instance if layout=\"size\" and an element has only changed position\n        if (animationType === \"size\") {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(axisSnapshot);\n                axisSnapshot.min = layout[axis].min;\n                axisSnapshot.max = axisSnapshot.min + length;\n            });\n        }\n        else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(layout[axis]);\n                axisSnapshot.max = axisSnapshot.min + length;\n                /**\n                 * Ensure relative target gets resized and rerendererd\n                 */\n                if (node.relativeTarget && !node.currentAnimation) {\n                    node.isProjectionDirty = true;\n                    node.relativeTarget[axis].max =\n                        node.relativeTarget[axis].min + length;\n                }\n            });\n        }\n        const layoutDelta = createDelta();\n        calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n        const visualDelta = createDelta();\n        if (isShared) {\n            calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n        }\n        else {\n            calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n        }\n        const hasLayoutChanged = !isDeltaZero(layoutDelta);\n        let hasRelativeLayoutChanged = false;\n        if (!node.resumeFrom) {\n            const relativeParent = node.getClosestProjectingParent();\n            /**\n             * If the relativeParent is itself resuming from a different element then\n             * the relative snapshot is not relavent\n             */\n            if (relativeParent && !relativeParent.resumeFrom) {\n                const { snapshot: parentSnapshot, layout: parentLayout } = relativeParent;\n                if (parentSnapshot && parentLayout) {\n                    const relativeSnapshot = createBox();\n                    calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n                    const relativeLayout = createBox();\n                    calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n                    if (!boxEqualsRounded(relativeSnapshot, relativeLayout)) {\n                        hasRelativeLayoutChanged = true;\n                    }\n                    if (relativeParent.options.layoutRoot) {\n                        node.relativeTarget = relativeLayout;\n                        node.relativeTargetOrigin = relativeSnapshot;\n                        node.relativeParent = relativeParent;\n                    }\n                }\n            }\n        }\n        node.notifyListeners(\"didUpdate\", {\n            layout,\n            snapshot,\n            delta: visualDelta,\n            layoutDelta,\n            hasLayoutChanged,\n            hasRelativeLayoutChanged,\n        });\n    }\n    else if (node.isLead()) {\n        const { onExitComplete } = node.options;\n        onExitComplete && onExitComplete();\n    }\n    /**\n     * Clearing transition\n     * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n     * and why we need it at all\n     */\n    node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n    /**\n     * Increase debug counter for nodes encountered this frame\n     */\n    if (statsBuffer.value) {\n        metrics.nodes++;\n    }\n    if (!node.parent)\n        return;\n    /**\n     * If this node isn't projecting, propagate isProjectionDirty. It will have\n     * no performance impact but it will allow the next child that *is* projecting\n     * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n     * correcting.\n     */\n    if (!node.isProjecting()) {\n        node.isProjectionDirty = node.parent.isProjectionDirty;\n    }\n    /**\n     * Propagate isSharedProjectionDirty and isTransformDirty\n     * throughout the whole tree. A future revision can take another look at\n     * this but for safety we still recalcualte shared nodes.\n     */\n    node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty ||\n        node.parent.isProjectionDirty ||\n        node.parent.isSharedProjectionDirty));\n    node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n    node.isProjectionDirty =\n        node.isSharedProjectionDirty =\n            node.isTransformDirty =\n                false;\n}\nfunction clearSnapshot(node) {\n    node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n    node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n    node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n    const { visualElement } = node.options;\n    if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n        visualElement.notify(\"BeforeLayoutMeasure\");\n    }\n    node.resetTransform();\n}\nfunction finishAnimation(node) {\n    node.finishAnimation();\n    node.targetDelta = node.relativeTarget = node.target = undefined;\n    node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n    node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n    node.calcProjection();\n}\nfunction resetSkewAndRotation(node) {\n    node.resetSkewAndRotation();\n}\nfunction removeLeadSnapshots(stack) {\n    stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n    output.translate = mixNumber(delta.translate, 0, p);\n    output.scale = mixNumber(delta.scale, 1, p);\n    output.origin = delta.origin;\n    output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n    output.min = mixNumber(from.min, to.min, p);\n    output.max = mixNumber(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n    mixAxis(output.x, from.x, to.x, p);\n    mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n    return (node.animationValues && node.animationValues.opacityExit !== undefined);\n}\nconst defaultLayoutTransition = {\n    duration: 0.45,\n    ease: [0.4, 0, 0.1, 1],\n};\nconst userAgentContains = (string) => typeof navigator !== \"undefined\" &&\n    navigator.userAgent &&\n    navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\")\n    ? Math.round\n    : noop;\nfunction roundAxis(axis) {\n    // Round to the nearest .5 pixels to support subpixel layouts\n    axis.min = roundPoint(axis.min);\n    axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n    roundAxis(box.x);\n    roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n    return (animationType === \"position\" ||\n        (animationType === \"preserve-aspect\" &&\n            !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2)));\n}\nfunction checkNodeWasScrollRoot(node) {\n    return node !== node.root && node.scroll?.wasRoot;\n}\n\nexport { cleanDirtyNodes, createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AAEA,MAAM,UAAU;IACZ,OAAO;IACP,wBAAwB;IACxB,uBAAuB;AAC3B;AACA,MAAM,gBAAgB;IAAC;IAAI;IAAK;IAAK;CAAI;AACzC;;;CAGC,GACD,MAAM,kBAAkB;AACxB,IAAI,KAAK;AACT,SAAS,yBAAyB,GAAG,EAAE,aAAa,EAAE,MAAM,EAAE,qBAAqB;IAC/E,MAAM,EAAE,YAAY,EAAE,GAAG;IACzB,mEAAmE;IACnE,IAAI,YAAY,CAAC,IAAI,EAAE;QACnB,MAAM,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI;QAC/B,cAAc,cAAc,CAAC,KAAK;QAClC,IAAI,uBAAuB;YACvB,qBAAqB,CAAC,IAAI,GAAG;QACjC;IACJ;AACJ;AACA,SAAS,uCAAuC,cAAc;IAC1D,eAAe,yBAAyB,GAAG;IAC3C,IAAI,eAAe,IAAI,KAAK,gBACxB;IACJ,MAAM,EAAE,aAAa,EAAE,GAAG,eAAe,OAAO;IAChD,IAAI,CAAC,eACD;IACJ,MAAM,WAAW,CAAA,GAAA,0MAAA,CAAA,uBAAoB,AAAD,EAAE;IACtC,IAAI,OAAO,2BAA2B,CAAC,UAAU,cAAc;QAC3D,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,eAAe,OAAO;QACnD,OAAO,8BAA8B,CAAC,UAAU,aAAa,kKAAA,CAAA,QAAK,EAAE,CAAC,CAAC,UAAU,QAAQ;IAC5F;IACA,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,IAAI,UAAU,CAAC,OAAO,yBAAyB,EAAE;QAC7C,uCAAuC;IAC3C;AACJ;AACA,SAAS,qBAAqB,EAAE,oBAAoB,EAAE,aAAa,EAAE,aAAa,EAAE,iBAAiB,EAAE,cAAc,EAAG;IACpH,OAAO,MAAM;QACT,YAAY,eAAe,CAAC,CAAC,EAAE,SAAS,iBAAiB,CAAE;YACvD;;aAEC,GACD,IAAI,CAAC,EAAE,GAAG;YACV;;aAEC,GACD,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,iBAAiB,GAAG;YACzB;;;;;aAKC,GACD,IAAI,CAAC,QAAQ,GAAG,IAAI;YACpB;;;aAGC,GACD,IAAI,CAAC,OAAO,GAAG,CAAC;YAChB;;;;aAIC,GACD,IAAI,CAAC,eAAe,GAAG;YACvB,IAAI,CAAC,kBAAkB,GAAG;YAC1B;;;;;aAKC,GACD,IAAI,CAAC,aAAa,GAAG;YACrB;;;aAGC,GACD,IAAI,CAAC,iBAAiB,GAAG;YACzB;;;aAGC,GACD,IAAI,CAAC,uBAAuB,GAAG;YAC/B;;;aAGC,GACD,IAAI,CAAC,gBAAgB,GAAG;YACxB;;aAEC,GACD,IAAI,CAAC,qBAAqB,GAAG;YAC7B,IAAI,CAAC,qBAAqB,GAAG;YAC7B;;;aAGC,GACD,IAAI,CAAC,UAAU,GAAG;YAClB;;aAEC,GACD,IAAI,CAAC,KAAK,GAAG;YACb;;;aAGC,GACD,IAAI,CAAC,UAAU,GAAG;YAClB;;aAEC,GACD,IAAI,CAAC,oBAAoB,GAAG;YAC5B;;;;;aAKC,GACD,IAAI,CAAC,yBAAyB,GAAG;YACjC;;;;;;;aAOC,GACD,IAAI,CAAC,SAAS,GAAG;gBAAE,GAAG;gBAAG,GAAG;YAAE;YAC9B;;aAEC,GACD,IAAI,CAAC,aAAa,GAAG,IAAI;YACzB,IAAI,CAAC,eAAe,GAAG;YACvB,4CAA4C;YAC5C,IAAI,CAAC,eAAe,GAAG;YACvB,IAAI,CAAC,cAAc,GAAG,IAAM,IAAI,CAAC,MAAM;YACvC,IAAI,CAAC,yBAAyB,GAAG;YACjC,IAAI,CAAC,iBAAiB,GAAG;gBACrB,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,IAAI,CAAC,UAAU,GAAG;oBAClB,IAAI,CAAC,iBAAiB;gBAC1B;YACJ;YACA;;;;aAIC,GACD,IAAI,CAAC,gBAAgB,GAAG;gBACpB,IAAI,CAAC,yBAAyB,GAAG;gBACjC;;;iBAGC,GACD,IAAI,+JAAA,CAAA,cAAW,CAAC,KAAK,EAAE;oBACnB,QAAQ,KAAK,GACT,QAAQ,sBAAsB,GAC1B,QAAQ,qBAAqB,GACzB;gBAChB;gBACA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBACnB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBACnB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBACnB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBACnB,IAAI,+JAAA,CAAA,cAAW,CAAC,oBAAoB,EAAE;oBAClC,+JAAA,CAAA,cAAW,CAAC,oBAAoB,CAAC;gBACrC;YACJ;YACA;;aAEC,GACD,IAAI,CAAC,wBAAwB,GAAG;YAChC,IAAI,CAAC,YAAY,GAAG;YACpB,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,iBAAiB,GAAG;YACzB;;aAEC,GACD,iCAAiC;YACjC,IAAI,CAAC,WAAW,GAAG,IAAI;YACvB,IAAI,CAAC,YAAY,GAAG;YACpB,IAAI,CAAC,IAAI,GAAG,SAAS,OAAO,IAAI,IAAI,SAAS,IAAI;YACjD,IAAI,CAAC,IAAI,GAAG,SAAS;mBAAI,OAAO,IAAI;gBAAE;aAAO,GAAG,EAAE;YAClD,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,KAAK,GAAG,SAAS,OAAO,KAAK,GAAG,IAAI;YACzC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;gBACvC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,oBAAoB,GAAG;YACxC;YACA,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAClB,IAAI,CAAC,KAAK,GAAG,IAAI,kLAAA,CAAA,WAAQ;QACjC;QACA,iBAAiB,IAAI,EAAE,OAAO,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO;gBAC/B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,IAAI,yKAAA,CAAA,sBAAmB;YACxD;YACA,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC;QAC5C;QACA,gBAAgB,IAAI,EAAE,GAAG,IAAI,EAAE;YAC3B,MAAM,sBAAsB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YACnD,uBAAuB,oBAAoB,MAAM,IAAI;QACzD;QACA,aAAa,IAAI,EAAE;YACf,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAClC;QACA;;SAEC,GACD,MAAM,QAAQ,EAAE;YACZ,IAAI,IAAI,CAAC,QAAQ,EACb;YACJ,IAAI,CAAC,KAAK,GAAG,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,aAAa,CAAC,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE;YACxD,IAAI,CAAC,QAAQ,GAAG;YAChB,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,OAAO;YACxD,IAAI,iBAAiB,CAAC,cAAc,OAAO,EAAE;gBACzC,cAAc,KAAK,CAAC;YACxB;YACA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;YACxB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI;YAC5C,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,UAAU,QAAQ,GAAG;gBACnD,IAAI,CAAC,aAAa,GAAG;YACzB;YACA,IAAI,sBAAsB;gBACtB,IAAI;gBACJ,IAAI,aAAa;gBACjB,MAAM,sBAAsB,IAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB,GAAG;gBACrE,oEAAoE;gBACpE,kKAAA,CAAA,QAAK,CAAC,IAAI,CAAC;oBACP,aAAa,OAAO,UAAU;gBAClC;gBACA,qBAAqB,UAAU;oBAC3B,MAAM,gBAAgB,OAAO,UAAU;oBACvC,IAAI,kBAAkB,YAClB;oBACJ,aAAa;oBACb,IAAI,CAAC,IAAI,CAAC,qBAAqB,GAAG;oBAClC,eAAe;oBACf,cAAc,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,qBAAqB;oBACzC,IAAI,8KAAA,CAAA,wBAAqB,CAAC,sBAAsB,EAAE;wBAC9C,8KAAA,CAAA,wBAAqB,CAAC,sBAAsB,GAAG;wBAC/C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;oBACvB;gBACJ;YACJ;YACA,IAAI,UAAU;gBACV,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,IAAI;YAC/C;YACA,4DAA4D;YAC5D,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,SACzB,iBACA,CAAC,YAAY,MAAM,GAAG;gBACtB,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,wBAAwB,EAAE,QAAQ,SAAS,EAAG;oBACzG,IAAI,IAAI,CAAC,sBAAsB,IAAI;wBAC/B,IAAI,CAAC,MAAM,GAAG;wBACd,IAAI,CAAC,cAAc,GAAG;wBACtB;oBACJ;oBACA,0CAA0C;oBAC1C,MAAM,mBAAmB,IAAI,CAAC,OAAO,CAAC,UAAU,IAC5C,cAAc,oBAAoB,MAClC;oBACJ,MAAM,EAAE,sBAAsB,EAAE,yBAAyB,EAAG,GAAG,cAAc,QAAQ;oBACrF;;;qBAGC,GACD,MAAM,mBAAmB,CAAC,IAAI,CAAC,YAAY,IACvC,CAAC,CAAA,GAAA,kLAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,YAAY,EAAE;oBACzC;;;;qBAIC,GACD,8BAA8B;oBAC9B;;;;qBAIC,GACD,MAAM,+BAA+B,CAAC,oBAAoB;oBAC1D,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IACvB,IAAI,CAAC,UAAU,IACf,gCACC,oBACG,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB,GAAI;wBACnD,IAAI,IAAI,CAAC,UAAU,EAAE;4BACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU;4BACnC,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG;wBACrC;wBACA,MAAM,mBAAmB;4BACrB,GAAG,CAAA,GAAA,gMAAA,CAAA,qBAAkB,AAAD,EAAE,kBAAkB,SAAS;4BACjD,QAAQ;4BACR,YAAY;wBAChB;wBACA,IAAI,cAAc,kBAAkB,IAChC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;4BACzB,iBAAiB,KAAK,GAAG;4BACzB,iBAAiB,IAAI,GAAG;wBAC5B;wBACA,IAAI,CAAC,cAAc,CAAC;wBACpB;;;yBAGC,GACD,IAAI,CAAC,kBAAkB,CAAC,OAAO;oBACnC,OACK;wBACD;;;;yBAIC,GACD,IAAI,CAAC,kBAAkB;4BACnB,gBAAgB,IAAI;wBACxB;wBACA,IAAI,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;4BAC9C,IAAI,CAAC,OAAO,CAAC,cAAc;wBAC/B;oBACJ;oBACA,IAAI,CAAC,YAAY,GAAG;gBACxB;YACJ;QACJ;QACA,UAAU;YACN,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU;YACxC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;YAC3B,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,SAAS,MAAM,MAAM,CAAC,IAAI;YAC1B,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI;YAC/C,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,aAAa,CAAC,KAAK;YACxB,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,gBAAgB;QACrC;QACA,mBAAmB;QACnB,cAAc;YACV,IAAI,CAAC,qBAAqB,GAAG;QACjC;QACA,gBAAgB;YACZ,IAAI,CAAC,qBAAqB,GAAG;QACjC;QACA,kBAAkB;YACd,OAAO,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB;QACnE;QACA,yBAAyB;YACrB,OAAQ,IAAI,CAAC,kBAAkB,IAC1B,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,MAClD;QACR;QACA,4CAA4C;QAC5C,cAAc;YACV,IAAI,IAAI,CAAC,eAAe,IACpB;YACJ,IAAI,CAAC,UAAU,GAAG;YAClB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;YACjC,IAAI,CAAC,WAAW;QACpB;QACA,uBAAuB;YACnB,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,OAAO;YACtC,OAAO,iBAAiB,cAAc,QAAQ,GAAG,iBAAiB;QACtE;QACA,WAAW,wBAAwB,IAAI,EAAE;YACrC,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG;YAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI;gBAC7B,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc;gBAC1D;YACJ;YACA;;;;;;;;;;;aAWC,GACD,IAAI,OAAO,8BAA8B,IACrC,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACjC,uCAAuC,IAAI;YAC/C;YACA,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW;YAC9C,IAAI,IAAI,CAAC,aAAa,EAClB;YACJ,IAAI,CAAC,aAAa,GAAG;YACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;gBACvC,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;gBACzB,KAAK,oBAAoB,GAAG;gBAC5B,KAAK,YAAY,CAAC;gBAClB,IAAI,KAAK,OAAO,CAAC,UAAU,EAAE;oBACzB,KAAK,UAAU,CAAC;gBACpB;YACJ;YACA,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO;YACzC,IAAI,aAAa,aAAa,CAAC,QAC3B;YACJ,MAAM,oBAAoB,IAAI,CAAC,oBAAoB;YACnD,IAAI,CAAC,0BAA0B,GAAG,oBAC5B,kBAAkB,IAAI,CAAC,YAAY,EAAE,MACrC;YACN,IAAI,CAAC,cAAc;YACnB,yBAAyB,IAAI,CAAC,eAAe,CAAC;QAClD;QACA,SAAS;YACL,IAAI,CAAC,eAAe,GAAG;YACvB,MAAM,mBAAmB,IAAI,CAAC,eAAe;YAC7C,+DAA+D;YAC/D,8DAA8D;YAC9D,qCAAqC;YACrC,IAAI,kBAAkB;gBAClB,IAAI,CAAC,aAAa;gBAClB,IAAI,CAAC,iBAAiB;gBACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBACnB;YACJ;YACA;;aAEC,GACD,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBACnB;YACJ;YACA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW;YACzC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;YACvB,OACK;gBACD,IAAI,CAAC,UAAU,GAAG;gBAClB;;iBAEC,GACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBACnB;;iBAEC,GACD,iDAAiD;gBACjD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;gBACnB;;iBAEC,GACD,8CAA8C;gBAC9C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;YACvB;YACA,IAAI,CAAC,iBAAiB;YACtB;;;;aAIC,GACD,MAAM,MAAM,yKAAA,CAAA,OAAI,CAAC,GAAG;YACpB,kKAAA,CAAA,YAAS,CAAC,KAAK,GAAG,CAAA,GAAA,uJAAA,CAAA,QAAK,AAAD,EAAE,GAAG,OAAO,IAAI,MAAM,kKAAA,CAAA,YAAS,CAAC,SAAS;YAC/D,kKAAA,CAAA,YAAS,CAAC,SAAS,GAAG;YACtB,kKAAA,CAAA,YAAS,CAAC,YAAY,GAAG;YACzB,kKAAA,CAAA,aAAU,CAAC,MAAM,CAAC,OAAO,CAAC,kKAAA,CAAA,YAAS;YACnC,kKAAA,CAAA,aAAU,CAAC,SAAS,CAAC,OAAO,CAAC,kKAAA,CAAA,YAAS;YACtC,kKAAA,CAAA,aAAU,CAAC,MAAM,CAAC,OAAO,CAAC,kKAAA,CAAA,YAAS;YACnC,kKAAA,CAAA,YAAS,CAAC,YAAY,GAAG;QAC7B;QACA,YAAY;YACR,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACvB,IAAI,CAAC,eAAe,GAAG;gBACvB,sKAAA,CAAA,YAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc;YACtC;QACJ;QACA,oBAAoB;YAChB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;QAC7B;QACA,2BAA2B;YACvB,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACjC,IAAI,CAAC,yBAAyB,GAAG;gBACjC,kKAAA,CAAA,QAAK,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,OAAO;YAClD;QACJ;QACA,4BAA4B;YACxB;;;;aAIC,GACD,kKAAA,CAAA,QAAK,CAAC,UAAU,CAAC;gBACb,IAAI,IAAI,CAAC,aAAa,EAAE;oBACpB,IAAI,CAAC,IAAI,CAAC,SAAS;gBACvB,OACK;oBACD,IAAI,CAAC,IAAI,CAAC,iBAAiB;gBAC/B;YACJ;QACJ;QACA;;SAEC,GACD,iBAAiB;YACb,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAC/B;YACJ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO;YAC5B,IAAI,IAAI,CAAC,QAAQ,IACb,CAAC,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,KACvC,CAAC,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,GAAG;gBAC1C,IAAI,CAAC,QAAQ,GAAG;YACpB;QACJ;QACA,eAAe;YACX,IAAI,CAAC,IAAI,CAAC,QAAQ,EACd;YACJ,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC,MAAM,EAAE,KACnD,CAAC,IAAI,CAAC,aAAa,EAAE;gBACrB;YACJ;YACA;;;;;;aAMC,GACD,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;gBAC9C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;oBACvC,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;oBACzB,KAAK,YAAY;gBACrB;YACJ;YACA,MAAM,aAAa,IAAI,CAAC,MAAM;YAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAC3B,IAAI,CAAC,eAAe,GAAG,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;YAC/B,IAAI,CAAC,aAAa,GAAG;YACrB,IAAI,CAAC,eAAe,GAAG;YACvB,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,CAAC,MAAM,CAAC,SAAS;YACrD,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,OAAO;YACtC,iBACI,cAAc,MAAM,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,aAAa,WAAW,SAAS,GAAG;QACzG;QACA,aAAa,QAAQ,SAAS,EAAE;YAC5B,IAAI,mBAAmB,QAAQ,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ;YACzE,IAAI,IAAI,CAAC,MAAM,IACX,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,IACjD,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,OAAO;gBAC7B,mBAAmB;YACvB;YACA,IAAI,oBAAoB,IAAI,CAAC,QAAQ,EAAE;gBACnC,MAAM,SAAS,kBAAkB,IAAI,CAAC,QAAQ;gBAC9C,IAAI,CAAC,MAAM,GAAG;oBACV,aAAa,IAAI,CAAC,IAAI,CAAC,WAAW;oBAClC;oBACA;oBACA,QAAQ,cAAc,IAAI,CAAC,QAAQ;oBACnC,SAAS,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;gBAChD;YACJ;QACJ;QACA,iBAAiB;YACb,IAAI,CAAC,gBACD;YACJ,MAAM,mBAAmB,IAAI,CAAC,aAAa,IACvC,IAAI,CAAC,oBAAoB,IACzB,IAAI,CAAC,OAAO,CAAC,mBAAmB;YACpC,MAAM,gBAAgB,IAAI,CAAC,eAAe,IAAI,CAAC,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,eAAe;YAC/E,MAAM,oBAAoB,IAAI,CAAC,oBAAoB;YACnD,MAAM,yBAAyB,oBACzB,kBAAkB,IAAI,CAAC,YAAY,EAAE,MACrC;YACN,MAAM,8BAA8B,2BAA2B,IAAI,CAAC,0BAA0B;YAC9F,IAAI,oBACA,IAAI,CAAC,QAAQ,IACb,CAAC,iBACG,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,YAAY,KAC9B,2BAA2B,GAAG;gBAClC,eAAe,IAAI,CAAC,QAAQ,EAAE;gBAC9B,IAAI,CAAC,oBAAoB,GAAG;gBAC5B,IAAI,CAAC,cAAc;YACvB;QACJ;QACA,QAAQ,kBAAkB,IAAI,EAAE;YAC5B,MAAM,UAAU,IAAI,CAAC,cAAc;YACnC,IAAI,YAAY,IAAI,CAAC,mBAAmB,CAAC;YACzC;;;;aAIC,GACD,IAAI,iBAAiB;gBACjB,YAAY,IAAI,CAAC,eAAe,CAAC;YACrC;YACA,SAAS;YACT,OAAO;gBACH,aAAa,IAAI,CAAC,IAAI,CAAC,WAAW;gBAClC,aAAa;gBACb;gBACA,cAAc,CAAC;gBACf,QAAQ,IAAI,CAAC,EAAE;YACnB;QACJ;QACA,iBAAiB;YACb,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,OAAO;YACtC,IAAI,CAAC,eACD,OAAO,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;YACnB,MAAM,MAAM,cAAc,kBAAkB;YAC5C,MAAM,kBAAkB,IAAI,CAAC,MAAM,EAAE,WAAW,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAC/D,IAAI,CAAC,iBAAiB;gBAClB,2DAA2D;gBAC3D,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI;gBAC5B,IAAI,QAAQ;oBACR,CAAA,GAAA,2LAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC;oBACpC,CAAA,GAAA,2LAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC;gBACxC;YACJ;YACA,OAAO;QACX;QACA,oBAAoB,GAAG,EAAE;YACrB,MAAM,mBAAmB,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;YACjC,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB;YAC9B,IAAI,IAAI,CAAC,MAAM,EAAE,SAAS;gBACtB,OAAO;YACX;YACA;;;aAGC,GACD,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;gBACvC,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;gBACzB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;gBAC5B,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,UAAU,QAAQ,YAAY,EAAE;oBACtD;;;qBAGC,GACD,IAAI,OAAO,OAAO,EAAE;wBAChB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB;oBAClC;oBACA,CAAA,GAAA,2LAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC;oBACjD,CAAA,GAAA,2LAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC;gBACrD;YACJ;YACA,OAAO;QACX;QACA,eAAe,GAAG,EAAE,gBAAgB,KAAK,EAAE;YACvC,MAAM,iBAAiB,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;YAC/B,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB;YAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;gBACvC,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;gBACzB,IAAI,CAAC,iBACD,KAAK,OAAO,CAAC,YAAY,IACzB,KAAK,MAAM,IACX,SAAS,KAAK,IAAI,EAAE;oBACpB,CAAA,GAAA,2LAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;wBACzB,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;wBACxB,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC5B;gBACJ;gBACA,IAAI,CAAC,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,KAAK,YAAY,GAC/B;gBACJ,CAAA,GAAA,2LAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,KAAK,YAAY;YAClD;YACA,IAAI,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,YAAY,GAAG;gBACjC,CAAA,GAAA,2LAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,IAAI,CAAC,YAAY;YAClD;YACA,OAAO;QACX;QACA,gBAAgB,GAAG,EAAE;YACjB,MAAM,sBAAsB,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;YACpC,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,qBAAqB;YACjC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;gBACvC,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;gBACzB,IAAI,CAAC,KAAK,QAAQ,EACd;gBACJ,IAAI,CAAC,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,KAAK,YAAY,GAC/B;gBACJ,CAAA,GAAA,0LAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,YAAY,KAAK,KAAK,cAAc;gBAClD,MAAM,YAAY,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;gBAC1B,MAAM,UAAU,KAAK,cAAc;gBACnC,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,WAAW;gBACvB,CAAA,GAAA,4LAAA,CAAA,sBAAmB,AAAD,EAAE,qBAAqB,KAAK,YAAY,EAAE,KAAK,QAAQ,GAAG,KAAK,QAAQ,CAAC,SAAS,GAAG,WAAW;YACrH;YACA,IAAI,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,YAAY,GAAG;gBACjC,CAAA,GAAA,4LAAA,CAAA,sBAAmB,AAAD,EAAE,qBAAqB,IAAI,CAAC,YAAY;YAC9D;YACA,OAAO;QACX;QACA,eAAe,KAAK,EAAE;YAClB,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,IAAI,CAAC,wBAAwB;YAClC,IAAI,CAAC,iBAAiB,GAAG;QAC7B;QACA,WAAW,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,GAAG;gBACX,GAAG,IAAI,CAAC,OAAO;gBACf,GAAG,OAAO;gBACV,WAAW,QAAQ,SAAS,KAAK,YAAY,QAAQ,SAAS,GAAG;YACrE;QACJ;QACA,oBAAoB;YAChB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,0BAA0B,GAAG;YAClC,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,aAAa,GAAG;QACzB;QACA,qCAAqC;YACjC,IAAI,CAAC,IAAI,CAAC,cAAc,EACpB;YACJ;;;;;aAKC,GACD,IAAI,IAAI,CAAC,cAAc,CAAC,wBAAwB,KAC5C,kKAAA,CAAA,YAAS,CAAC,SAAS,EAAE;gBACrB,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC;YAC3C;QACJ;QACA,mBAAmB,qBAAqB,KAAK,EAAE;YAC3C;;;;aAIC,GACD,MAAM,OAAO,IAAI,CAAC,OAAO;YACzB,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,KAAK,iBAAiB;YAC1E,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,KAAK,gBAAgB;YACvE,IAAI,CAAC,uBAAuB,IAAI,CAAC,IAAI,CAAC,uBAAuB,GAAG,KAAK,uBAAuB;YAC5F,MAAM,WAAW,QAAQ,IAAI,CAAC,YAAY,KAAK,IAAI,KAAK;YACxD;;;aAGC,GACD,MAAM,UAAU,CAAC,CAAC,sBACb,YAAY,IAAI,CAAC,uBAAuB,IACzC,IAAI,CAAC,iBAAiB,IACtB,IAAI,CAAC,MAAM,EAAE,qBACb,IAAI,CAAC,8BAA8B,IACnC,IAAI,CAAC,IAAI,CAAC,qBAAqB;YACnC,IAAI,SACA;YACJ,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,OAAO;YACzC;;aAEC,GACD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,UAAU,QAAQ,GACpC;YACJ,IAAI,CAAC,wBAAwB,GAAG,kKAAA,CAAA,YAAS,CAAC,SAAS;YACnD;;;;aAIC,GACD,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC3C,MAAM,iBAAiB,IAAI,CAAC,0BAA0B;gBACtD,IAAI,kBACA,eAAe,MAAM,IACrB,IAAI,CAAC,iBAAiB,KAAK,GAAG;oBAC9B,IAAI,CAAC,cAAc,GAAG;oBACtB,IAAI,CAAC,kCAAkC;oBACvC,IAAI,CAAC,cAAc,GAAG,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;oBAC9B,IAAI,CAAC,oBAAoB,GAAG,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;oBACpC,CAAA,GAAA,0LAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,eAAe,MAAM,CAAC,SAAS;oBACtG,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,oBAAoB;gBAC9D,OACK;oBACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG;gBAChD;YACJ;YACA;;;aAGC,GACD,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,WAAW,EACzC;YACJ;;aAEC,GACD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACd,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;gBACtB,IAAI,CAAC,oBAAoB,GAAG,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;YACxC;YACA;;aAEC,GACD,IAAI,IAAI,CAAC,cAAc,IACnB,IAAI,CAAC,oBAAoB,IACzB,IAAI,CAAC,cAAc,IACnB,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;gBAC5B,IAAI,CAAC,kCAAkC;gBACvC,CAAA,GAAA,0LAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;YAC5E;;iBAEC,GACL,OACK,IAAI,IAAI,CAAC,WAAW,EAAE;gBACvB,IAAI,QAAQ,IAAI,CAAC,YAAY,GAAG;oBAC5B,kDAAkD;oBAClD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS;gBAC3D,OACK;oBACD,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;gBAClD;gBACA,CAAA,GAAA,2LAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW;YAC/C,OACK;gBACD;;iBAEC,GACD,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;YAClD;YACA;;aAEC,GACD,IAAI,IAAI,CAAC,8BAA8B,EAAE;gBACrC,IAAI,CAAC,8BAA8B,GAAG;gBACtC,MAAM,iBAAiB,IAAI,CAAC,0BAA0B;gBACtD,IAAI,kBACA,QAAQ,eAAe,YAAY,MAC/B,QAAQ,IAAI,CAAC,YAAY,KAC7B,CAAC,eAAe,OAAO,CAAC,YAAY,IACpC,eAAe,MAAM,IACrB,IAAI,CAAC,iBAAiB,KAAK,GAAG;oBAC9B,IAAI,CAAC,cAAc,GAAG;oBACtB,IAAI,CAAC,kCAAkC;oBACvC,IAAI,CAAC,cAAc,GAAG,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;oBAC9B,IAAI,CAAC,oBAAoB,GAAG,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;oBACpC,CAAA,GAAA,0LAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,MAAM,EAAE,eAAe,MAAM;oBAClF,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,oBAAoB;gBAC9D,OACK;oBACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG;gBAChD;YACJ;YACA;;aAEC,GACD,IAAI,+JAAA,CAAA,cAAW,CAAC,KAAK,EAAE;gBACnB,QAAQ,sBAAsB;YAClC;QACJ;QACA,6BAA6B;YACzB,IAAI,CAAC,IAAI,CAAC,MAAM,IACZ,CAAA,GAAA,0LAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,KACjC,CAAA,GAAA,0LAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG;gBAC1C,OAAO;YACX;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI;gBAC5B,OAAO,IAAI,CAAC,MAAM;YACtB,OACK;gBACD,OAAO,IAAI,CAAC,MAAM,CAAC,0BAA0B;YACjD;QACJ;QACA,eAAe;YACX,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc,IAC/B,IAAI,CAAC,WAAW,IAChB,IAAI,CAAC,OAAO,CAAC,UAAU,KACvB,IAAI,CAAC,MAAM;QACnB;QACA,iBAAiB;YACb,MAAM,OAAO,IAAI,CAAC,OAAO;YACzB,MAAM,WAAW,QAAQ,IAAI,CAAC,YAAY,KAAK,IAAI,KAAK;YACxD,IAAI,UAAU;YACd;;;aAGC,GACD,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,EAAE,mBAAmB;gBAC1D,UAAU;YACd;YACA;;;aAGC,GACD,IAAI,YACA,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,gBAAgB,GAAG;gBACzD,UAAU;YACd;YACA;;;aAGC,GACD,IAAI,IAAI,CAAC,wBAAwB,KAAK,kKAAA,CAAA,YAAS,CAAC,SAAS,EAAE;gBACvD,UAAU;YACd;YACA,IAAI,SACA;YACJ,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,OAAO;YACzC;;;aAGC,GACD,IAAI,CAAC,eAAe,GAAG,QAAQ,AAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,IACtE,IAAI,CAAC,gBAAgB,IACrB,IAAI,CAAC,gBAAgB;YACzB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,GAAG;YAC7C;YACA,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,UAAU,QAAQ,GACpC;YACJ;;;aAGC,GACD,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;YACvD;;aAEC,GACD,MAAM,iBAAiB,IAAI,CAAC,SAAS,CAAC,CAAC;YACvC,MAAM,iBAAiB,IAAI,CAAC,SAAS,CAAC,CAAC;YACvC;;;aAGC,GACD,CAAA,GAAA,2LAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAE;YACjE;;;aAGC,GACD,IAAI,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,IACZ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG;gBACpD,KAAK,MAAM,GAAG,KAAK,MAAM,CAAC,SAAS;gBACnC,KAAK,oBAAoB,GAAG,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;YACxC;YACA,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,IAAI,CAAC,QAAQ;gBACT;;;;iBAIC,GACD,IAAI,IAAI,CAAC,mBAAmB,EAAE;oBAC1B,IAAI,CAAC,sBAAsB;oBAC3B,IAAI,CAAC,cAAc;gBACvB;gBACA;YACJ;YACA,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBACpD,IAAI,CAAC,sBAAsB;YAC/B,OACK;gBACD,CAAA,GAAA,iLAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBACpE,CAAA,GAAA,iLAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YACxE;YACA;;;;;;;;aAQC,GACD,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,QAAQ,IAAI,CAAC,YAAY;YAClF,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,kBACrB,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,kBACrB,CAAC,CAAA,GAAA,kLAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,KACnE,CAAC,CAAA,GAAA,kLAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG;gBACtE,IAAI,CAAC,YAAY,GAAG;gBACpB,IAAI,CAAC,cAAc;gBACnB,IAAI,CAAC,eAAe,CAAC,oBAAoB;YAC7C;YACA;;aAEC,GACD,IAAI,+JAAA,CAAA,cAAW,CAAC,KAAK,EAAE;gBACnB,QAAQ,qBAAqB;YACjC;QACJ;QACA,OAAO;YACH,IAAI,CAAC,SAAS,GAAG;QACjB,wBAAwB;QAC5B;QACA,OAAO;YACH,IAAI,CAAC,SAAS,GAAG;QACjB,wBAAwB;QAC5B;QACA,eAAe,YAAY,IAAI,EAAE;YAC7B,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;YAC5B,IAAI,WAAW;gBACX,MAAM,QAAQ,IAAI,CAAC,QAAQ;gBAC3B,SAAS,MAAM,cAAc;YACjC;YACA,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;gBAClD,IAAI,CAAC,YAAY,GAAG;YACxB;QACJ;QACA,yBAAyB;YACrB,IAAI,CAAC,mBAAmB,GAAG,CAAA,GAAA,mLAAA,CAAA,cAAW,AAAD;YACrC,IAAI,CAAC,eAAe,GAAG,CAAA,GAAA,mLAAA,CAAA,cAAW,AAAD;YACjC,IAAI,CAAC,4BAA4B,GAAG,CAAA,GAAA,mLAAA,CAAA,cAAW,AAAD;QAClD;QACA,mBAAmB,KAAK,EAAE,+BAA+B,KAAK,EAAE;YAC5D,MAAM,WAAW,IAAI,CAAC,QAAQ;YAC9B,MAAM,uBAAuB,WAAW,SAAS,YAAY,GAAG,CAAC;YACjE,MAAM,cAAc;gBAAE,GAAG,IAAI,CAAC,YAAY;YAAC;YAC3C,MAAM,cAAc,CAAA,GAAA,mLAAA,CAAA,cAAW,AAAD;YAC9B,IAAI,CAAC,IAAI,CAAC,cAAc,IACpB,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE;gBACzC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,GAAG;YACtD;YACA,IAAI,CAAC,8BAA8B,GAAG,CAAC;YACvC,MAAM,iBAAiB,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;YAC/B,MAAM,iBAAiB,WAAW,SAAS,MAAM,GAAG;YACpD,MAAM,eAAe,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;YACxD,MAAM,0BAA0B,mBAAmB;YACnD,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,MAAM,eAAe,CAAC,SAAS,MAAM,OAAO,CAAC,MAAM,IAAI;YACvD,MAAM,yBAAyB,QAAQ,2BACnC,CAAC,gBACD,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,QAC3B,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACpB,IAAI,CAAC,iBAAiB,GAAG;YACzB,IAAI;YACJ,IAAI,CAAC,cAAc,GAAG,CAAC;gBACnB,MAAM,WAAW,SAAS;gBAC1B,aAAa,YAAY,CAAC,EAAE,MAAM,CAAC,EAAE;gBACrC,aAAa,YAAY,CAAC,EAAE,MAAM,CAAC,EAAE;gBACrC,IAAI,CAAC,cAAc,CAAC;gBACpB,IAAI,IAAI,CAAC,cAAc,IACnB,IAAI,CAAC,oBAAoB,IACzB,IAAI,CAAC,MAAM,IACX,IAAI,CAAC,cAAc,IACnB,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;oBAC5B,CAAA,GAAA,0LAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS;oBAChG,OAAO,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,oBAAoB,EAAE,gBAAgB;oBACvE;;;qBAGC,GACD,IAAI,sBACA,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,cAAc,EAAE,qBAAqB;wBACpD,IAAI,CAAC,iBAAiB,GAAG;oBAC7B;oBACA,IAAI,CAAC,oBACD,qBAAqB,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;oBACjC,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,oBAAoB,IAAI,CAAC,cAAc;gBACvD;gBACA,IAAI,yBAAyB;oBACzB,IAAI,CAAC,eAAe,GAAG;oBACvB,CAAA,GAAA,2LAAA,CAAA,YAAS,AAAD,EAAE,aAAa,sBAAsB,IAAI,CAAC,YAAY,EAAE,UAAU,wBAAwB;gBACtG;gBACA,IAAI,CAAC,IAAI,CAAC,wBAAwB;gBAClC,IAAI,CAAC,cAAc;gBACnB,IAAI,CAAC,iBAAiB,GAAG;YAC7B;YACA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,OAAO;QACzD;QACA,eAAe,OAAO,EAAE;YACpB,IAAI,CAAC,eAAe,CAAC;YACrB,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,YAAY,EAAE,kBAAkB;YACrC,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,gBAAgB;gBACjC,IAAI,CAAC,gBAAgB,GAAG;YAC5B;YACA;;;;aAIC,GACD,IAAI,CAAC,gBAAgB,GAAG,kKAAA,CAAA,QAAK,CAAC,MAAM,CAAC;gBACjC,8KAAA,CAAA,wBAAqB,CAAC,sBAAsB,GAAG;gBAC/C,2KAAA,CAAA,mBAAgB,CAAC,MAAM;gBACvB,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,EAAE;gBACtD,IAAI,CAAC,gBAAgB,GAAG,CAAA,GAAA,0LAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE;oBAAC;oBAAG;iBAAK,EAAE;oBACpE,GAAG,OAAO;oBACV,UAAU;oBACV,QAAQ;oBACR,UAAU,CAAC;wBACP,IAAI,CAAC,cAAc,CAAC;wBACpB,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC;oBACzC;oBACA,QAAQ;wBACJ,2KAAA,CAAA,mBAAgB,CAAC,MAAM;oBAC3B;oBACA,YAAY;wBACR,2KAAA,CAAA,mBAAgB,CAAC,MAAM;wBACvB,QAAQ,UAAU,IAAI,QAAQ,UAAU;wBACxC,IAAI,CAAC,iBAAiB;oBAC1B;gBACJ;gBACA,IAAI,IAAI,CAAC,YAAY,EAAE;oBACnB,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB;gBAC9D;gBACA,IAAI,CAAC,gBAAgB,GAAG;YAC5B;QACJ;QACA,oBAAoB;YAChB,IAAI,IAAI,CAAC,YAAY,EAAE;gBACnB,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG;gBACrC,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG;YACxC;YACA,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,SAAS,MAAM,qBAAqB;YACpC,IAAI,CAAC,YAAY,GACb,IAAI,CAAC,gBAAgB,GACjB,IAAI,CAAC,eAAe,GAChB;YACZ,IAAI,CAAC,eAAe,CAAC;QACzB;QACA,kBAAkB;YACd,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC;gBAC3C,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAC9B;YACA,IAAI,CAAC,iBAAiB;QAC1B;QACA,0BAA0B;YACtB,MAAM,OAAO,IAAI,CAAC,OAAO;YACzB,IAAI,EAAE,oBAAoB,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;YAC7D,IAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,QACrC;YACJ;;;;aAIC,GACD,IAAI,IAAI,KAAK,QACT,IAAI,CAAC,MAAM,IACX,UACA,0BAA0B,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,SAAS,GAAG;gBAChG,SAAS,IAAI,CAAC,MAAM,IAAI,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;gBAChC,MAAM,UAAU,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAClD,OAAO,CAAC,CAAC,GAAG,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG;gBAChC,OAAO,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG;gBAC9B,MAAM,UAAU,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAClD,OAAO,CAAC,CAAC,GAAG,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG;gBAChC,OAAO,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,GAAG;YAClC;YACA,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,sBAAsB;YAClC;;;;aAIC,GACD,CAAA,GAAA,2LAAA,CAAA,eAAY,AAAD,EAAE,sBAAsB;YACnC;;;;;aAKC,GACD,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,4BAA4B,EAAE,IAAI,CAAC,eAAe,EAAE,sBAAsB;QAChG;QACA,mBAAmB,QAAQ,EAAE,IAAI,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW;gBACjC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,IAAI,gLAAA,CAAA,YAAS;YAChD;YACA,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YACnC,MAAM,GAAG,CAAC;YACV,MAAM,SAAS,KAAK,OAAO,CAAC,sBAAsB;YAClD,KAAK,OAAO,CAAC;gBACT,YAAY,SAAS,OAAO,UAAU,GAAG;gBACzC,uBAAuB,UAAU,OAAO,2BAA2B,GAC7D,OAAO,2BAA2B,CAAC,QACnC;YACV;QACJ;QACA,SAAS;YACL,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,OAAO,QAAQ,MAAM,IAAI,KAAK,IAAI,GAAG;QACzC;QACA,UAAU;YACN,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,OAAO;YACjC,OAAO,WAAW,IAAI,CAAC,QAAQ,IAAI,QAAQ,IAAI,GAAG,IAAI;QAC1D;QACA,cAAc;YACV,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,OAAO;YACjC,OAAO,WAAW,IAAI,CAAC,QAAQ,IAAI,WAAW;QAClD;QACA,WAAW;YACP,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,OAAO;YACjC,IAAI,UACA,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QACzC;QACA,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,qBAAqB,EAAG,GAAG,CAAC,CAAC,EAAE;YAC7D,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,IAAI,OACA,MAAM,OAAO,CAAC,IAAI,EAAE;YACxB,IAAI,YAAY;gBACZ,IAAI,CAAC,eAAe,GAAG;gBACvB,IAAI,CAAC,UAAU,GAAG;YACtB;YACA,IAAI,YACA,IAAI,CAAC,UAAU,CAAC;gBAAE;YAAW;QACrC;QACA,WAAW;YACP,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,IAAI,OAAO;gBACP,OAAO,MAAM,QAAQ,CAAC,IAAI;YAC9B,OACK;gBACD,OAAO;YACX;QACJ;QACA,uBAAuB;YACnB,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC,OAAO;YACtC,IAAI,CAAC,eACD;YACJ,+FAA+F;YAC/F,IAAI,yBAAyB;YAC7B;;;aAGC,GACD,MAAM,EAAE,YAAY,EAAE,GAAG;YACzB,IAAI,aAAa,CAAC,IACd,aAAa,MAAM,IACnB,aAAa,OAAO,IACpB,aAAa,OAAO,IACpB,aAAa,OAAO,IACpB,aAAa,KAAK,IAClB,aAAa,KAAK,EAAE;gBACpB,yBAAyB;YAC7B;YACA,iEAAiE;YACjE,IAAI,CAAC,wBACD;YACJ,MAAM,cAAc,CAAC;YACrB,IAAI,aAAa,CAAC,EAAE;gBAChB,yBAAyB,KAAK,eAAe,aAAa,IAAI,CAAC,eAAe;YAClF;YACA,6DAA6D;YAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;gBAC3C,yBAAyB,CAAC,MAAM,EAAE,aAAa,CAAC,EAAE,EAAE,EAAE,eAAe,aAAa,IAAI,CAAC,eAAe;gBACtG,yBAAyB,CAAC,IAAI,EAAE,aAAa,CAAC,EAAE,EAAE,EAAE,eAAe,aAAa,IAAI,CAAC,eAAe;YACxG;YACA,qFAAqF;YACrF,YAAY;YACZ,cAAc,MAAM;YACpB,mCAAmC;YACnC,IAAK,MAAM,OAAO,YAAa;gBAC3B,cAAc,cAAc,CAAC,KAAK,WAAW,CAAC,IAAI;gBAClD,IAAI,IAAI,CAAC,eAAe,EAAE;oBACtB,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI;gBAChD;YACJ;YACA,uEAAuE;YACvE,uDAAuD;YACvD,cAAc,cAAc;QAChC;QACA,sBAAsB,WAAW,EACjC,SAAS,EAAE;YACP,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,EAC5B;YACJ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACjB,YAAY,UAAU,GAAG;gBACzB;YACJ;YACA,MAAM,oBAAoB,IAAI,CAAC,oBAAoB;YACnD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,GAAG;gBAClB,YAAY,UAAU,GAAG;gBACzB,YAAY,OAAO,GAAG;gBACtB,YAAY,aAAa,GACrB,CAAA,GAAA,+LAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,kBAAkB;gBACpD,YAAY,SAAS,GAAG,oBAClB,kBAAkB,IAAI,CAAC,YAAY,EAAE,MACrC;gBACN;YACJ;YACA,MAAM,OAAO,IAAI,CAAC,OAAO;YACzB,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,KAAK,MAAM,EAAE;gBACvD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;oBACvB,YAAY,OAAO,GACf,IAAI,CAAC,YAAY,CAAC,OAAO,KAAK,YACxB,IAAI,CAAC,YAAY,CAAC,OAAO,GACzB;oBACV,YAAY,aAAa,GACrB,CAAA,GAAA,+LAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,kBAAkB;gBACxD;gBACA,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,YAAY,GAAG;oBACvD,YAAY,SAAS,GAAG,oBAClB,kBAAkB,CAAC,GAAG,MACtB;oBACN,IAAI,CAAC,YAAY,GAAG;gBACxB;gBACA;YACJ;YACA,YAAY,UAAU,GAAG;YACzB,MAAM,iBAAiB,KAAK,eAAe,IAAI,KAAK,YAAY;YAChE,IAAI,CAAC,uBAAuB;YAC5B,IAAI,YAAY,CAAA,GAAA,oLAAA,CAAA,2BAAwB,AAAD,EAAE,IAAI,CAAC,4BAA4B,EAAE,IAAI,CAAC,SAAS,EAAE;YAC5F,IAAI,mBAAmB;gBACnB,YAAY,kBAAkB,gBAAgB;YAClD;YACA,YAAY,SAAS,GAAG;YACxB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,eAAe;YACrC,YAAY,eAAe,GAAG,GAAG,EAAE,MAAM,GAAG,IAAI,EAAE,EAAE,EAAE,MAAM,GAAG,IAAI,GAAG,CAAC;YACvE,IAAI,KAAK,eAAe,EAAE;gBACtB;;;iBAGC,GACD,YAAY,OAAO,GACf,SAAS,IAAI,GACP,eAAe,OAAO,IACpB,IAAI,CAAC,YAAY,CAAC,OAAO,IACzB,IACF,IAAI,CAAC,eAAe,GAChB,IAAI,CAAC,YAAY,CAAC,OAAO,GACzB,eAAe,WAAW;YAC5C,OACK;gBACD;;;iBAGC,GACD,YAAY,OAAO,GACf,SAAS,IAAI,GACP,eAAe,OAAO,KAAK,YACvB,eAAe,OAAO,GACtB,KACJ,eAAe,WAAW,KAAK,YAC3B,eAAe,WAAW,GAC1B;YAClB;YACA;;aAEC,GACD,IAAK,MAAM,OAAO,8LAAA,CAAA,kBAAe,CAAE;gBAC/B,IAAI,cAAc,CAAC,IAAI,KAAK,WACxB;gBACJ,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,8LAAA,CAAA,kBAAe,CAAC,IAAI;gBAChE;;;;;iBAKC,GACD,MAAM,YAAY,cAAc,SAC1B,cAAc,CAAC,IAAI,GACnB,QAAQ,cAAc,CAAC,IAAI,EAAE;gBACnC,IAAI,SAAS;oBACT,MAAM,MAAM,QAAQ,MAAM;oBAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;wBAC1B,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG;oBAC9B;gBACJ,OACK;oBACD,8DAA8D;oBAC9D,+DAA+D;oBAC/D,4DAA4D;oBAC5D,IAAI,eAAe;wBACf,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG;oBACvD,OACK;wBACD,WAAW,CAAC,IAAI,GAAG;oBACvB;gBACJ;YACJ;YACA;;;;aAIC,GACD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;gBACvB,YAAY,aAAa,GACrB,SAAS,IAAI,GACP,CAAA,GAAA,+LAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW,kBAAkB,KAChD;YACd;QACJ;QACA,gBAAgB;YACZ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG;QACtC;QACA,mBAAmB;QACnB,YAAY;YACR,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,OAAS,KAAK,gBAAgB,EAAE;YACzD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK;QAC/B;IACJ;AACJ;AACA,SAAS,aAAa,IAAI;IACtB,KAAK,YAAY;AACrB;AACA,SAAS,mBAAmB,IAAI;IAC5B,MAAM,WAAW,KAAK,UAAU,EAAE,YAAY,KAAK,QAAQ;IAC3D,IAAI,KAAK,MAAM,MACX,KAAK,MAAM,IACX,YACA,KAAK,YAAY,CAAC,cAAc;QAChC,MAAM,EAAE,WAAW,MAAM,EAAE,aAAa,cAAc,EAAE,GAAG,KAAK,MAAM;QACtE,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,OAAO;QACtC,MAAM,WAAW,SAAS,MAAM,KAAK,KAAK,MAAM,CAAC,MAAM;QACvD,4EAA4E;QAC5E,oFAAoF;QACpF,IAAI,kBAAkB,QAAQ;YAC1B,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;gBACN,MAAM,eAAe,WACf,SAAS,WAAW,CAAC,KAAK,GAC1B,SAAS,SAAS,CAAC,KAAK;gBAC9B,MAAM,SAAS,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE;gBAC1B,aAAa,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG;gBACnC,aAAa,GAAG,GAAG,aAAa,GAAG,GAAG;YAC1C;QACJ,OACK,IAAI,0BAA0B,eAAe,SAAS,SAAS,EAAE,SAAS;YAC3E,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;gBACN,MAAM,eAAe,WACf,SAAS,WAAW,CAAC,KAAK,GAC1B,SAAS,SAAS,CAAC,KAAK;gBAC9B,MAAM,SAAS,CAAA,GAAA,0LAAA,CAAA,aAAU,AAAD,EAAE,MAAM,CAAC,KAAK;gBACtC,aAAa,GAAG,GAAG,aAAa,GAAG,GAAG;gBACtC;;iBAEC,GACD,IAAI,KAAK,cAAc,IAAI,CAAC,KAAK,gBAAgB,EAAE;oBAC/C,KAAK,iBAAiB,GAAG;oBACzB,KAAK,cAAc,CAAC,KAAK,CAAC,GAAG,GACzB,KAAK,cAAc,CAAC,KAAK,CAAC,GAAG,GAAG;gBACxC;YACJ;QACJ;QACA,MAAM,cAAc,CAAA,GAAA,mLAAA,CAAA,cAAW,AAAD;QAC9B,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa,QAAQ,SAAS,SAAS;QACpD,MAAM,cAAc,CAAA,GAAA,mLAAA,CAAA,cAAW,AAAD;QAC9B,IAAI,UAAU;YACV,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa,KAAK,cAAc,CAAC,gBAAgB,OAAO,SAAS,WAAW;QAC7F,OACK;YACD,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa,QAAQ,SAAS,SAAS;QACxD;QACA,MAAM,mBAAmB,CAAC,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,2BAA2B;QAC/B,IAAI,CAAC,KAAK,UAAU,EAAE;YAClB,MAAM,iBAAiB,KAAK,0BAA0B;YACtD;;;aAGC,GACD,IAAI,kBAAkB,CAAC,eAAe,UAAU,EAAE;gBAC9C,MAAM,EAAE,UAAU,cAAc,EAAE,QAAQ,YAAY,EAAE,GAAG;gBAC3D,IAAI,kBAAkB,cAAc;oBAChC,MAAM,mBAAmB,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;oBACjC,CAAA,GAAA,0LAAA,CAAA,uBAAoB,AAAD,EAAE,kBAAkB,SAAS,SAAS,EAAE,eAAe,SAAS;oBACnF,MAAM,iBAAiB,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;oBAC/B,CAAA,GAAA,0LAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,QAAQ,aAAa,SAAS;oBACnE,IAAI,CAAC,CAAA,GAAA,kLAAA,CAAA,mBAAgB,AAAD,EAAE,kBAAkB,iBAAiB;wBACrD,2BAA2B;oBAC/B;oBACA,IAAI,eAAe,OAAO,CAAC,UAAU,EAAE;wBACnC,KAAK,cAAc,GAAG;wBACtB,KAAK,oBAAoB,GAAG;wBAC5B,KAAK,cAAc,GAAG;oBAC1B;gBACJ;YACJ;QACJ;QACA,KAAK,eAAe,CAAC,aAAa;YAC9B;YACA;YACA,OAAO;YACP;YACA;YACA;QACJ;IACJ,OACK,IAAI,KAAK,MAAM,IAAI;QACpB,MAAM,EAAE,cAAc,EAAE,GAAG,KAAK,OAAO;QACvC,kBAAkB;IACtB;IACA;;;;KAIC,GACD,KAAK,OAAO,CAAC,UAAU,GAAG;AAC9B;AACA,SAAS,oBAAoB,IAAI;IAC7B;;KAEC,GACD,IAAI,+JAAA,CAAA,cAAW,CAAC,KAAK,EAAE;QACnB,QAAQ,KAAK;IACjB;IACA,IAAI,CAAC,KAAK,MAAM,EACZ;IACJ;;;;;KAKC,GACD,IAAI,CAAC,KAAK,YAAY,IAAI;QACtB,KAAK,iBAAiB,GAAG,KAAK,MAAM,CAAC,iBAAiB;IAC1D;IACA;;;;KAIC,GACD,KAAK,uBAAuB,IAAI,CAAC,KAAK,uBAAuB,GAAG,QAAQ,KAAK,iBAAiB,IAC1F,KAAK,MAAM,CAAC,iBAAiB,IAC7B,KAAK,MAAM,CAAC,uBAAuB,CAAC;IACxC,KAAK,gBAAgB,IAAI,CAAC,KAAK,gBAAgB,GAAG,KAAK,MAAM,CAAC,gBAAgB;AAClF;AACA,SAAS,gBAAgB,IAAI;IACzB,KAAK,iBAAiB,GAClB,KAAK,uBAAuB,GACxB,KAAK,gBAAgB,GACjB;AAChB;AACA,SAAS,cAAc,IAAI;IACvB,KAAK,aAAa;AACtB;AACA,SAAS,kBAAkB,IAAI;IAC3B,KAAK,iBAAiB;AAC1B;AACA,SAAS,mBAAmB,IAAI;IAC5B,KAAK,aAAa,GAAG;AACzB;AACA,SAAS,oBAAoB,IAAI;IAC7B,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,OAAO;IACtC,IAAI,iBAAiB,cAAc,QAAQ,GAAG,qBAAqB,EAAE;QACjE,cAAc,MAAM,CAAC;IACzB;IACA,KAAK,cAAc;AACvB;AACA,SAAS,gBAAgB,IAAI;IACzB,KAAK,eAAe;IACpB,KAAK,WAAW,GAAG,KAAK,cAAc,GAAG,KAAK,MAAM,GAAG;IACvD,KAAK,iBAAiB,GAAG;AAC7B;AACA,SAAS,mBAAmB,IAAI;IAC5B,KAAK,kBAAkB;AAC3B;AACA,SAAS,eAAe,IAAI;IACxB,KAAK,cAAc;AACvB;AACA,SAAS,qBAAqB,IAAI;IAC9B,KAAK,oBAAoB;AAC7B;AACA,SAAS,oBAAoB,KAAK;IAC9B,MAAM,kBAAkB;AAC5B;AACA,SAAS,aAAa,MAAM,EAAE,KAAK,EAAE,CAAC;IAClC,OAAO,SAAS,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,SAAS,EAAE,GAAG;IACjD,OAAO,KAAK,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,KAAK,EAAE,GAAG;IACzC,OAAO,MAAM,GAAG,MAAM,MAAM;IAC5B,OAAO,WAAW,GAAG,MAAM,WAAW;AAC1C;AACA,SAAS,QAAQ,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IAChC,OAAO,GAAG,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,KAAK,GAAG,EAAE,GAAG,GAAG,EAAE;IACzC,OAAO,GAAG,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,KAAK,GAAG,EAAE,GAAG,GAAG,EAAE;AAC7C;AACA,SAAS,OAAO,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;IAC/B,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE;IAChC,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE;AACpC;AACA,SAAS,oBAAoB,IAAI;IAC7B,OAAQ,KAAK,eAAe,IAAI,KAAK,eAAe,CAAC,WAAW,KAAK;AACzE;AACA,MAAM,0BAA0B;IAC5B,UAAU;IACV,MAAM;QAAC;QAAK;QAAG;QAAK;KAAE;AAC1B;AACA,MAAM,oBAAoB,CAAC,SAAW,OAAO,cAAc,eACvD,UAAU,SAAS,IACnB,UAAU,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC;AAC/C;;;;CAIC,GACD,MAAM,aAAa,kBAAkB,mBAAmB,CAAC,kBAAkB,aACrE,KAAK,KAAK,GACV,sJAAA,CAAA,OAAI;AACV,SAAS,UAAU,IAAI;IACnB,6DAA6D;IAC7D,KAAK,GAAG,GAAG,WAAW,KAAK,GAAG;IAC9B,KAAK,GAAG,GAAG,WAAW,KAAK,GAAG;AAClC;AACA,SAAS,SAAS,GAAG;IACjB,UAAU,IAAI,CAAC;IACf,UAAU,IAAI,CAAC;AACnB;AACA,SAAS,0BAA0B,aAAa,EAAE,QAAQ,EAAE,MAAM;IAC9D,OAAQ,kBAAkB,cACrB,kBAAkB,qBACf,CAAC,CAAA,GAAA,0LAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAE,WAAW,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAE,SAAS;AAChE;AACA,SAAS,uBAAuB,IAAI;IAChC,OAAO,SAAS,KAAK,IAAI,IAAI,KAAK,MAAM,EAAE;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4982, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/node/DocumentProjectionNode.mjs"], "sourcesContent": ["import { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { createProjectionNode } from './create-projection-node.mjs';\n\nconst DocumentProjectionNode = createProjectionNode({\n    attachResizeListener: (ref, notify) => addDomEvent(ref, \"resize\", notify),\n    measureScroll: () => ({\n        x: document.documentElement.scrollLeft || document.body.scrollLeft,\n        y: document.documentElement.scrollTop || document.body.scrollTop,\n    }),\n    checkIsScrollRoot: () => true,\n});\n\nexport { DocumentProjectionNode };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,uBAAoB,AAAD,EAAE;IAChD,sBAAsB,CAAC,KAAK,SAAW,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE,KAAK,UAAU;IAClE,eAAe,IAAM,CAAC;YAClB,GAAG,SAAS,eAAe,CAAC,UAAU,IAAI,SAAS,IAAI,CAAC,UAAU;YAClE,GAAG,SAAS,eAAe,CAAC,SAAS,IAAI,SAAS,IAAI,CAAC,SAAS;QACpE,CAAC;IACD,mBAAmB,IAAM;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5004, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs"], "sourcesContent": ["import { createProjectionNode } from './create-projection-node.mjs';\nimport { DocumentProjectionNode } from './DocumentProjectionNode.mjs';\n\nconst rootProjectionNode = {\n    current: undefined,\n};\nconst HTMLProjectionNode = createProjectionNode({\n    measureScroll: (instance) => ({\n        x: instance.scrollLeft,\n        y: instance.scrollTop,\n    }),\n    defaultParent: () => {\n        if (!rootProjectionNode.current) {\n            const documentNode = new DocumentProjectionNode({});\n            documentNode.mount(window);\n            documentNode.setOptions({ layoutScroll: true });\n            rootProjectionNode.current = documentNode;\n        }\n        return rootProjectionNode.current;\n    },\n    resetTransform: (instance, value) => {\n        instance.style.transform = value !== undefined ? value : \"none\";\n    },\n    checkIsScrollRoot: (instance) => Boolean(window.getComputedStyle(instance).position === \"fixed\"),\n});\n\nexport { HTMLProjectionNode, rootProjectionNode };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,qBAAqB;IACvB,SAAS;AACb;AACA,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,uBAAoB,AAAD,EAAE;IAC5C,eAAe,CAAC,WAAa,CAAC;YAC1B,GAAG,SAAS,UAAU;YACtB,GAAG,SAAS,SAAS;QACzB,CAAC;IACD,eAAe;QACX,IAAI,CAAC,mBAAmB,OAAO,EAAE;YAC7B,MAAM,eAAe,IAAI,+LAAA,CAAA,yBAAsB,CAAC,CAAC;YACjD,aAAa,KAAK,CAAC;YACnB,aAAa,UAAU,CAAC;gBAAE,cAAc;YAAK;YAC7C,mBAAmB,OAAO,GAAG;QACjC;QACA,OAAO,mBAAmB,OAAO;IACrC;IACA,gBAAgB,CAAC,UAAU;QACvB,SAAS,KAAK,CAAC,SAAS,GAAG,UAAU,YAAY,QAAQ;IAC7D;IACA,mBAAmB,CAAC,WAAa,QAAQ,OAAO,gBAAgB,CAAC,UAAU,QAAQ,KAAK;AAC5F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5043, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/features/drag.mjs"], "sourcesContent": ["import { DragGesture } from '../../gestures/drag/index.mjs';\nimport { PanGesture } from '../../gestures/pan/index.mjs';\nimport { MeasureLayout } from './layout/MeasureLayout.mjs';\nimport { HTMLProjectionNode } from '../../projection/node/HTMLProjectionNode.mjs';\n\nconst drag = {\n    pan: {\n        Feature: PanGesture,\n    },\n    drag: {\n        Feature: DragGesture,\n        ProjectionNode: HTMLProjectionNode,\n        MeasureLayout,\n    },\n};\n\nexport { drag };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,OAAO;IACT,KAAK;QACD,SAAS,2KAAA,CAAA,aAAU;IACvB;IACA,MAAM;QACF,SAAS,4KAAA,CAAA,cAAW;QACpB,gBAAgB,2LAAA,CAAA,qBAAkB;QAClC,eAAA,gMAAA,CAAA,gBAAa;IACjB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5071, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/gestures/hover.mjs"], "sourcesContent": ["import { hover, frame } from 'motion-dom';\nimport { extractEventInfo } from '../events/event-info.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\n\nfunction handleHoverEvent(node, event, lifecycle) {\n    const { props } = node;\n    if (node.animationState && props.whileHover) {\n        node.animationState.setActive(\"whileHover\", lifecycle === \"Start\");\n    }\n    const eventName = (\"onHover\" + lifecycle);\n    const callback = props[eventName];\n    if (callback) {\n        frame.postRender(() => callback(event, extractEventInfo(event)));\n    }\n}\nclass HoverGesture extends Feature {\n    mount() {\n        const { current } = this.node;\n        if (!current)\n            return;\n        this.unmount = hover(current, (_element, startEvent) => {\n            handleHoverEvent(this.node, startEvent, \"Start\");\n            return (endEvent) => handleHoverEvent(this.node, endEvent, \"End\");\n        });\n    }\n    unmount() { }\n}\n\nexport { HoverGesture };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;AAEA,SAAS,iBAAiB,IAAI,EAAE,KAAK,EAAE,SAAS;IAC5C,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,IAAI,KAAK,cAAc,IAAI,MAAM,UAAU,EAAE;QACzC,KAAK,cAAc,CAAC,SAAS,CAAC,cAAc,cAAc;IAC9D;IACA,MAAM,YAAa,YAAY;IAC/B,MAAM,WAAW,KAAK,CAAC,UAAU;IACjC,IAAI,UAAU;QACV,kKAAA,CAAA,QAAK,CAAC,UAAU,CAAC,IAAM,SAAS,OAAO,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE;IAC5D;AACJ;AACA,MAAM,qBAAqB,gLAAA,CAAA,UAAO;IAC9B,QAAQ;QACJ,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI;QAC7B,IAAI,CAAC,SACD;QACJ,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,SAAS,CAAC,UAAU;YACrC,iBAAiB,IAAI,CAAC,IAAI,EAAE,YAAY;YACxC,OAAO,CAAC,WAAa,iBAAiB,IAAI,CAAC,IAAI,EAAE,UAAU;QAC/D;IACJ;IACA,UAAU,CAAE;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5110, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/gestures/focus.mjs"], "sourcesContent": ["import { pipe } from 'motion-utils';\nimport { addDomEvent } from '../events/add-dom-event.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\n\nclass FocusGesture extends Feature {\n    constructor() {\n        super(...arguments);\n        this.isActive = false;\n    }\n    onFocus() {\n        let isFocusVisible = false;\n        /**\n         * If this element doesn't match focus-visible then don't\n         * apply whileHover. But, if matches throws that focus-visible\n         * is not a valid selector then in that browser outline styles will be applied\n         * to the element by default and we want to match that behaviour with whileFocus.\n         */\n        try {\n            isFocusVisible = this.node.current.matches(\":focus-visible\");\n        }\n        catch (e) {\n            isFocusVisible = true;\n        }\n        if (!isFocusVisible || !this.node.animationState)\n            return;\n        this.node.animationState.setActive(\"whileFocus\", true);\n        this.isActive = true;\n    }\n    onBlur() {\n        if (!this.isActive || !this.node.animationState)\n            return;\n        this.node.animationState.setActive(\"whileFocus\", false);\n        this.isActive = false;\n    }\n    mount() {\n        this.unmount = pipe(addDomEvent(this.node.current, \"focus\", () => this.onFocus()), addDomEvent(this.node.current, \"blur\", () => this.onBlur()));\n    }\n    unmount() { }\n}\n\nexport { FocusGesture };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,qBAAqB,gLAAA,CAAA,UAAO;IAC9B,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,UAAU;QACN,IAAI,iBAAiB;QACrB;;;;;SAKC,GACD,IAAI;YACA,iBAAiB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QAC/C,EACA,OAAO,GAAG;YACN,iBAAiB;QACrB;QACA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAC5C;QACJ,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc;QACjD,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,SAAS;QACL,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAC3C;QACJ,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc;QACjD,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,QAAQ;QACJ,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,IAAM,IAAI,CAAC,OAAO,KAAK,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,IAAM,IAAI,CAAC,MAAM;IAC/I;IACA,UAAU,CAAE;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5157, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/gestures/press.mjs"], "sourcesContent": ["import { press, frame } from 'motion-dom';\nimport { extractEventInfo } from '../events/event-info.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\n\nfunction handlePressEvent(node, event, lifecycle) {\n    const { props } = node;\n    if (node.current instanceof HTMLButtonElement && node.current.disabled) {\n        return;\n    }\n    if (node.animationState && props.whileTap) {\n        node.animationState.setActive(\"whileTap\", lifecycle === \"Start\");\n    }\n    const eventName = (\"onTap\" + (lifecycle === \"End\" ? \"\" : lifecycle));\n    const callback = props[eventName];\n    if (callback) {\n        frame.postRender(() => callback(event, extractEventInfo(event)));\n    }\n}\nclass PressGesture extends Feature {\n    mount() {\n        const { current } = this.node;\n        if (!current)\n            return;\n        this.unmount = press(current, (_element, startEvent) => {\n            handlePressEvent(this.node, startEvent, \"Start\");\n            return (endEvent, { success }) => handlePressEvent(this.node, endEvent, success ? \"End\" : \"Cancel\");\n        }, { useGlobalTarget: this.node.props.globalTapTarget });\n    }\n    unmount() { }\n}\n\nexport { PressGesture };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;AAEA,SAAS,iBAAiB,IAAI,EAAE,KAAK,EAAE,SAAS;IAC5C,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,IAAI,KAAK,OAAO,YAAY,qBAAqB,KAAK,OAAO,CAAC,QAAQ,EAAE;QACpE;IACJ;IACA,IAAI,KAAK,cAAc,IAAI,MAAM,QAAQ,EAAE;QACvC,KAAK,cAAc,CAAC,SAAS,CAAC,YAAY,cAAc;IAC5D;IACA,MAAM,YAAa,UAAU,CAAC,cAAc,QAAQ,KAAK,SAAS;IAClE,MAAM,WAAW,KAAK,CAAC,UAAU;IACjC,IAAI,UAAU;QACV,kKAAA,CAAA,QAAK,CAAC,UAAU,CAAC,IAAM,SAAS,OAAO,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE;IAC5D;AACJ;AACA,MAAM,qBAAqB,gLAAA,CAAA,UAAO;IAC9B,QAAQ;QACJ,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI;QAC7B,IAAI,CAAC,SACD;QACJ,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,0KAAA,CAAA,QAAK,AAAD,EAAE,SAAS,CAAC,UAAU;YACrC,iBAAiB,IAAI,CAAC,IAAI,EAAE,YAAY;YACxC,OAAO,CAAC,UAAU,EAAE,OAAO,EAAE,GAAK,iBAAiB,IAAI,CAAC,IAAI,EAAE,UAAU,UAAU,QAAQ;QAC9F,GAAG;YAAE,iBAAiB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe;QAAC;IAC1D;IACA,UAAU,CAAE;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5201, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs"], "sourcesContent": ["/**\n * Map an IntersectionHandler callback to an element. We only ever make one handler for one\n * element, so even though these handlers might all be triggered by different\n * observers, we can keep them in the same map.\n */\nconst observerCallbacks = new WeakMap();\n/**\n * Multiple observers can be created for multiple element/document roots. Each with\n * different settings. So here we store dictionaries of observers to each root,\n * using serialised settings (threshold/margin) as lookup keys.\n */\nconst observers = new WeakMap();\nconst fireObserverCallback = (entry) => {\n    const callback = observerCallbacks.get(entry.target);\n    callback && callback(entry);\n};\nconst fireAllObserverCallbacks = (entries) => {\n    entries.forEach(fireObserverCallback);\n};\nfunction initIntersectionObserver({ root, ...options }) {\n    const lookupRoot = root || document;\n    /**\n     * If we don't have an observer lookup map for this root, create one.\n     */\n    if (!observers.has(lookupRoot)) {\n        observers.set(lookupRoot, {});\n    }\n    const rootObservers = observers.get(lookupRoot);\n    const key = JSON.stringify(options);\n    /**\n     * If we don't have an observer for this combination of root and settings,\n     * create one.\n     */\n    if (!rootObservers[key]) {\n        rootObservers[key] = new IntersectionObserver(fireAllObserverCallbacks, { root, ...options });\n    }\n    return rootObservers[key];\n}\nfunction observeIntersection(element, options, callback) {\n    const rootInteresectionObserver = initIntersectionObserver(options);\n    observerCallbacks.set(element, callback);\n    rootInteresectionObserver.observe(element);\n    return () => {\n        observerCallbacks.delete(element);\n        rootInteresectionObserver.unobserve(element);\n    };\n}\n\nexport { observeIntersection };\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACD,MAAM,oBAAoB,IAAI;AAC9B;;;;CAIC,GACD,MAAM,YAAY,IAAI;AACtB,MAAM,uBAAuB,CAAC;IAC1B,MAAM,WAAW,kBAAkB,GAAG,CAAC,MAAM,MAAM;IACnD,YAAY,SAAS;AACzB;AACA,MAAM,2BAA2B,CAAC;IAC9B,QAAQ,OAAO,CAAC;AACpB;AACA,SAAS,yBAAyB,EAAE,IAAI,EAAE,GAAG,SAAS;IAClD,MAAM,aAAa,QAAQ;IAC3B;;KAEC,GACD,IAAI,CAAC,UAAU,GAAG,CAAC,aAAa;QAC5B,UAAU,GAAG,CAAC,YAAY,CAAC;IAC/B;IACA,MAAM,gBAAgB,UAAU,GAAG,CAAC;IACpC,MAAM,MAAM,KAAK,SAAS,CAAC;IAC3B;;;KAGC,GACD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;QACrB,aAAa,CAAC,IAAI,GAAG,IAAI,qBAAqB,0BAA0B;YAAE;YAAM,GAAG,OAAO;QAAC;IAC/F;IACA,OAAO,aAAa,CAAC,IAAI;AAC7B;AACA,SAAS,oBAAoB,OAAO,EAAE,OAAO,EAAE,QAAQ;IACnD,MAAM,4BAA4B,yBAAyB;IAC3D,kBAAkB,GAAG,CAAC,SAAS;IAC/B,0BAA0B,OAAO,CAAC;IAClC,OAAO;QACH,kBAAkB,MAAM,CAAC;QACzB,0BAA0B,SAAS,CAAC;IACxC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5257, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs"], "sourcesContent": ["import { Feature } from '../Feature.mjs';\nimport { observeIntersection } from './observers.mjs';\n\nconst thresholdNames = {\n    some: 0,\n    all: 1,\n};\nclass InViewFeature extends Feature {\n    constructor() {\n        super(...arguments);\n        this.hasEnteredView = false;\n        this.isInView = false;\n    }\n    startObserver() {\n        this.unmount();\n        const { viewport = {} } = this.node.getProps();\n        const { root, margin: rootMargin, amount = \"some\", once } = viewport;\n        const options = {\n            root: root ? root.current : undefined,\n            rootMargin,\n            threshold: typeof amount === \"number\" ? amount : thresholdNames[amount],\n        };\n        const onIntersectionUpdate = (entry) => {\n            const { isIntersecting } = entry;\n            /**\n             * If there's been no change in the viewport state, early return.\n             */\n            if (this.isInView === isIntersecting)\n                return;\n            this.isInView = isIntersecting;\n            /**\n             * Handle hasEnteredView. If this is only meant to run once, and\n             * element isn't visible, early return. Otherwise set hasEnteredView to true.\n             */\n            if (once && !isIntersecting && this.hasEnteredView) {\n                return;\n            }\n            else if (isIntersecting) {\n                this.hasEnteredView = true;\n            }\n            if (this.node.animationState) {\n                this.node.animationState.setActive(\"whileInView\", isIntersecting);\n            }\n            /**\n             * Use the latest committed props rather than the ones in scope\n             * when this observer is created\n             */\n            const { onViewportEnter, onViewportLeave } = this.node.getProps();\n            const callback = isIntersecting ? onViewportEnter : onViewportLeave;\n            callback && callback(entry);\n        };\n        return observeIntersection(this.node.current, options, onIntersectionUpdate);\n    }\n    mount() {\n        this.startObserver();\n    }\n    update() {\n        if (typeof IntersectionObserver === \"undefined\")\n            return;\n        const { props, prevProps } = this.node;\n        const hasOptionsChanged = [\"amount\", \"margin\", \"root\"].some(hasViewportOptionChanged(props, prevProps));\n        if (hasOptionsChanged) {\n            this.startObserver();\n        }\n    }\n    unmount() { }\n}\nfunction hasViewportOptionChanged({ viewport = {} }, { viewport: prevViewport = {} } = {}) {\n    return (name) => viewport[name] !== prevViewport[name];\n}\n\nexport { InViewFeature };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,iBAAiB;IACnB,MAAM;IACN,KAAK;AACT;AACA,MAAM,sBAAsB,gLAAA,CAAA,UAAO;IAC/B,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,gBAAgB;QACZ,IAAI,CAAC,OAAO;QACZ,MAAM,EAAE,WAAW,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;QAC5C,MAAM,EAAE,IAAI,EAAE,QAAQ,UAAU,EAAE,SAAS,MAAM,EAAE,IAAI,EAAE,GAAG;QAC5D,MAAM,UAAU;YACZ,MAAM,OAAO,KAAK,OAAO,GAAG;YAC5B;YACA,WAAW,OAAO,WAAW,WAAW,SAAS,cAAc,CAAC,OAAO;QAC3E;QACA,MAAM,uBAAuB,CAAC;YAC1B,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B;;aAEC,GACD,IAAI,IAAI,CAAC,QAAQ,KAAK,gBAClB;YACJ,IAAI,CAAC,QAAQ,GAAG;YAChB;;;aAGC,GACD,IAAI,QAAQ,CAAC,kBAAkB,IAAI,CAAC,cAAc,EAAE;gBAChD;YACJ,OACK,IAAI,gBAAgB;gBACrB,IAAI,CAAC,cAAc,GAAG;YAC1B;YACA,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBAC1B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,eAAe;YACtD;YACA;;;aAGC,GACD,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;YAC/D,MAAM,WAAW,iBAAiB,kBAAkB;YACpD,YAAY,SAAS;QACzB;QACA,OAAO,CAAA,GAAA,8LAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS;IAC3D;IACA,QAAQ;QACJ,IAAI,CAAC,aAAa;IACtB;IACA,SAAS;QACL,IAAI,OAAO,yBAAyB,aAChC;QACJ,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,IAAI;QACtC,MAAM,oBAAoB;YAAC;YAAU;YAAU;SAAO,CAAC,IAAI,CAAC,yBAAyB,OAAO;QAC5F,IAAI,mBAAmB;YACnB,IAAI,CAAC,aAAa;QACtB;IACJ;IACA,UAAU,CAAE;AAChB;AACA,SAAS,yBAAyB,EAAE,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,UAAU,eAAe,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACrF,OAAO,CAAC,OAAS,QAAQ,CAAC,KAAK,KAAK,YAAY,CAAC,KAAK;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5336, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/features/gestures.mjs"], "sourcesContent": ["import { HoverGesture } from '../../gestures/hover.mjs';\nimport { FocusGesture } from '../../gestures/focus.mjs';\nimport { PressGesture } from '../../gestures/press.mjs';\nimport { InViewFeature } from './viewport/index.mjs';\n\nconst gestureAnimations = {\n    inView: {\n        Feature: InViewFeature,\n    },\n    tap: {\n        Feature: PressGesture,\n    },\n    focus: {\n        Feature: FocusGesture,\n    },\n    hover: {\n        Feature: HoverGesture,\n    },\n};\n\nexport { gestureAnimations };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,oBAAoB;IACtB,QAAQ;QACJ,SAAS,0LAAA,CAAA,gBAAa;IAC1B;IACA,KAAK;QACD,SAAS,oKAAA,CAAA,eAAY;IACzB;IACA,OAAO;QACH,SAAS,oKAAA,CAAA,eAAY;IACzB;IACA,OAAO;QACH,SAAS,oKAAA,CAAA,eAAY;IACzB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5368, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/features/layout.mjs"], "sourcesContent": ["import { HTMLProjectionNode } from '../../projection/node/HTMLProjectionNode.mjs';\nimport { MeasureLayout } from './layout/MeasureLayout.mjs';\n\nconst layout = {\n    layout: {\n        ProjectionNode: HTMLProjectionNode,\n        MeasureLayout,\n    },\n};\n\nexport { layout };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,SAAS;IACX,QAAQ;QACJ,gBAAgB,2LAAA,CAAA,qBAAkB;QAClC,eAAA,gMAAA,CAAA,gBAAa;IACjB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5388, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/context/LazyContext.mjs"], "sourcesContent": ["\"use client\";\nimport { createContext } from 'react';\n\nconst LazyContext = createContext({ strict: false });\n\nexport { LazyContext };\n"], "names": [], "mappings": ";;;AACA;AADA;;AAGA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;IAAE,QAAQ;AAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5404, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/context/MotionContext/index.mjs"], "sourcesContent": ["\"use client\";\nimport { createContext } from 'react';\n\nconst MotionContext = /* @__PURE__ */ createContext({});\n\nexport { MotionContext };\n"], "names": [], "mappings": ";;;AACA;AADA;;AAGA,MAAM,gBAAgB,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5418, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs"], "sourcesContent": ["import { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nimport { variantProps } from './variant-props.mjs';\n\nfunction isControllingVariants(props) {\n    return (isAnimationControls(props.animate) ||\n        variantProps.some((name) => isVariantLabel(props[name])));\n}\nfunction isVariantNode(props) {\n    return Boolean(isControllingVariants(props) || props.variants);\n}\n\nexport { isControllingVariants, isVariantNode };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,SAAS,sBAAsB,KAAK;IAChC,OAAQ,CAAA,GAAA,oMAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,OAAO,KACrC,sLAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAC,OAAS,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,CAAC,KAAK;AAC9D;AACA,SAAS,cAAc,KAAK;IACxB,OAAO,QAAQ,sBAAsB,UAAU,MAAM,QAAQ;AACjE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5441, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/context/MotionContext/utils.mjs"], "sourcesContent": ["import { isControllingVariants } from '../../render/utils/is-controlling-variants.mjs';\nimport { isVariantLabel } from '../../render/utils/is-variant-label.mjs';\n\nfunction getCurrentTreeVariants(props, context) {\n    if (isControllingVariants(props)) {\n        const { initial, animate } = props;\n        return {\n            initial: initial === false || isVariantLabel(initial)\n                ? initial\n                : undefined,\n            animate: isVariantLabel(animate) ? animate : undefined,\n        };\n    }\n    return props.inherit !== false ? context : {};\n}\n\nexport { getCurrentTreeVariants };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,uBAAuB,KAAK,EAAE,OAAO;IAC1C,IAAI,CAAA,GAAA,mMAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ;QAC9B,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAC7B,OAAO;YACH,SAAS,YAAY,SAAS,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE,WACvC,UACA;YACN,SAAS,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,UAAU;QACjD;IACJ;IACA,OAAO,MAAM,OAAO,KAAK,QAAQ,UAAU,CAAC;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5465, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/context/MotionContext/create.mjs"], "sourcesContent": ["import { useContext, useMemo } from 'react';\nimport { MotionContext } from './index.mjs';\nimport { getCurrentTreeVariants } from './utils.mjs';\n\nfunction useCreateMotionContext(props) {\n    const { initial, animate } = getCurrentTreeVariants(props, useContext(MotionContext));\n    return useMemo(() => ({ initial, animate }), [variantLabelsAsDependency(initial), variantLabelsAsDependency(animate)]);\n}\nfunction variantLabelsAsDependency(prop) {\n    return Array.isArray(prop) ? prop.join(\" \") : prop;\n}\n\nexport { useCreateMotionContext };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,uBAAuB,KAAK;IACjC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oLAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,oLAAA,CAAA,gBAAa;IACnF,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAAE;YAAS;QAAQ,CAAC,GAAG;QAAC,0BAA0B;QAAU,0BAA0B;KAAS;AACzH;AACA,SAAS,0BAA0B,IAAI;IACnC,OAAO,MAAM,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,OAAO;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5494, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/utils/is-browser.mjs"], "sourcesContent": ["const isBrowser = typeof window !== \"undefined\";\n\nexport { isBrowser };\n"], "names": [], "mappings": ";;;AAAA,MAAM,YAAY,OAAO,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5505, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/features/definitions.mjs"], "sourcesContent": ["const featureProps = {\n    animation: [\n        \"animate\",\n        \"variants\",\n        \"whileHover\",\n        \"whileTap\",\n        \"exit\",\n        \"whileInView\",\n        \"whileFocus\",\n        \"whileDrag\",\n    ],\n    exit: [\"exit\"],\n    drag: [\"drag\", \"dragControls\"],\n    focus: [\"whileFocus\"],\n    hover: [\"whileHover\", \"onHoverStart\", \"onHoverEnd\"],\n    tap: [\"whileTap\", \"onTap\", \"onTapStart\", \"onTapCancel\"],\n    pan: [\"onPan\", \"onPanStart\", \"onPanSessionStart\", \"onPanEnd\"],\n    inView: [\"whileInView\", \"onViewportEnter\", \"onViewportLeave\"],\n    layout: [\"layout\", \"layoutId\"],\n};\nconst featureDefinitions = {};\nfor (const key in featureProps) {\n    featureDefinitions[key] = {\n        isEnabled: (props) => featureProps[key].some((name) => !!props[name]),\n    };\n}\n\nexport { featureDefinitions };\n"], "names": [], "mappings": ";;;AAAA,MAAM,eAAe;IACjB,WAAW;QACP;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,MAAM;QAAC;KAAO;IACd,MAAM;QAAC;QAAQ;KAAe;IAC9B,OAAO;QAAC;KAAa;IACrB,OAAO;QAAC;QAAc;QAAgB;KAAa;IACnD,KAAK;QAAC;QAAY;QAAS;QAAc;KAAc;IACvD,KAAK;QAAC;QAAS;QAAc;QAAqB;KAAW;IAC7D,QAAQ;QAAC;QAAe;QAAmB;KAAkB;IAC7D,QAAQ;QAAC;QAAU;KAAW;AAClC;AACA,MAAM,qBAAqB,CAAC;AAC5B,IAAK,MAAM,OAAO,aAAc;IAC5B,kBAAkB,CAAC,IAAI,GAAG;QACtB,WAAW,CAAC,QAAU,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAS,CAAC,CAAC,KAAK,CAAC,KAAK;IACxE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5569, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/features/load-features.mjs"], "sourcesContent": ["import { featureDefinitions } from './definitions.mjs';\n\nfunction loadFeatures(features) {\n    for (const key in features) {\n        featureDefinitions[key] = {\n            ...featureDefinitions[key],\n            ...features[key],\n        };\n    }\n}\n\nexport { loadFeatures };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,aAAa,QAAQ;IAC1B,IAAK,MAAM,OAAO,SAAU;QACxB,oLAAA,CAAA,qBAAkB,CAAC,IAAI,GAAG;YACtB,GAAG,oLAAA,CAAA,qBAAkB,CAAC,IAAI;YAC1B,GAAG,QAAQ,CAAC,IAAI;QACpB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5589, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs"], "sourcesContent": ["const motionComponentSymbol = Symbol.for(\"motionComponentSymbol\");\n\nexport { motionComponentSymbol };\n"], "names": [], "mappings": ";;;AAAA,MAAM,wBAAwB,OAAO,GAAG,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5600, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs"], "sourcesContent": ["import { useCallback } from 'react';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\n\n/**\n * Creates a ref function that, when called, hydrates the provided\n * external ref and VisualElement.\n */\nfunction useMotionRef(visualState, visualElement, externalRef) {\n    return useCallback((instance) => {\n        if (instance) {\n            visualState.onMount && visualState.onMount(instance);\n        }\n        if (visualElement) {\n            if (instance) {\n                visualElement.mount(instance);\n            }\n            else {\n                visualElement.unmount();\n            }\n        }\n        if (externalRef) {\n            if (typeof externalRef === \"function\") {\n                externalRef(instance);\n            }\n            else if (isRefObject(externalRef)) {\n                externalRef.current = instance;\n            }\n        }\n    }, \n    /**\n     * Only pass a new ref callback to <PERSON>act if we've received a visual element\n     * factory. Otherwise we'll be mounting/remounting every time externalRef\n     * or other dependencies change.\n     */\n    [visualElement]);\n}\n\nexport { useMotionRef };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA;;;CAGC,GACD,SAAS,aAAa,WAAW,EAAE,aAAa,EAAE,WAAW;IACzD,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChB,IAAI,UAAU;YACV,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC;QAC/C;QACA,IAAI,eAAe;YACf,IAAI,UAAU;gBACV,cAAc,KAAK,CAAC;YACxB,OACK;gBACD,cAAc,OAAO;YACzB;QACJ;QACA,IAAI,aAAa;YACb,IAAI,OAAO,gBAAgB,YAAY;gBACnC,YAAY;YAChB,OACK,IAAI,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD,EAAE,cAAc;gBAC/B,YAAY,OAAO,GAAG;YAC1B;QACJ;IACJ,GACA;;;;KAIC,GACD;QAAC;KAAc;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5644, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs"], "sourcesContent": ["import { useLayoutEffect, useEffect } from 'react';\nimport { isBrowser } from './is-browser.mjs';\n\nconst useIsomorphicLayoutEffect = isBrowser ? useLayoutEffect : useEffect;\n\nexport { useIsomorphicLayoutEffect };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,4BAA4B,yKAAA,CAAA,YAAS,GAAG,qMAAA,CAAA,kBAAe,GAAG,qMAAA,CAAA,YAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5659, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs"], "sourcesContent": ["import { microtask } from 'motion-dom';\nimport { useContext, useRef, useInsertionEffect, useEffect } from 'react';\nimport { optimizedAppearDataAttribute } from '../../animation/optimized-appear/data-id.mjs';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { SwitchLayoutGroupContext } from '../../context/SwitchLayoutGroupContext.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\n\nfunction useVisualElement(Component, visualState, props, createVisualElement, ProjectionNodeConstructor) {\n    const { visualElement: parent } = useContext(MotionContext);\n    const lazyContext = useContext(LazyContext);\n    const presenceContext = useContext(PresenceContext);\n    const reducedMotionConfig = useContext(MotionConfigContext).reducedMotion;\n    const visualElementRef = useRef(null);\n    /**\n     * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n     */\n    createVisualElement = createVisualElement || lazyContext.renderer;\n    if (!visualElementRef.current && createVisualElement) {\n        visualElementRef.current = createVisualElement(Component, {\n            visualState,\n            parent,\n            props,\n            presenceContext,\n            blockInitialAnimation: presenceContext\n                ? presenceContext.initial === false\n                : false,\n            reducedMotionConfig,\n        });\n    }\n    const visualElement = visualElementRef.current;\n    /**\n     * Load Motion gesture and animation features. These are rendered as renderless\n     * components so each feature can optionally make use of React lifecycle methods.\n     */\n    const initialLayoutGroupConfig = useContext(SwitchLayoutGroupContext);\n    if (visualElement &&\n        !visualElement.projection &&\n        ProjectionNodeConstructor &&\n        (visualElement.type === \"html\" || visualElement.type === \"svg\")) {\n        createProjectionNode(visualElementRef.current, props, ProjectionNodeConstructor, initialLayoutGroupConfig);\n    }\n    const isMounted = useRef(false);\n    useInsertionEffect(() => {\n        /**\n         * Check the component has already mounted before calling\n         * `update` unnecessarily. This ensures we skip the initial update.\n         */\n        if (visualElement && isMounted.current) {\n            visualElement.update(props, presenceContext);\n        }\n    });\n    /**\n     * Cache this value as we want to know whether HandoffAppearAnimations\n     * was present on initial render - it will be deleted after this.\n     */\n    const optimisedAppearId = props[optimizedAppearDataAttribute];\n    const wantsHandoff = useRef(Boolean(optimisedAppearId) &&\n        !window.MotionHandoffIsComplete?.(optimisedAppearId) &&\n        window.MotionHasOptimisedAnimation?.(optimisedAppearId));\n    useIsomorphicLayoutEffect(() => {\n        if (!visualElement)\n            return;\n        isMounted.current = true;\n        window.MotionIsMounted = true;\n        visualElement.updateFeatures();\n        microtask.render(visualElement.render);\n        /**\n         * Ideally this function would always run in a useEffect.\n         *\n         * However, if we have optimised appear animations to handoff from,\n         * it needs to happen synchronously to ensure there's no flash of\n         * incorrect styles in the event of a hydration error.\n         *\n         * So if we detect a situtation where optimised appear animations\n         * are running, we use useLayoutEffect to trigger animations.\n         */\n        if (wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n    });\n    useEffect(() => {\n        if (!visualElement)\n            return;\n        if (!wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n        if (wantsHandoff.current) {\n            // This ensures all future calls to animateChanges() in this component will run in useEffect\n            queueMicrotask(() => {\n                window.MotionHandoffMarkAsComplete?.(optimisedAppearId);\n            });\n            wantsHandoff.current = false;\n        }\n    });\n    return visualElement;\n}\nfunction createProjectionNode(visualElement, props, ProjectionNodeConstructor, initialPromotionConfig) {\n    const { layoutId, layout, drag, dragConstraints, layoutScroll, layoutRoot, layoutCrossfade, } = props;\n    visualElement.projection = new ProjectionNodeConstructor(visualElement.latestValues, props[\"data-framer-portal-id\"]\n        ? undefined\n        : getClosestProjectingNode(visualElement.parent));\n    visualElement.projection.setOptions({\n        layoutId,\n        layout,\n        alwaysMeasureLayout: Boolean(drag) || (dragConstraints && isRefObject(dragConstraints)),\n        visualElement,\n        /**\n         * TODO: Update options in an effect. This could be tricky as it'll be too late\n         * to update by the time layout animations run.\n         * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n         * ensuring it gets called if there's no potential layout animations.\n         *\n         */\n        animationType: typeof layout === \"string\" ? layout : \"both\",\n        initialPromotionConfig,\n        crossfade: layoutCrossfade,\n        layoutScroll,\n        layoutRoot,\n    });\n}\nfunction getClosestProjectingNode(visualElement) {\n    if (!visualElement)\n        return undefined;\n    return visualElement.options.allowProjection !== false\n        ? visualElement.projection\n        : getClosestProjectingNode(visualElement.parent);\n}\n\nexport { useVisualElement };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,SAAS,iBAAiB,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,mBAAmB,EAAE,yBAAyB;IACnG,MAAM,EAAE,eAAe,MAAM,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,oLAAA,CAAA,gBAAa;IAC1D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,yKAAA,CAAA,cAAW;IAC1C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,6KAAA,CAAA,kBAAe;IAClD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,iLAAA,CAAA,sBAAmB,EAAE,aAAa;IACzE,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAChC;;KAEC,GACD,sBAAsB,uBAAuB,YAAY,QAAQ;IACjE,IAAI,CAAC,iBAAiB,OAAO,IAAI,qBAAqB;QAClD,iBAAiB,OAAO,GAAG,oBAAoB,WAAW;YACtD;YACA;YACA;YACA;YACA,uBAAuB,kBACjB,gBAAgB,OAAO,KAAK,QAC5B;YACN;QACJ;IACJ;IACA,MAAM,gBAAgB,iBAAiB,OAAO;IAC9C;;;KAGC,GACD,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,sLAAA,CAAA,2BAAwB;IACpE,IAAI,iBACA,CAAC,cAAc,UAAU,IACzB,6BACA,CAAC,cAAc,IAAI,KAAK,UAAU,cAAc,IAAI,KAAK,KAAK,GAAG;QACjE,qBAAqB,iBAAiB,OAAO,EAAE,OAAO,2BAA2B;IACrF;IACA,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,CAAA,GAAA,qMAAA,CAAA,qBAAkB,AAAD,EAAE;QACf;;;SAGC,GACD,IAAI,iBAAiB,UAAU,OAAO,EAAE;YACpC,cAAc,MAAM,CAAC,OAAO;QAChC;IACJ;IACA;;;KAGC,GACD,MAAM,oBAAoB,KAAK,CAAC,iMAAA,CAAA,+BAA4B,CAAC;IAC7D,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,sBAChC,CAAC,OAAO,uBAAuB,GAAG,sBAClC,OAAO,2BAA2B,GAAG;IACzC,CAAA,GAAA,uLAAA,CAAA,4BAAyB,AAAD,EAAE;QACtB,IAAI,CAAC,eACD;QACJ,UAAU,OAAO,GAAG;QACpB,OAAO,eAAe,GAAG;QACzB,cAAc,cAAc;QAC5B,sKAAA,CAAA,YAAS,CAAC,MAAM,CAAC,cAAc,MAAM;QACrC;;;;;;;;;SASC,GACD,IAAI,aAAa,OAAO,IAAI,cAAc,cAAc,EAAE;YACtD,cAAc,cAAc,CAAC,cAAc;QAC/C;IACJ;IACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,eACD;QACJ,IAAI,CAAC,aAAa,OAAO,IAAI,cAAc,cAAc,EAAE;YACvD,cAAc,cAAc,CAAC,cAAc;QAC/C;QACA,IAAI,aAAa,OAAO,EAAE;YACtB,4FAA4F;YAC5F,eAAe;gBACX,OAAO,2BAA2B,GAAG;YACzC;YACA,aAAa,OAAO,GAAG;QAC3B;IACJ;IACA,OAAO;AACX;AACA,SAAS,qBAAqB,aAAa,EAAE,KAAK,EAAE,yBAAyB,EAAE,sBAAsB;IACjG,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,YAAY,EAAE,UAAU,EAAE,eAAe,EAAG,GAAG;IAChG,cAAc,UAAU,GAAG,IAAI,0BAA0B,cAAc,YAAY,EAAE,KAAK,CAAC,wBAAwB,GAC7G,YACA,yBAAyB,cAAc,MAAM;IACnD,cAAc,UAAU,CAAC,UAAU,CAAC;QAChC;QACA;QACA,qBAAqB,QAAQ,SAAU,mBAAmB,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD,EAAE;QACtE;QACA;;;;;;SAMC,GACD,eAAe,OAAO,WAAW,WAAW,SAAS;QACrD;QACA,WAAW;QACX;QACA;IACJ;AACJ;AACA,SAAS,yBAAyB,aAAa;IAC3C,IAAI,CAAC,eACD,OAAO;IACX,OAAO,cAAc,OAAO,CAAC,eAAe,KAAK,QAC3C,cAAc,UAAU,GACxB,yBAAyB,cAAc,MAAM;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5789, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/index.mjs"], "sourcesContent": ["\"use client\";\nimport { jsxs, jsx } from 'react/jsx-runtime';\nimport { warning, invariant } from 'motion-utils';\nimport { forwardRef, useContext } from 'react';\nimport { LayoutGroupContext } from '../context/LayoutGroupContext.mjs';\nimport { LazyContext } from '../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../context/MotionContext/index.mjs';\nimport { useCreateMotionContext } from '../context/MotionContext/create.mjs';\nimport { isBrowser } from '../utils/is-browser.mjs';\nimport { featureDefinitions } from './features/definitions.mjs';\nimport { loadFeatures } from './features/load-features.mjs';\nimport { motionComponentSymbol } from './utils/symbol.mjs';\nimport { useMotionRef } from './utils/use-motion-ref.mjs';\nimport { useVisualElement } from './utils/use-visual-element.mjs';\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */\nfunction createRendererMotionComponent({ preloadedFeatures, createVisualElement, useRender, useVisualState, Component, }) {\n    preloadedFeatures && loadFeatures(preloadedFeatures);\n    function MotionComponent(props, externalRef) {\n        /**\n         * If we need to measure the element we load this functionality in a\n         * separate class component in order to gain access to getSnapshotBeforeUpdate.\n         */\n        let MeasureLayout;\n        const configAndProps = {\n            ...useContext(MotionConfigContext),\n            ...props,\n            layoutId: useLayoutId(props),\n        };\n        const { isStatic } = configAndProps;\n        const context = useCreateMotionContext(props);\n        const visualState = useVisualState(props, isStatic);\n        if (!isStatic && isBrowser) {\n            useStrictMode(configAndProps, preloadedFeatures);\n            const layoutProjection = getProjectionFunctionality(configAndProps);\n            MeasureLayout = layoutProjection.MeasureLayout;\n            /**\n             * Create a VisualElement for this component. A VisualElement provides a common\n             * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n             * providing a way of rendering to these APIs outside of the React render loop\n             * for more performant animations and interactions\n             */\n            context.visualElement = useVisualElement(Component, visualState, configAndProps, createVisualElement, layoutProjection.ProjectionNode);\n        }\n        /**\n         * The mount order and hierarchy is specific to ensure our element ref\n         * is hydrated by the time features fire their effects.\n         */\n        return (jsxs(MotionContext.Provider, { value: context, children: [MeasureLayout && context.visualElement ? (jsx(MeasureLayout, { visualElement: context.visualElement, ...configAndProps })) : null, useRender(Component, props, useMotionRef(visualState, context.visualElement, externalRef), visualState, isStatic, context.visualElement)] }));\n    }\n    MotionComponent.displayName = `motion.${typeof Component === \"string\"\n        ? Component\n        : `create(${Component.displayName ?? Component.name ?? \"\"})`}`;\n    const ForwardRefMotionComponent = forwardRef(MotionComponent);\n    ForwardRefMotionComponent[motionComponentSymbol] = Component;\n    return ForwardRefMotionComponent;\n}\nfunction useLayoutId({ layoutId }) {\n    const layoutGroupId = useContext(LayoutGroupContext).id;\n    return layoutGroupId && layoutId !== undefined\n        ? layoutGroupId + \"-\" + layoutId\n        : layoutId;\n}\nfunction useStrictMode(configAndProps, preloadedFeatures) {\n    const isStrict = useContext(LazyContext).strict;\n    /**\n     * If we're in development mode, check to make sure we're not rendering a motion component\n     * as a child of LazyMotion, as this will break the file-size benefits of using it.\n     */\n    if (process.env.NODE_ENV !== \"production\" &&\n        preloadedFeatures &&\n        isStrict) {\n        const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n        configAndProps.ignoreStrict\n            ? warning(false, strictMessage, \"lazy-strict-mode\")\n            : invariant(false, strictMessage, \"lazy-strict-mode\");\n    }\n}\nfunction getProjectionFunctionality(props) {\n    const { drag, layout } = featureDefinitions;\n    if (!drag && !layout)\n        return {};\n    const combined = { ...drag, ...layout };\n    return {\n        MeasureLayout: drag?.isEnabled(props) || layout?.isEnabled(props)\n            ? combined.MeasureLayout\n            : undefined,\n        ProjectionNode: combined.ProjectionNode,\n    };\n}\n\nexport { createRendererMotionComponent };\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;;AAgBA;;;;;;;;CAQC,GACD,SAAS,8BAA8B,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAG;IACpH,qBAAqB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE;IAClC,SAAS,gBAAgB,KAAK,EAAE,WAAW;QACvC;;;SAGC,GACD,IAAI;QACJ,MAAM,iBAAiB;YACnB,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,iLAAA,CAAA,sBAAmB,CAAC;YAClC,GAAG,KAAK;YACR,UAAU,YAAY;QAC1B;QACA,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,MAAM,UAAU,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE;QACvC,MAAM,cAAc,eAAe,OAAO;QAC1C,IAAI,CAAC,YAAY,yKAAA,CAAA,YAAS,EAAE;YACxB,cAAc,gBAAgB;YAC9B,MAAM,mBAAmB,2BAA2B;YACpD,gBAAgB,iBAAiB,aAAa;YAC9C;;;;;aAKC,GACD,QAAQ,aAAa,GAAG,CAAA,GAAA,8LAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,aAAa,gBAAgB,qBAAqB,iBAAiB,cAAc;QACzI;QACA;;;SAGC,GACD,OAAQ,CAAA,GAAA,uNAAA,CAAA,OAAI,AAAD,EAAE,oLAAA,CAAA,gBAAa,CAAC,QAAQ,EAAE;YAAE,OAAO;YAAS,UAAU;gBAAC,iBAAiB,QAAQ,aAAa,GAAI,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,eAAe;oBAAE,eAAe,QAAQ,aAAa;oBAAE,GAAG,cAAc;gBAAC,KAAM;gBAAM,UAAU,WAAW,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa,QAAQ,aAAa,EAAE,cAAc,aAAa,UAAU,QAAQ,aAAa;aAAE;QAAC;IACnV;IACA,gBAAgB,WAAW,GAAG,CAAC,OAAO,EAAE,OAAO,cAAc,WACvD,YACA,CAAC,OAAO,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE;IAClE,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC7C,yBAAyB,CAAC,4KAAA,CAAA,wBAAqB,CAAC,GAAG;IACnD,OAAO;AACX;AACA,SAAS,YAAY,EAAE,QAAQ,EAAE;IAC7B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gLAAA,CAAA,qBAAkB,EAAE,EAAE;IACvD,OAAO,iBAAiB,aAAa,YAC/B,gBAAgB,MAAM,WACtB;AACV;AACA,SAAS,cAAc,cAAc,EAAE,iBAAiB;IACpD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,yKAAA,CAAA,cAAW,EAAE,MAAM;IAC/C;;;KAGC,GACD,IAAI,oDAAyB,gBACzB,qBACA,UAAU;QACV,MAAM,gBAAgB;QACtB,eAAe,YAAY,GACrB,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,OAAO,eAAe,sBAC9B,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,OAAO,eAAe;IAC1C;AACJ;AACA,SAAS,2BAA2B,KAAK;IACrC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,oLAAA,CAAA,qBAAkB;IAC3C,IAAI,CAAC,QAAQ,CAAC,QACV,OAAO,CAAC;IACZ,MAAM,WAAW;QAAE,GAAG,IAAI;QAAE,GAAG,MAAM;IAAC;IACtC,OAAO;QACH,eAAe,MAAM,UAAU,UAAU,QAAQ,UAAU,SACrD,SAAS,aAAa,GACtB;QACN,gBAAgB,SAAS,cAAc;IAC3C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5907, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs"], "sourcesContent": ["import { transformProps } from 'motion-dom';\nimport { scaleCorrectors } from '../../projection/styles/scale-correction.mjs';\n\nfunction isForcedMotionValue(key, { layout, layoutId }) {\n    return (transformProps.has(key) ||\n        key.startsWith(\"origin\") ||\n        ((layout || layoutId !== undefined) &&\n            (!!scaleCorrectors[key] || key === \"opacity\")));\n}\n\nexport { isForcedMotionValue };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,oBAAoB,GAAG,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;IAClD,OAAQ,oLAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,QACvB,IAAI,UAAU,CAAC,aACd,CAAC,UAAU,aAAa,SAAS,KAC9B,CAAC,CAAC,CAAC,8LAAA,CAAA,kBAAe,CAAC,IAAI,IAAI,QAAQ,SAAS;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5924, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs"], "sourcesContent": ["import { transformPropOrder, getValueAsType, numberValueTypes } from 'motion-dom';\n\nconst translateAlias = {\n    x: \"translateX\",\n    y: \"translateY\",\n    z: \"translateZ\",\n    transformPerspective: \"perspective\",\n};\nconst numTransforms = transformPropOrder.length;\n/**\n * Build a CSS transform style from individual x/y/scale etc properties.\n *\n * This outputs with a default order of transforms/scales/rotations, this can be customised by\n * providing a transformTemplate function.\n */\nfunction buildTransform(latestValues, transform, transformTemplate) {\n    // The transform string we're going to build into.\n    let transformString = \"\";\n    let transformIsDefault = true;\n    /**\n     * Loop over all possible transforms in order, adding the ones that\n     * are present to the transform string.\n     */\n    for (let i = 0; i < numTransforms; i++) {\n        const key = transformPropOrder[i];\n        const value = latestValues[key];\n        if (value === undefined)\n            continue;\n        let valueIsDefault = true;\n        if (typeof value === \"number\") {\n            valueIsDefault = value === (key.startsWith(\"scale\") ? 1 : 0);\n        }\n        else {\n            valueIsDefault = parseFloat(value) === 0;\n        }\n        if (!valueIsDefault || transformTemplate) {\n            const valueAsType = getValueAsType(value, numberValueTypes[key]);\n            if (!valueIsDefault) {\n                transformIsDefault = false;\n                const transformName = translateAlias[key] || key;\n                transformString += `${transformName}(${valueAsType}) `;\n            }\n            if (transformTemplate) {\n                transform[key] = valueAsType;\n            }\n        }\n    }\n    transformString = transformString.trim();\n    // If we have a custom `transform` template, pass our transform values and\n    // generated transformString to that before returning\n    if (transformTemplate) {\n        transformString = transformTemplate(transform, transformIsDefault ? \"\" : transformString);\n    }\n    else if (transformIsDefault) {\n        transformString = \"none\";\n    }\n    return transformString;\n}\n\nexport { buildTransform };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;;AAEA,MAAM,iBAAiB;IACnB,GAAG;IACH,GAAG;IACH,GAAG;IACH,sBAAsB;AAC1B;AACA,MAAM,gBAAgB,oLAAA,CAAA,qBAAkB,CAAC,MAAM;AAC/C;;;;;CAKC,GACD,SAAS,eAAe,YAAY,EAAE,SAAS,EAAE,iBAAiB;IAC9D,kDAAkD;IAClD,IAAI,kBAAkB;IACtB,IAAI,qBAAqB;IACzB;;;KAGC,GACD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;QACpC,MAAM,MAAM,oLAAA,CAAA,qBAAkB,CAAC,EAAE;QACjC,MAAM,QAAQ,YAAY,CAAC,IAAI;QAC/B,IAAI,UAAU,WACV;QACJ,IAAI,iBAAiB;QACrB,IAAI,OAAO,UAAU,UAAU;YAC3B,iBAAiB,UAAU,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,CAAC;QAC/D,OACK;YACD,iBAAiB,WAAW,WAAW;QAC3C;QACA,IAAI,CAAC,kBAAkB,mBAAmB;YACtC,MAAM,cAAc,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,gLAAA,CAAA,mBAAgB,CAAC,IAAI;YAC/D,IAAI,CAAC,gBAAgB;gBACjB,qBAAqB;gBACrB,MAAM,gBAAgB,cAAc,CAAC,IAAI,IAAI;gBAC7C,mBAAmB,GAAG,cAAc,CAAC,EAAE,YAAY,EAAE,CAAC;YAC1D;YACA,IAAI,mBAAmB;gBACnB,SAAS,CAAC,IAAI,GAAG;YACrB;QACJ;IACJ;IACA,kBAAkB,gBAAgB,IAAI;IACtC,0EAA0E;IAC1E,qDAAqD;IACrD,IAAI,mBAAmB;QACnB,kBAAkB,kBAAkB,WAAW,qBAAqB,KAAK;IAC7E,OACK,IAAI,oBAAoB;QACzB,kBAAkB;IACtB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5989, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs"], "sourcesContent": ["import { transformProps, isCSSVariableName, getValueAsType, numberValueTypes } from 'motion-dom';\nimport { buildTransform } from './build-transform.mjs';\n\nfunction buildHTMLStyles(state, latestValues, transformTemplate) {\n    const { style, vars, transformOrigin } = state;\n    // Track whether we encounter any transform or transformOrigin values.\n    let hasTransform = false;\n    let hasTransformOrigin = false;\n    /**\n     * Loop over all our latest animated values and decide whether to handle them\n     * as a style or CSS variable.\n     *\n     * Transforms and transform origins are kept separately for further processing.\n     */\n    for (const key in latestValues) {\n        const value = latestValues[key];\n        if (transformProps.has(key)) {\n            // If this is a transform, flag to enable further transform processing\n            hasTransform = true;\n            continue;\n        }\n        else if (isCSSVariableName(key)) {\n            vars[key] = value;\n            continue;\n        }\n        else {\n            // Convert the value to its default value type, ie 0 -> \"0px\"\n            const valueAsType = getValueAsType(value, numberValueTypes[key]);\n            if (key.startsWith(\"origin\")) {\n                // If this is a transform origin, flag and enable further transform-origin processing\n                hasTransformOrigin = true;\n                transformOrigin[key] =\n                    valueAsType;\n            }\n            else {\n                style[key] = valueAsType;\n            }\n        }\n    }\n    if (!latestValues.transform) {\n        if (hasTransform || transformTemplate) {\n            style.transform = buildTransform(latestValues, state.transform, transformTemplate);\n        }\n        else if (style.transform) {\n            /**\n             * If we have previously created a transform but currently don't have any,\n             * reset transform style to none.\n             */\n            style.transform = \"none\";\n        }\n    }\n    /**\n     * Build a transformOrigin style. Uses the same defaults as the browser for\n     * undefined origins.\n     */\n    if (hasTransformOrigin) {\n        const { originX = \"50%\", originY = \"50%\", originZ = 0, } = transformOrigin;\n        style.transformOrigin = `${originX} ${originY} ${originZ}`;\n    }\n}\n\nexport { buildHTMLStyles };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AACA;;;AAEA,SAAS,gBAAgB,KAAK,EAAE,YAAY,EAAE,iBAAiB;IAC3D,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG;IACzC,sEAAsE;IACtE,IAAI,eAAe;IACnB,IAAI,qBAAqB;IACzB;;;;;KAKC,GACD,IAAK,MAAM,OAAO,aAAc;QAC5B,MAAM,QAAQ,YAAY,CAAC,IAAI;QAC/B,IAAI,oLAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,MAAM;YACzB,sEAAsE;YACtE,eAAe;YACf;QACJ,OACK,IAAI,CAAA,GAAA,2LAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM;YAC7B,IAAI,CAAC,IAAI,GAAG;YACZ;QACJ,OACK;YACD,6DAA6D;YAC7D,MAAM,cAAc,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,gLAAA,CAAA,mBAAgB,CAAC,IAAI;YAC/D,IAAI,IAAI,UAAU,CAAC,WAAW;gBAC1B,qFAAqF;gBACrF,qBAAqB;gBACrB,eAAe,CAAC,IAAI,GAChB;YACR,OACK;gBACD,KAAK,CAAC,IAAI,GAAG;YACjB;QACJ;IACJ;IACA,IAAI,CAAC,aAAa,SAAS,EAAE;QACzB,IAAI,gBAAgB,mBAAmB;YACnC,MAAM,SAAS,GAAG,CAAA,GAAA,gMAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,MAAM,SAAS,EAAE;QACpE,OACK,IAAI,MAAM,SAAS,EAAE;YACtB;;;aAGC,GACD,MAAM,SAAS,GAAG;QACtB;IACJ;IACA;;;KAGC,GACD,IAAI,oBAAoB;QACpB,MAAM,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,UAAU,CAAC,EAAG,GAAG;QAC3D,MAAM,eAAe,GAAG,GAAG,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS;IAC9D;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6055, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs"], "sourcesContent": ["const createHtmlRenderState = () => ({\n    style: {},\n    transform: {},\n    transformOrigin: {},\n    vars: {},\n});\n\nexport { createHtmlRenderState };\n"], "names": [], "mappings": ";;;AAAA,MAAM,wBAAwB,IAAM,CAAC;QACjC,OAAO,CAAC;QACR,WAAW,CAAC;QACZ,iBAAiB,CAAC;QAClB,MAAM,CAAC;IACX,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6071, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/html/use-props.mjs"], "sourcesContent": ["import { isMotionValue } from 'motion-dom';\nimport { useMemo } from 'react';\nimport { isForcedMotionValue } from '../../motion/utils/is-forced-motion-value.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\n\nfunction copyRawValuesOnly(target, source, props) {\n    for (const key in source) {\n        if (!isMotionValue(source[key]) && !isForcedMotionValue(key, props)) {\n            target[key] = source[key];\n        }\n    }\n}\nfunction useInitialMotionValues({ transformTemplate }, visualState) {\n    return useMemo(() => {\n        const state = createHtmlRenderState();\n        buildHTMLStyles(state, visualState, transformTemplate);\n        return Object.assign({}, state.vars, state.style);\n    }, [visualState]);\n}\nfunction useStyle(props, visualState) {\n    const styleProp = props.style || {};\n    const style = {};\n    /**\n     * Copy non-Motion Values straight into style\n     */\n    copyRawValuesOnly(style, styleProp, props);\n    Object.assign(style, useInitialMotionValues(props, visualState));\n    return style;\n}\nfunction useHTMLProps(props, visualState) {\n    // The `any` isn't ideal but it is the type of createElement props argument\n    const htmlProps = {};\n    const style = useStyle(props, visualState);\n    if (props.drag && props.dragListener !== false) {\n        // Disable the ghost element when a user drags\n        htmlProps.draggable = false;\n        // Disable text selection\n        style.userSelect =\n            style.WebkitUserSelect =\n                style.WebkitTouchCallout =\n                    \"none\";\n        // Disable scrolling on the draggable direction\n        style.touchAction =\n            props.drag === true\n                ? \"none\"\n                : `pan-${props.drag === \"x\" ? \"y\" : \"x\"}`;\n    }\n    if (props.tabIndex === undefined &&\n        (props.onTap || props.onTapStart || props.whileTap)) {\n        htmlProps.tabIndex = 0;\n    }\n    htmlProps.style = style;\n    return htmlProps;\n}\n\nexport { copyRawValuesOnly, useHTMLProps };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,SAAS,kBAAkB,MAAM,EAAE,MAAM,EAAE,KAAK;IAC5C,IAAK,MAAM,OAAO,OAAQ;QACtB,IAAI,CAAC,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,CAAC,IAAI,KAAK,CAAC,CAAA,GAAA,qMAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,QAAQ;YACjE,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAC7B;IACJ;AACJ;AACA,SAAS,uBAAuB,EAAE,iBAAiB,EAAE,EAAE,WAAW;IAC9D,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACX,MAAM,QAAQ,CAAA,GAAA,uMAAA,CAAA,wBAAqB,AAAD;QAClC,CAAA,GAAA,6LAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,aAAa;QACpC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,IAAI,EAAE,MAAM,KAAK;IACpD,GAAG;QAAC;KAAY;AACpB;AACA,SAAS,SAAS,KAAK,EAAE,WAAW;IAChC,MAAM,YAAY,MAAM,KAAK,IAAI,CAAC;IAClC,MAAM,QAAQ,CAAC;IACf;;KAEC,GACD,kBAAkB,OAAO,WAAW;IACpC,OAAO,MAAM,CAAC,OAAO,uBAAuB,OAAO;IACnD,OAAO;AACX;AACA,SAAS,aAAa,KAAK,EAAE,WAAW;IACpC,2EAA2E;IAC3E,MAAM,YAAY,CAAC;IACnB,MAAM,QAAQ,SAAS,OAAO;IAC9B,IAAI,MAAM,IAAI,IAAI,MAAM,YAAY,KAAK,OAAO;QAC5C,8CAA8C;QAC9C,UAAU,SAAS,GAAG;QACtB,yBAAyB;QACzB,MAAM,UAAU,GACZ,MAAM,gBAAgB,GAClB,MAAM,kBAAkB,GACpB;QACZ,+CAA+C;QAC/C,MAAM,WAAW,GACb,MAAM,IAAI,KAAK,OACT,SACA,CAAC,IAAI,EAAE,MAAM,IAAI,KAAK,MAAM,MAAM,KAAK;IACrD;IACA,IAAI,MAAM,QAAQ,KAAK,aACnB,CAAC,MAAM,KAAK,IAAI,MAAM,UAAU,IAAI,MAAM,QAAQ,GAAG;QACrD,UAAU,QAAQ,GAAG;IACzB;IACA,UAAU,KAAK,GAAG;IAClB,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6135, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/svg/utils/path.mjs"], "sourcesContent": ["import { px } from 'motion-dom';\n\nconst dashKeys = {\n    offset: \"stroke-dashoffset\",\n    array: \"stroke-dasharray\",\n};\nconst camelKeys = {\n    offset: \"strokeDashoffset\",\n    array: \"strokeDasharray\",\n};\n/**\n * Build SVG path properties. Uses the path's measured length to convert\n * our custom pathLength, pathSpacing and pathOffset into stroke-dashoffset\n * and stroke-dasharray attributes.\n *\n * This function is mutative to reduce per-frame GC.\n */\nfunction buildSVGPath(attrs, length, spacing = 1, offset = 0, useDashCase = true) {\n    // Normalise path length by setting SVG attribute pathLength to 1\n    attrs.pathLength = 1;\n    // We use dash case when setting attributes directly to the DOM node and camel case\n    // when defining props on a React component.\n    const keys = useDashCase ? dashKeys : camelKeys;\n    // Build the dash offset\n    attrs[keys.offset] = px.transform(-offset);\n    // Build the dash array\n    const pathLength = px.transform(length);\n    const pathSpacing = px.transform(spacing);\n    attrs[keys.array] = `${pathLength} ${pathSpacing}`;\n}\n\nexport { buildSVGPath };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,WAAW;IACb,QAAQ;IACR,OAAO;AACX;AACA,MAAM,YAAY;IACd,QAAQ;IACR,OAAO;AACX;AACA;;;;;;CAMC,GACD,SAAS,aAAa,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,SAAS,CAAC,EAAE,cAAc,IAAI;IAC5E,iEAAiE;IACjE,MAAM,UAAU,GAAG;IACnB,mFAAmF;IACnF,4CAA4C;IAC5C,MAAM,OAAO,cAAc,WAAW;IACtC,wBAAwB;IACxB,KAAK,CAAC,KAAK,MAAM,CAAC,GAAG,kLAAA,CAAA,KAAE,CAAC,SAAS,CAAC,CAAC;IACnC,uBAAuB;IACvB,MAAM,aAAa,kLAAA,CAAA,KAAE,CAAC,SAAS,CAAC;IAChC,MAAM,cAAc,kLAAA,CAAA,KAAE,CAAC,SAAS,CAAC;IACjC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,GAAG,WAAW,CAAC,EAAE,aAAa;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6174, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs"], "sourcesContent": ["import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attributes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, { attrX, attrY, attrScale, pathLength, pathSpacing = 1, pathOffset = 0, \n// This is object creation, which we try to avoid per-frame.\n...latest }, isSVGTag, transformTemplate, styleProp) {\n    buildHTMLStyles(state, latest, transformTemplate);\n    /**\n     * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n     * as normal HTML tags.\n     */\n    if (isSVGTag) {\n        if (state.style.viewBox) {\n            state.attrs.viewBox = state.style.viewBox;\n        }\n        return;\n    }\n    state.attrs = state.style;\n    state.style = {};\n    const { attrs, style } = state;\n    /**\n     * However, we apply transforms as CSS transforms.\n     * So if we detect a transform, transformOrigin we take it from attrs and copy it into style.\n     */\n    if (attrs.transform) {\n        style.transform = attrs.transform;\n        delete attrs.transform;\n    }\n    if (style.transform || attrs.transformOrigin) {\n        style.transformOrigin = attrs.transformOrigin ?? \"50% 50%\";\n        delete attrs.transformOrigin;\n    }\n    if (style.transform) {\n        /**\n         * SVG's element transform-origin uses its own median as a reference.\n         * Therefore, transformBox becomes a fill-box\n         */\n        style.transformBox = styleProp?.transformBox ?? \"fill-box\";\n        delete attrs.transformBox;\n    }\n    // Render attrX/attrY/attrScale as attributes\n    if (attrX !== undefined)\n        attrs.x = attrX;\n    if (attrY !== undefined)\n        attrs.y = attrY;\n    if (attrScale !== undefined)\n        attrs.scale = attrScale;\n    // Build SVG path if one has been defined\n    if (pathLength !== undefined) {\n        buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n    }\n}\n\nexport { buildSVGAttrs };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA;;CAEC,GACD,SAAS,cAAc,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,CAAC,EAAE,aAAa,CAAC,EACpG,4DAA4D;AAC5D,GAAG,QAAQ,EAAE,QAAQ,EAAE,iBAAiB,EAAE,SAAS;IAC/C,CAAA,GAAA,6LAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,QAAQ;IAC/B;;;KAGC,GACD,IAAI,UAAU;QACV,IAAI,MAAM,KAAK,CAAC,OAAO,EAAE;YACrB,MAAM,KAAK,CAAC,OAAO,GAAG,MAAM,KAAK,CAAC,OAAO;QAC7C;QACA;IACJ;IACA,MAAM,KAAK,GAAG,MAAM,KAAK;IACzB,MAAM,KAAK,GAAG,CAAC;IACf,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG;IACzB;;;KAGC,GACD,IAAI,MAAM,SAAS,EAAE;QACjB,MAAM,SAAS,GAAG,MAAM,SAAS;QACjC,OAAO,MAAM,SAAS;IAC1B;IACA,IAAI,MAAM,SAAS,IAAI,MAAM,eAAe,EAAE;QAC1C,MAAM,eAAe,GAAG,MAAM,eAAe,IAAI;QACjD,OAAO,MAAM,eAAe;IAChC;IACA,IAAI,MAAM,SAAS,EAAE;QACjB;;;SAGC,GACD,MAAM,YAAY,GAAG,WAAW,gBAAgB;QAChD,OAAO,MAAM,YAAY;IAC7B;IACA,6CAA6C;IAC7C,IAAI,UAAU,WACV,MAAM,CAAC,GAAG;IACd,IAAI,UAAU,WACV,MAAM,CAAC,GAAG;IACd,IAAI,cAAc,WACd,MAAM,KAAK,GAAG;IAClB,yCAAyC;IACzC,IAAI,eAAe,WAAW;QAC1B,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,OAAO,YAAY,aAAa,YAAY;IAC7D;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6232, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs"], "sourcesContent": ["import { createHtmlRenderState } from '../../html/utils/create-render-state.mjs';\n\nconst createSvgRenderState = () => ({\n    ...createHtmlRenderState(),\n    attrs: {},\n});\n\nexport { createSvgRenderState };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,uBAAuB,IAAM,CAAC;QAChC,GAAG,CAAA,GAAA,uMAAA,CAAA,wBAAqB,AAAD,GAAG;QAC1B,OAAO,CAAC;IACZ,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6248, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs"], "sourcesContent": ["const isSVGTag = (tag) => typeof tag === \"string\" && tag.toLowerCase() === \"svg\";\n\nexport { isSVGTag };\n"], "names": [], "mappings": ";;;AAAA,MAAM,WAAW,CAAC,MAAQ,OAAO,QAAQ,YAAY,IAAI,WAAW,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6259, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/svg/use-props.mjs"], "sourcesContent": ["import { useMemo } from 'react';\nimport { copyRawValuesOnly } from '../html/use-props.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\n\nfunction useSVGProps(props, visualState, _isStatic, Component) {\n    const visualProps = useMemo(() => {\n        const state = createSvgRenderState();\n        buildSVGAttrs(state, visualState, isSVGTag(Component), props.transformTemplate, props.style);\n        return {\n            ...state.attrs,\n            style: { ...state.style },\n        };\n    }, [visualState]);\n    if (props.style) {\n        const rawStyles = {};\n        copyRawValuesOnly(rawStyles, props.style, props);\n        visualProps.style = { ...rawStyles, ...visualProps.style };\n    }\n    return visualProps;\n}\n\nexport { useSVGProps };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,SAAS,YAAY,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS;IACzD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,MAAM,QAAQ,CAAA,GAAA,sMAAA,CAAA,uBAAoB,AAAD;QACjC,CAAA,GAAA,2LAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,aAAa,CAAA,GAAA,6LAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,MAAM,iBAAiB,EAAE,MAAM,KAAK;QAC3F,OAAO;YACH,GAAG,MAAM,KAAK;YACd,OAAO;gBAAE,GAAG,MAAM,KAAK;YAAC;QAC5B;IACJ,GAAG;QAAC;KAAY;IAChB,IAAI,MAAM,KAAK,EAAE;QACb,MAAM,YAAY,CAAC;QACnB,CAAA,GAAA,iLAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,MAAM,KAAK,EAAE;QAC1C,YAAY,KAAK,GAAG;YAAE,GAAG,SAAS;YAAE,GAAG,YAAY,KAAK;QAAC;IAC7D;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6302, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs"], "sourcesContent": ["/**\n * A list of all valid MotionProps.\n *\n * @privateRemarks\n * This doesn't throw if a `MotionProp` name is missing - it should.\n */\nconst validMotionProps = new Set([\n    \"animate\",\n    \"exit\",\n    \"variants\",\n    \"initial\",\n    \"style\",\n    \"values\",\n    \"variants\",\n    \"transition\",\n    \"transformTemplate\",\n    \"custom\",\n    \"inherit\",\n    \"onBeforeLayoutMeasure\",\n    \"onAnimationStart\",\n    \"onAnimationComplete\",\n    \"onUpdate\",\n    \"onDragStart\",\n    \"onDrag\",\n    \"onDragEnd\",\n    \"onMeasureDragConstraints\",\n    \"onDirectionLock\",\n    \"onDragTransitionEnd\",\n    \"_dragX\",\n    \"_dragY\",\n    \"onHoverStart\",\n    \"onHoverEnd\",\n    \"onViewportEnter\",\n    \"onViewportLeave\",\n    \"globalTapTarget\",\n    \"ignoreStrict\",\n    \"viewport\",\n]);\n/**\n * Check whether a prop name is a valid `MotionProp` key.\n *\n * @param key - Name of the property to check\n * @returns `true` is key is a valid `MotionProp`.\n *\n * @public\n */\nfunction isValidMotionProp(key) {\n    return (key.startsWith(\"while\") ||\n        (key.startsWith(\"drag\") && key !== \"draggable\") ||\n        key.startsWith(\"layout\") ||\n        key.startsWith(\"onTap\") ||\n        key.startsWith(\"onPan\") ||\n        key.startsWith(\"onLayout\") ||\n        validMotionProps.has(key));\n}\n\nexport { isValidMotionProp };\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AACD,MAAM,mBAAmB,IAAI,IAAI;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD;;;;;;;CAOC,GACD,SAAS,kBAAkB,GAAG;IAC1B,OAAQ,IAAI,UAAU,CAAC,YAClB,IAAI,UAAU,CAAC,WAAW,QAAQ,eACnC,IAAI,UAAU,CAAC,aACf,IAAI,UAAU,CAAC,YACf,IAAI,UAAU,CAAC,YACf,IAAI,UAAU,CAAC,eACf,iBAAiB,GAAG,CAAC;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6359, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs"], "sourcesContent": ["import { isValidMotionProp } from '../../../motion/utils/valid-prop.mjs';\n\nlet shouldForward = (key) => !isValidMotionProp(key);\nfunction loadExternalIsValidProp(isValidProp) {\n    if (typeof isValidProp !== \"function\")\n        return;\n    // Explicitly filter our events\n    shouldForward = (key) => key.startsWith(\"on\") ? !isValidMotionProp(key) : isValidProp(key);\n}\n/**\n * Emotion and Styled Components both allow users to pass through arbitrary props to their components\n * to dynamically generate CSS. They both use the `@emotion/is-prop-valid` package to determine which\n * of these should be passed to the underlying DOM node.\n *\n * However, when styling a Motion component `styled(motion.div)`, both packages pass through *all* props\n * as it's seen as an arbitrary component rather than a DOM node. Motion only allows arbitrary props\n * passed through the `custom` prop so it doesn't *need* the payload or computational overhead of\n * `@emotion/is-prop-valid`, however to fix this problem we need to use it.\n *\n * By making it an optionalDependency we can offer this functionality only in the situations where it's\n * actually required.\n */\ntry {\n    /**\n     * We attempt to import this package but require won't be defined in esm environments, in that case\n     * isPropValid will have to be provided via `MotionContext`. In a 6.0.0 this should probably be removed\n     * in favour of explicit injection.\n     */\n    loadExternalIsValidProp(require(\"@emotion/is-prop-valid\").default);\n}\ncatch {\n    // We don't need to actually do anything here - the fallback is the existing `isPropValid`.\n}\nfunction filterProps(props, isDom, forwardMotionProps) {\n    const filteredProps = {};\n    for (const key in props) {\n        /**\n         * values is considered a valid prop by Emotion, so if it's present\n         * this will be rendered out to the DOM unless explicitly filtered.\n         *\n         * We check the type as it could be used with the `feColorMatrix`\n         * element, which we support.\n         */\n        if (key === \"values\" && typeof props.values === \"object\")\n            continue;\n        if (shouldForward(key) ||\n            (forwardMotionProps === true && isValidMotionProp(key)) ||\n            (!isDom && !isValidMotionProp(key)) ||\n            // If trying to use native HTML drag events, forward drag listeners\n            (props[\"draggable\"] &&\n                key.startsWith(\"onDrag\"))) {\n            filteredProps[key] =\n                props[key];\n        }\n    }\n    return filteredProps;\n}\n\nexport { filterProps, loadExternalIsValidProp };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,IAAI,gBAAgB,CAAC,MAAQ,CAAC,CAAA,GAAA,mLAAA,CAAA,oBAAiB,AAAD,EAAE;AAChD,SAAS,wBAAwB,WAAW;IACxC,IAAI,OAAO,gBAAgB,YACvB;IACJ,+BAA+B;IAC/B,gBAAgB,CAAC,MAAQ,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAA,GAAA,mLAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO,YAAY;AAC1F;AACA;;;;;;;;;;;;CAYC,GACD,IAAI;IACA;;;;KAIC,GACD,wBAAwB;;;;SAAkC,OAAO;AACrE,EACA,OAAM;AACF,2FAA2F;AAC/F;AACA,SAAS,YAAY,KAAK,EAAE,KAAK,EAAE,kBAAkB;IACjD,MAAM,gBAAgB,CAAC;IACvB,IAAK,MAAM,OAAO,MAAO;QACrB;;;;;;SAMC,GACD,IAAI,QAAQ,YAAY,OAAO,MAAM,MAAM,KAAK,UAC5C;QACJ,IAAI,cAAc,QACb,uBAAuB,QAAQ,CAAA,GAAA,mLAAA,CAAA,oBAAiB,AAAD,EAAE,QACjD,CAAC,SAAS,CAAC,CAAA,GAAA,mLAAA,CAAA,oBAAiB,AAAD,EAAE,QAE7B,KAAK,CAAC,YAAY,IACf,IAAI,UAAU,CAAC,WAAY;YAC/B,aAAa,CAAC,IAAI,GACd,KAAK,CAAC,IAAI;QAClB;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6419, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs"], "sourcesContent": ["/**\n * We keep these listed separately as we use the lowercase tag names as part\n * of the runtime bundle to detect SVG components\n */\nconst lowercaseSVGElements = [\n    \"animate\",\n    \"circle\",\n    \"defs\",\n    \"desc\",\n    \"ellipse\",\n    \"g\",\n    \"image\",\n    \"line\",\n    \"filter\",\n    \"marker\",\n    \"mask\",\n    \"metadata\",\n    \"path\",\n    \"pattern\",\n    \"polygon\",\n    \"polyline\",\n    \"rect\",\n    \"stop\",\n    \"switch\",\n    \"symbol\",\n    \"svg\",\n    \"text\",\n    \"tspan\",\n    \"use\",\n    \"view\",\n];\n\nexport { lowercaseSVGElements };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACD,MAAM,uBAAuB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6459, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs"], "sourcesContent": ["import { lowercaseSVGElements } from '../../svg/lowercase-elements.mjs';\n\nfunction isSVGComponent(Component) {\n    if (\n    /**\n     * If it's not a string, it's a custom React component. Currently we only support\n     * HTML custom React components.\n     */\n    typeof Component !== \"string\" ||\n        /**\n         * If it contains a dash, the element is a custom HTML webcomponent.\n         */\n        Component.includes(\"-\")) {\n        return false;\n    }\n    else if (\n    /**\n     * If it's in our list of lowercase SVG tags, it's an SVG component\n     */\n    lowercaseSVGElements.indexOf(Component) > -1 ||\n        /**\n         * If it contains a capital letter, it's an SVG component\n         */\n        /[A-Z]/u.test(Component)) {\n        return true;\n    }\n    return false;\n}\n\nexport { isSVGComponent };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,eAAe,SAAS;IAC7B,IACA;;;KAGC,GACD,OAAO,cAAc,YACjB;;SAEC,GACD,UAAU,QAAQ,CAAC,MAAM;QACzB,OAAO;IACX,OACK,IACL;;KAEC,GACD,yLAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC,aAAa,CAAC,KACvC;;SAEC,GACD,SAAS,IAAI,CAAC,YAAY;QAC1B,OAAO;IACX;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6488, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/dom/use-render.mjs"], "sourcesContent": ["import { isMotionValue } from 'motion-dom';\nimport { Fragment, useMemo, createElement } from 'react';\nimport { useHTMLProps } from '../html/use-props.mjs';\nimport { useSVGProps } from '../svg/use-props.mjs';\nimport { filterProps } from './utils/filter-props.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\n\nfunction createUseRender(forwardMotionProps = false) {\n    const useRender = (Component, props, ref, { latestValues }, isStatic) => {\n        const useVisualProps = isSVGComponent(Component)\n            ? useSVGProps\n            : useHTMLProps;\n        const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n        const filteredProps = filterProps(props, typeof Component === \"string\", forwardMotionProps);\n        const elementProps = Component !== Fragment\n            ? { ...filteredProps, ...visualProps, ref }\n            : {};\n        /**\n         * If component has been handed a motion value as its child,\n         * memoise its initial value and render that. Subsequent updates\n         * will be handled by the onChange handler\n         */\n        const { children } = props;\n        const renderedChildren = useMemo(() => (isMotionValue(children) ? children.get() : children), [children]);\n        return createElement(Component, {\n            ...elementProps,\n            children: renderedChildren,\n        });\n    };\n    return useRender;\n}\n\nexport { createUseRender };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,SAAS,gBAAgB,qBAAqB,KAAK;IAC/C,MAAM,YAAY,CAAC,WAAW,OAAO,KAAK,EAAE,YAAY,EAAE,EAAE;QACxD,MAAM,iBAAiB,CAAA,GAAA,mMAAA,CAAA,iBAAc,AAAD,EAAE,aAChC,gLAAA,CAAA,cAAW,GACX,iLAAA,CAAA,eAAY;QAClB,MAAM,cAAc,eAAe,OAAO,cAAc,UAAU;QAClE,MAAM,gBAAgB,CAAA,GAAA,4LAAA,CAAA,cAAW,AAAD,EAAE,OAAO,OAAO,cAAc,UAAU;QACxE,MAAM,eAAe,cAAc,qMAAA,CAAA,WAAQ,GACrC;YAAE,GAAG,aAAa;YAAE,GAAG,WAAW;YAAE;QAAI,IACxC,CAAC;QACP;;;;SAIC,GACD,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAO,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,SAAS,GAAG,KAAK,UAAW;YAAC;SAAS;QACxG,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;YAC5B,GAAG,YAAY;YACf,UAAU;QACd;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6535, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs"], "sourcesContent": ["import { useContext } from 'react';\nimport { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { isControllingVariants, isVariantNode } from '../../render/utils/is-controlling-variants.mjs';\nimport { resolveVariantFromProps } from '../../render/utils/resolve-variants.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\n\nfunction makeState({ scrapeMotionValuesFromProps, createRenderState, }, props, context, presenceContext) {\n    const state = {\n        latestValues: makeLatestValues(props, context, presenceContext, scrapeMotionValuesFromProps),\n        renderState: createRenderState(),\n    };\n    return state;\n}\nconst makeUseVisualState = (config) => (props, isStatic) => {\n    const context = useContext(MotionContext);\n    const presenceContext = useContext(PresenceContext);\n    const make = () => makeState(config, props, context, presenceContext);\n    return isStatic ? make() : useConstant(make);\n};\nfunction makeLatestValues(props, context, presenceContext, scrapeMotionValues) {\n    const values = {};\n    const motionValues = scrapeMotionValues(props, {});\n    for (const key in motionValues) {\n        values[key] = resolveMotionValue(motionValues[key]);\n    }\n    let { initial, animate } = props;\n    const isControllingVariants$1 = isControllingVariants(props);\n    const isVariantNode$1 = isVariantNode(props);\n    if (context &&\n        isVariantNode$1 &&\n        !isControllingVariants$1 &&\n        props.inherit !== false) {\n        if (initial === undefined)\n            initial = context.initial;\n        if (animate === undefined)\n            animate = context.animate;\n    }\n    let isInitialAnimationBlocked = presenceContext\n        ? presenceContext.initial === false\n        : false;\n    isInitialAnimationBlocked = isInitialAnimationBlocked || initial === false;\n    const variantToSet = isInitialAnimationBlocked ? animate : initial;\n    if (variantToSet &&\n        typeof variantToSet !== \"boolean\" &&\n        !isAnimationControls(variantToSet)) {\n        const list = Array.isArray(variantToSet) ? variantToSet : [variantToSet];\n        for (let i = 0; i < list.length; i++) {\n            const resolved = resolveVariantFromProps(props, list[i]);\n            if (resolved) {\n                const { transitionEnd, transition, ...target } = resolved;\n                for (const key in target) {\n                    let valueTarget = target[key];\n                    if (Array.isArray(valueTarget)) {\n                        /**\n                         * Take final keyframe if the initial animation is blocked because\n                         * we want to initialise at the end of that blocked animation.\n                         */\n                        const index = isInitialAnimationBlocked\n                            ? valueTarget.length - 1\n                            : 0;\n                        valueTarget = valueTarget[index];\n                    }\n                    if (valueTarget !== null) {\n                        values[key] = valueTarget;\n                    }\n                }\n                for (const key in transitionEnd) {\n                    values[key] = transitionEnd[key];\n                }\n            }\n        }\n    }\n    return values;\n}\n\nexport { makeUseVisualState };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,SAAS,UAAU,EAAE,2BAA2B,EAAE,iBAAiB,EAAG,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe;IACnG,MAAM,QAAQ;QACV,cAAc,iBAAiB,OAAO,SAAS,iBAAiB;QAChE,aAAa;IACjB;IACA,OAAO;AACX;AACA,MAAM,qBAAqB,CAAC,SAAW,CAAC,OAAO;QAC3C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,oLAAA,CAAA,gBAAa;QACxC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,6KAAA,CAAA,kBAAe;QAClD,MAAM,OAAO,IAAM,UAAU,QAAQ,OAAO,SAAS;QACrD,OAAO,WAAW,SAAS,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;IAC3C;AACA,SAAS,iBAAiB,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,kBAAkB;IACzE,MAAM,SAAS,CAAC;IAChB,MAAM,eAAe,mBAAmB,OAAO,CAAC;IAChD,IAAK,MAAM,OAAO,aAAc;QAC5B,MAAM,CAAC,IAAI,GAAG,CAAA,GAAA,+LAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,CAAC,IAAI;IACtD;IACA,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAC3B,MAAM,0BAA0B,CAAA,GAAA,mMAAA,CAAA,wBAAqB,AAAD,EAAE;IACtD,MAAM,kBAAkB,CAAA,GAAA,mMAAA,CAAA,gBAAa,AAAD,EAAE;IACtC,IAAI,WACA,mBACA,CAAC,2BACD,MAAM,OAAO,KAAK,OAAO;QACzB,IAAI,YAAY,WACZ,UAAU,QAAQ,OAAO;QAC7B,IAAI,YAAY,WACZ,UAAU,QAAQ,OAAO;IACjC;IACA,IAAI,4BAA4B,kBAC1B,gBAAgB,OAAO,KAAK,QAC5B;IACN,4BAA4B,6BAA6B,YAAY;IACrE,MAAM,eAAe,4BAA4B,UAAU;IAC3D,IAAI,gBACA,OAAO,iBAAiB,aACxB,CAAC,CAAA,GAAA,oMAAA,CAAA,sBAAmB,AAAD,EAAE,eAAe;QACpC,MAAM,OAAO,MAAM,OAAO,CAAC,gBAAgB,eAAe;YAAC;SAAa;QACxE,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,IAAI,CAAC,EAAE;YACvD,IAAI,UAAU;gBACV,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,QAAQ,GAAG;gBACjD,IAAK,MAAM,OAAO,OAAQ;oBACtB,IAAI,cAAc,MAAM,CAAC,IAAI;oBAC7B,IAAI,MAAM,OAAO,CAAC,cAAc;wBAC5B;;;yBAGC,GACD,MAAM,QAAQ,4BACR,YAAY,MAAM,GAAG,IACrB;wBACN,cAAc,WAAW,CAAC,MAAM;oBACpC;oBACA,IAAI,gBAAgB,MAAM;wBACtB,MAAM,CAAC,IAAI,GAAG;oBAClB;gBACJ;gBACA,IAAK,MAAM,OAAO,cAAe;oBAC7B,MAAM,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI;gBACpC;YACJ;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6619, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs"], "sourcesContent": ["import { isMotionValue } from 'motion-dom';\nimport { isForcedMotionValue } from '../../../motion/utils/is-forced-motion-value.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    const { style } = props;\n    const newValues = {};\n    for (const key in style) {\n        if (isMotionValue(style[key]) ||\n            (prevProps.style &&\n                isMotionValue(prevProps.style[key])) ||\n            isForcedMotionValue(key, props) ||\n            visualElement?.getValue(key)?.liveStyle !== undefined) {\n            newValues[key] = style[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,4BAA4B,KAAK,EAAE,SAAS,EAAE,aAAa;IAChE,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,MAAM,YAAY,CAAC;IACnB,IAAK,MAAM,OAAO,MAAO;QACrB,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,CAAC,IAAI,KACvB,UAAU,KAAK,IACZ,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,KAAK,CAAC,IAAI,KACtC,CAAA,GAAA,qMAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,UACzB,eAAe,SAAS,MAAM,cAAc,WAAW;YACvD,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;QAC/B;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6643, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/html/config-motion.mjs"], "sourcesContent": ["import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\n\nconst htmlMotionConfig = {\n    useVisualState: makeUseVisualState({\n        scrapeMotionValuesFromProps,\n        createRenderState: createHtmlRenderState,\n    }),\n};\n\nexport { htmlMotionConfig };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,mBAAmB;IACrB,gBAAgB,CAAA,GAAA,4LAAA,CAAA,qBAAkB,AAAD,EAAE;QAC/B,6BAAA,wMAAA,CAAA,8BAA2B;QAC3B,mBAAmB,uMAAA,CAAA,wBAAqB;IAC5C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6665, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs"], "sourcesContent": ["import { isMotionValue, transformPropOrder } from 'motion-dom';\nimport { scrapeMotionValuesFromProps as scrapeMotionValuesFromProps$1 } from '../../html/utils/scrape-motion-values.mjs';\n\nfunction scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    const newValues = scrapeMotionValuesFromProps$1(props, prevProps, visualElement);\n    for (const key in props) {\n        if (isMotionValue(props[key]) ||\n            isMotionValue(prevProps[key])) {\n            const targetKey = transformPropOrder.indexOf(key) !== -1\n                ? \"attr\" + key.charAt(0).toUpperCase() + key.substring(1)\n                : key;\n            newValues[targetKey] = props[key];\n        }\n    }\n    return newValues;\n}\n\nexport { scrapeMotionValuesFromProps };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA,SAAS,4BAA4B,KAAK,EAAE,SAAS,EAAE,aAAa;IAChE,MAAM,YAAY,CAAA,GAAA,wMAAA,CAAA,8BAA6B,AAAD,EAAE,OAAO,WAAW;IAClE,IAAK,MAAM,OAAO,MAAO;QACrB,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,CAAC,IAAI,KACxB,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,CAAC,IAAI,GAAG;YAC/B,MAAM,YAAY,oLAAA,CAAA,qBAAkB,CAAC,OAAO,CAAC,SAAS,CAAC,IACjD,SAAS,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,SAAS,CAAC,KACrD;YACN,SAAS,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI;QACrC;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6690, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/svg/config-motion.mjs"], "sourcesContent": ["import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nconst svgMotionConfig = {\n    useVisualState: makeUseVisualState({\n        scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n        createRenderState: createSvgRenderState,\n    }),\n};\n\nexport { svgMotionConfig };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,kBAAkB;IACpB,gBAAgB,CAAA,GAAA,4LAAA,CAAA,qBAAkB,AAAD,EAAE;QAC/B,6BAA6B,uMAAA,CAAA,8BAA2B;QACxD,mBAAmB,sMAAA,CAAA,uBAAoB;IAC3C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6712, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/components/create-factory.mjs"], "sourcesContent": ["import { createRendererMotionComponent } from '../../motion/index.mjs';\nimport { createUseRender } from '../dom/use-render.mjs';\nimport { isSVGComponent } from '../dom/utils/is-svg-component.mjs';\nimport { htmlMotionConfig } from '../html/config-motion.mjs';\nimport { svgMotionConfig } from '../svg/config-motion.mjs';\n\nfunction createMotionComponentFactory(preloadedFeatures, createVisualElement) {\n    return function createMotionComponent(Component, { forwardMotionProps } = { forwardMotionProps: false }) {\n        const baseConfig = isSVGComponent(Component)\n            ? svgMotionConfig\n            : htmlMotionConfig;\n        const config = {\n            ...baseConfig,\n            preloadedFeatures,\n            useRender: createUseRender(forwardMotionProps),\n            createVisualElement,\n            Component,\n        };\n        return createRendererMotionComponent(config);\n    };\n}\n\nexport { createMotionComponentFactory };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,SAAS,6BAA6B,iBAAiB,EAAE,mBAAmB;IACxE,OAAO,SAAS,sBAAsB,SAAS,EAAE,EAAE,kBAAkB,EAAE,GAAG;QAAE,oBAAoB;IAAM,CAAC;QACnG,MAAM,aAAa,CAAA,GAAA,mMAAA,CAAA,iBAAc,AAAD,EAAE,aAC5B,oLAAA,CAAA,kBAAe,GACf,qLAAA,CAAA,mBAAgB;QACtB,MAAM,SAAS;YACX,GAAG,UAAU;YACb;YACA,WAAW,CAAA,GAAA,iLAAA,CAAA,kBAAe,AAAD,EAAE;YAC3B;YACA;QACJ;QACA,OAAO,CAAA,GAAA,kKAAA,CAAA,gCAA6B,AAAD,EAAE;IACzC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6747, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/utils/reduced-motion/state.mjs"], "sourcesContent": ["// Does this device prefer reduced motion? Returns `null` server-side.\nconst prefersReducedMotion = { current: null };\nconst hasReducedMotionListener = { current: false };\n\nexport { hasReducedMotionListener, prefersReducedMotion };\n"], "names": [], "mappings": "AAAA,sEAAsE;;;;;AACtE,MAAM,uBAAuB;IAAE,SAAS;AAAK;AAC7C,MAAM,2BAA2B;IAAE,SAAS;AAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6765, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/utils/reduced-motion/index.mjs"], "sourcesContent": ["import { isBrowser } from '../is-browser.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from './state.mjs';\n\nfunction initPrefersReducedMotion() {\n    hasReducedMotionListener.current = true;\n    if (!isBrowser)\n        return;\n    if (window.matchMedia) {\n        const motionMediaQuery = window.matchMedia(\"(prefers-reduced-motion)\");\n        const setReducedMotionPreferences = () => (prefersReducedMotion.current = motionMediaQuery.matches);\n        motionMediaQuery.addEventListener(\"change\", setReducedMotionPreferences);\n        setReducedMotionPreferences();\n    }\n    else {\n        prefersReducedMotion.current = false;\n    }\n}\n\nexport { initPrefersReducedMotion };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS;IACL,sLAAA,CAAA,2BAAwB,CAAC,OAAO,GAAG;IACnC,IAAI,CAAC,yKAAA,CAAA,YAAS,EACV;IACJ,IAAI,OAAO,UAAU,EAAE;QACnB,MAAM,mBAAmB,OAAO,UAAU,CAAC;QAC3C,MAAM,8BAA8B,IAAO,sLAAA,CAAA,uBAAoB,CAAC,OAAO,GAAG,iBAAiB,OAAO;QAClG,iBAAiB,gBAAgB,CAAC,UAAU;QAC5C;IACJ,OACK;QACD,sLAAA,CAAA,uBAAoB,CAAC,OAAO,GAAG;IACnC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6791, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/store.mjs"], "sourcesContent": ["const visualElementStore = new WeakMap();\n\nexport { visualElementStore };\n"], "names": [], "mappings": ";;;AAAA,MAAM,qBAAqB,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6802, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/utils/motion-values.mjs"], "sourcesContent": ["import { isMotionValue, motionValue } from 'motion-dom';\n\nfunction updateMotionValuesFromProps(element, next, prev) {\n    for (const key in next) {\n        const nextValue = next[key];\n        const prevValue = prev[key];\n        if (isMotionValue(nextValue)) {\n            /**\n             * If this is a motion value found in props or style, we want to add it\n             * to our visual element's motion value map.\n             */\n            element.addValue(key, nextValue);\n        }\n        else if (isMotionValue(prevValue)) {\n            /**\n             * If we're swapping from a motion value to a static value,\n             * create a new motion value from that\n             */\n            element.addValue(key, motionValue(nextValue, { owner: element }));\n        }\n        else if (prevValue !== nextValue) {\n            /**\n             * If this is a flat value that has changed, update the motion value\n             * or create one if it doesn't exist. We only want to do this if we're\n             * not handling the value with our animation state.\n             */\n            if (element.hasValue(key)) {\n                const existingValue = element.getValue(key);\n                if (existingValue.liveStyle === true) {\n                    existingValue.jump(nextValue);\n                }\n                else if (!existingValue.hasAnimated) {\n                    existingValue.set(nextValue);\n                }\n            }\n            else {\n                const latestValue = element.getStaticValue(key);\n                element.addValue(key, motionValue(latestValue !== undefined ? latestValue : nextValue, { owner: element }));\n            }\n        }\n    }\n    // Handle removed values\n    for (const key in prev) {\n        if (next[key] === undefined)\n            element.removeValue(key);\n    }\n    return next;\n}\n\nexport { updateMotionValuesFromProps };\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,SAAS,4BAA4B,OAAO,EAAE,IAAI,EAAE,IAAI;IACpD,IAAK,MAAM,OAAO,KAAM;QACpB,MAAM,YAAY,IAAI,CAAC,IAAI;QAC3B,MAAM,YAAY,IAAI,CAAC,IAAI;QAC3B,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,YAAY;YAC1B;;;aAGC,GACD,QAAQ,QAAQ,CAAC,KAAK;QAC1B,OACK,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,YAAY;YAC/B;;;aAGC,GACD,QAAQ,QAAQ,CAAC,KAAK,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,WAAW;gBAAE,OAAO;YAAQ;QAClE,OACK,IAAI,cAAc,WAAW;YAC9B;;;;aAIC,GACD,IAAI,QAAQ,QAAQ,CAAC,MAAM;gBACvB,MAAM,gBAAgB,QAAQ,QAAQ,CAAC;gBACvC,IAAI,cAAc,SAAS,KAAK,MAAM;oBAClC,cAAc,IAAI,CAAC;gBACvB,OACK,IAAI,CAAC,cAAc,WAAW,EAAE;oBACjC,cAAc,GAAG,CAAC;gBACtB;YACJ,OACK;gBACD,MAAM,cAAc,QAAQ,cAAc,CAAC;gBAC3C,QAAQ,QAAQ,CAAC,KAAK,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,YAAY,cAAc,WAAW;oBAAE,OAAO;gBAAQ;YAC5G;QACJ;IACJ;IACA,wBAAwB;IACxB,IAAK,MAAM,OAAO,KAAM;QACpB,IAAI,IAAI,CAAC,IAAI,KAAK,WACd,QAAQ,WAAW,CAAC;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6857, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/VisualElement.mjs"], "sourcesContent": ["import { KeyframeResolver, time, frame, isMotionValue, cancelFrame, transformProps, motionValue, findValueType, complex, getAnimatableNone } from 'motion-dom';\nimport { warnOnce, isNumericalString, isZeroValueString, SubscriptionManager } from 'motion-utils';\nimport { featureDefinitions } from '../motion/features/definitions.mjs';\nimport { createBox } from '../projection/geometry/models.mjs';\nimport { initPrefersReducedMotion } from '../utils/reduced-motion/index.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from '../utils/reduced-motion/state.mjs';\nimport { visualElementStore } from './store.mjs';\nimport { isControllingVariants, isVariantNode } from './utils/is-controlling-variants.mjs';\nimport { updateMotionValuesFromProps } from './utils/motion-values.mjs';\nimport { resolveVariantFromProps } from './utils/resolve-variants.mjs';\n\nconst propEventHandlers = [\n    \"AnimationStart\",\n    \"AnimationComplete\",\n    \"Update\",\n    \"BeforeLayoutMeasure\",\n    \"LayoutMeasure\",\n    \"LayoutAnimationStart\",\n    \"LayoutAnimationComplete\",\n];\n/**\n * A VisualElement is an imperative abstraction around UI elements such as\n * HTMLElement, SVGElement, Three.Object3D etc.\n */\nclass VisualElement {\n    /**\n     * This method takes React props and returns found MotionValues. For example, HTML\n     * MotionValues will be found within the style prop, whereas for Three.js within attribute arrays.\n     *\n     * This isn't an abstract method as it needs calling in the constructor, but it is\n     * intended to be one.\n     */\n    scrapeMotionValuesFromProps(_props, _prevProps, _visualElement) {\n        return {};\n    }\n    constructor({ parent, props, presenceContext, reducedMotionConfig, blockInitialAnimation, visualState, }, options = {}) {\n        /**\n         * A reference to the current underlying Instance, e.g. a HTMLElement\n         * or Three.Mesh etc.\n         */\n        this.current = null;\n        /**\n         * A set containing references to this VisualElement's children.\n         */\n        this.children = new Set();\n        /**\n         * Determine what role this visual element should take in the variant tree.\n         */\n        this.isVariantNode = false;\n        this.isControllingVariants = false;\n        /**\n         * Decides whether this VisualElement should animate in reduced motion\n         * mode.\n         *\n         * TODO: This is currently set on every individual VisualElement but feels\n         * like it could be set globally.\n         */\n        this.shouldReduceMotion = null;\n        /**\n         * A map of all motion values attached to this visual element. Motion\n         * values are source of truth for any given animated value. A motion\n         * value might be provided externally by the component via props.\n         */\n        this.values = new Map();\n        this.KeyframeResolver = KeyframeResolver;\n        /**\n         * Cleanup functions for active features (hover/tap/exit etc)\n         */\n        this.features = {};\n        /**\n         * A map of every subscription that binds the provided or generated\n         * motion values onChange listeners to this visual element.\n         */\n        this.valueSubscriptions = new Map();\n        /**\n         * A reference to the previously-provided motion values as returned\n         * from scrapeMotionValuesFromProps. We use the keys in here to determine\n         * if any motion values need to be removed after props are updated.\n         */\n        this.prevMotionValues = {};\n        /**\n         * An object containing a SubscriptionManager for each active event.\n         */\n        this.events = {};\n        /**\n         * An object containing an unsubscribe function for each prop event subscription.\n         * For example, every \"Update\" event can have multiple subscribers via\n         * VisualElement.on(), but only one of those can be defined via the onUpdate prop.\n         */\n        this.propEventSubscriptions = {};\n        this.notifyUpdate = () => this.notify(\"Update\", this.latestValues);\n        this.render = () => {\n            if (!this.current)\n                return;\n            this.triggerBuild();\n            this.renderInstance(this.current, this.renderState, this.props.style, this.projection);\n        };\n        this.renderScheduledAt = 0.0;\n        this.scheduleRender = () => {\n            const now = time.now();\n            if (this.renderScheduledAt < now) {\n                this.renderScheduledAt = now;\n                frame.render(this.render, false, true);\n            }\n        };\n        const { latestValues, renderState } = visualState;\n        this.latestValues = latestValues;\n        this.baseTarget = { ...latestValues };\n        this.initialValues = props.initial ? { ...latestValues } : {};\n        this.renderState = renderState;\n        this.parent = parent;\n        this.props = props;\n        this.presenceContext = presenceContext;\n        this.depth = parent ? parent.depth + 1 : 0;\n        this.reducedMotionConfig = reducedMotionConfig;\n        this.options = options;\n        this.blockInitialAnimation = Boolean(blockInitialAnimation);\n        this.isControllingVariants = isControllingVariants(props);\n        this.isVariantNode = isVariantNode(props);\n        if (this.isVariantNode) {\n            this.variantChildren = new Set();\n        }\n        this.manuallyAnimateOnMount = Boolean(parent && parent.current);\n        /**\n         * Any motion values that are provided to the element when created\n         * aren't yet bound to the element, as this would technically be impure.\n         * However, we iterate through the motion values and set them to the\n         * initial values for this component.\n         *\n         * TODO: This is impure and we should look at changing this to run on mount.\n         * Doing so will break some tests but this isn't necessarily a breaking change,\n         * more a reflection of the test.\n         */\n        const { willChange, ...initialMotionValues } = this.scrapeMotionValuesFromProps(props, {}, this);\n        for (const key in initialMotionValues) {\n            const value = initialMotionValues[key];\n            if (latestValues[key] !== undefined && isMotionValue(value)) {\n                value.set(latestValues[key], false);\n            }\n        }\n    }\n    mount(instance) {\n        this.current = instance;\n        visualElementStore.set(instance, this);\n        if (this.projection && !this.projection.instance) {\n            this.projection.mount(instance);\n        }\n        if (this.parent && this.isVariantNode && !this.isControllingVariants) {\n            this.removeFromVariantTree = this.parent.addVariantChild(this);\n        }\n        this.values.forEach((value, key) => this.bindToMotionValue(key, value));\n        if (!hasReducedMotionListener.current) {\n            initPrefersReducedMotion();\n        }\n        this.shouldReduceMotion =\n            this.reducedMotionConfig === \"never\"\n                ? false\n                : this.reducedMotionConfig === \"always\"\n                    ? true\n                    : prefersReducedMotion.current;\n        if (process.env.NODE_ENV !== \"production\") {\n            warnOnce(this.shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n        }\n        if (this.parent)\n            this.parent.children.add(this);\n        this.update(this.props, this.presenceContext);\n    }\n    unmount() {\n        this.projection && this.projection.unmount();\n        cancelFrame(this.notifyUpdate);\n        cancelFrame(this.render);\n        this.valueSubscriptions.forEach((remove) => remove());\n        this.valueSubscriptions.clear();\n        this.removeFromVariantTree && this.removeFromVariantTree();\n        this.parent && this.parent.children.delete(this);\n        for (const key in this.events) {\n            this.events[key].clear();\n        }\n        for (const key in this.features) {\n            const feature = this.features[key];\n            if (feature) {\n                feature.unmount();\n                feature.isMounted = false;\n            }\n        }\n        this.current = null;\n    }\n    bindToMotionValue(key, value) {\n        if (this.valueSubscriptions.has(key)) {\n            this.valueSubscriptions.get(key)();\n        }\n        const valueIsTransform = transformProps.has(key);\n        if (valueIsTransform && this.onBindTransform) {\n            this.onBindTransform();\n        }\n        const removeOnChange = value.on(\"change\", (latestValue) => {\n            this.latestValues[key] = latestValue;\n            this.props.onUpdate && frame.preRender(this.notifyUpdate);\n            if (valueIsTransform && this.projection) {\n                this.projection.isTransformDirty = true;\n            }\n        });\n        const removeOnRenderRequest = value.on(\"renderRequest\", this.scheduleRender);\n        let removeSyncCheck;\n        if (window.MotionCheckAppearSync) {\n            removeSyncCheck = window.MotionCheckAppearSync(this, key, value);\n        }\n        this.valueSubscriptions.set(key, () => {\n            removeOnChange();\n            removeOnRenderRequest();\n            if (removeSyncCheck)\n                removeSyncCheck();\n            if (value.owner)\n                value.stop();\n        });\n    }\n    sortNodePosition(other) {\n        /**\n         * If these nodes aren't even of the same type we can't compare their depth.\n         */\n        if (!this.current ||\n            !this.sortInstanceNodePosition ||\n            this.type !== other.type) {\n            return 0;\n        }\n        return this.sortInstanceNodePosition(this.current, other.current);\n    }\n    updateFeatures() {\n        let key = \"animation\";\n        for (key in featureDefinitions) {\n            const featureDefinition = featureDefinitions[key];\n            if (!featureDefinition)\n                continue;\n            const { isEnabled, Feature: FeatureConstructor } = featureDefinition;\n            /**\n             * If this feature is enabled but not active, make a new instance.\n             */\n            if (!this.features[key] &&\n                FeatureConstructor &&\n                isEnabled(this.props)) {\n                this.features[key] = new FeatureConstructor(this);\n            }\n            /**\n             * If we have a feature, mount or update it.\n             */\n            if (this.features[key]) {\n                const feature = this.features[key];\n                if (feature.isMounted) {\n                    feature.update();\n                }\n                else {\n                    feature.mount();\n                    feature.isMounted = true;\n                }\n            }\n        }\n    }\n    triggerBuild() {\n        this.build(this.renderState, this.latestValues, this.props);\n    }\n    /**\n     * Measure the current viewport box with or without transforms.\n     * Only measures axis-aligned boxes, rotate and skew must be manually\n     * removed with a re-render to work.\n     */\n    measureViewportBox() {\n        return this.current\n            ? this.measureInstanceViewportBox(this.current, this.props)\n            : createBox();\n    }\n    getStaticValue(key) {\n        return this.latestValues[key];\n    }\n    setStaticValue(key, value) {\n        this.latestValues[key] = value;\n    }\n    /**\n     * Update the provided props. Ensure any newly-added motion values are\n     * added to our map, old ones removed, and listeners updated.\n     */\n    update(props, presenceContext) {\n        if (props.transformTemplate || this.props.transformTemplate) {\n            this.scheduleRender();\n        }\n        this.prevProps = this.props;\n        this.props = props;\n        this.prevPresenceContext = this.presenceContext;\n        this.presenceContext = presenceContext;\n        /**\n         * Update prop event handlers ie onAnimationStart, onAnimationComplete\n         */\n        for (let i = 0; i < propEventHandlers.length; i++) {\n            const key = propEventHandlers[i];\n            if (this.propEventSubscriptions[key]) {\n                this.propEventSubscriptions[key]();\n                delete this.propEventSubscriptions[key];\n            }\n            const listenerName = (\"on\" + key);\n            const listener = props[listenerName];\n            if (listener) {\n                this.propEventSubscriptions[key] = this.on(key, listener);\n            }\n        }\n        this.prevMotionValues = updateMotionValuesFromProps(this, this.scrapeMotionValuesFromProps(props, this.prevProps, this), this.prevMotionValues);\n        if (this.handleChildMotionValue) {\n            this.handleChildMotionValue();\n        }\n    }\n    getProps() {\n        return this.props;\n    }\n    /**\n     * Returns the variant definition with a given name.\n     */\n    getVariant(name) {\n        return this.props.variants ? this.props.variants[name] : undefined;\n    }\n    /**\n     * Returns the defined default transition on this component.\n     */\n    getDefaultTransition() {\n        return this.props.transition;\n    }\n    getTransformPagePoint() {\n        return this.props.transformPagePoint;\n    }\n    getClosestVariantNode() {\n        return this.isVariantNode\n            ? this\n            : this.parent\n                ? this.parent.getClosestVariantNode()\n                : undefined;\n    }\n    /**\n     * Add a child visual element to our set of children.\n     */\n    addVariantChild(child) {\n        const closestVariantNode = this.getClosestVariantNode();\n        if (closestVariantNode) {\n            closestVariantNode.variantChildren &&\n                closestVariantNode.variantChildren.add(child);\n            return () => closestVariantNode.variantChildren.delete(child);\n        }\n    }\n    /**\n     * Add a motion value and bind it to this visual element.\n     */\n    addValue(key, value) {\n        // Remove existing value if it exists\n        const existingValue = this.values.get(key);\n        if (value !== existingValue) {\n            if (existingValue)\n                this.removeValue(key);\n            this.bindToMotionValue(key, value);\n            this.values.set(key, value);\n            this.latestValues[key] = value.get();\n        }\n    }\n    /**\n     * Remove a motion value and unbind any active subscriptions.\n     */\n    removeValue(key) {\n        this.values.delete(key);\n        const unsubscribe = this.valueSubscriptions.get(key);\n        if (unsubscribe) {\n            unsubscribe();\n            this.valueSubscriptions.delete(key);\n        }\n        delete this.latestValues[key];\n        this.removeValueFromRenderState(key, this.renderState);\n    }\n    /**\n     * Check whether we have a motion value for this key\n     */\n    hasValue(key) {\n        return this.values.has(key);\n    }\n    getValue(key, defaultValue) {\n        if (this.props.values && this.props.values[key]) {\n            return this.props.values[key];\n        }\n        let value = this.values.get(key);\n        if (value === undefined && defaultValue !== undefined) {\n            value = motionValue(defaultValue === null ? undefined : defaultValue, { owner: this });\n            this.addValue(key, value);\n        }\n        return value;\n    }\n    /**\n     * If we're trying to animate to a previously unencountered value,\n     * we need to check for it in our state and as a last resort read it\n     * directly from the instance (which might have performance implications).\n     */\n    readValue(key, target) {\n        let value = this.latestValues[key] !== undefined || !this.current\n            ? this.latestValues[key]\n            : this.getBaseTargetFromProps(this.props, key) ??\n                this.readValueFromInstance(this.current, key, this.options);\n        if (value !== undefined && value !== null) {\n            if (typeof value === \"string\" &&\n                (isNumericalString(value) || isZeroValueString(value))) {\n                // If this is a number read as a string, ie \"0\" or \"200\", convert it to a number\n                value = parseFloat(value);\n            }\n            else if (!findValueType(value) && complex.test(target)) {\n                value = getAnimatableNone(key, target);\n            }\n            this.setBaseTarget(key, isMotionValue(value) ? value.get() : value);\n        }\n        return isMotionValue(value) ? value.get() : value;\n    }\n    /**\n     * Set the base target to later animate back to. This is currently\n     * only hydrated on creation and when we first read a value.\n     */\n    setBaseTarget(key, value) {\n        this.baseTarget[key] = value;\n    }\n    /**\n     * Find the base target for a value thats been removed from all animation\n     * props.\n     */\n    getBaseTarget(key) {\n        const { initial } = this.props;\n        let valueFromInitial;\n        if (typeof initial === \"string\" || typeof initial === \"object\") {\n            const variant = resolveVariantFromProps(this.props, initial, this.presenceContext?.custom);\n            if (variant) {\n                valueFromInitial = variant[key];\n            }\n        }\n        /**\n         * If this value still exists in the current initial variant, read that.\n         */\n        if (initial && valueFromInitial !== undefined) {\n            return valueFromInitial;\n        }\n        /**\n         * Alternatively, if this VisualElement config has defined a getBaseTarget\n         * so we can read the value from an alternative source, try that.\n         */\n        const target = this.getBaseTargetFromProps(this.props, key);\n        if (target !== undefined && !isMotionValue(target))\n            return target;\n        /**\n         * If the value was initially defined on initial, but it doesn't any more,\n         * return undefined. Otherwise return the value as initially read from the DOM.\n         */\n        return this.initialValues[key] !== undefined &&\n            valueFromInitial === undefined\n            ? undefined\n            : this.baseTarget[key];\n    }\n    on(eventName, callback) {\n        if (!this.events[eventName]) {\n            this.events[eventName] = new SubscriptionManager();\n        }\n        return this.events[eventName].add(callback);\n    }\n    notify(eventName, ...args) {\n        if (this.events[eventName]) {\n            this.events[eventName].notify(...args);\n        }\n    }\n}\n\nexport { VisualElement };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,MAAM,oBAAoB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD;;;CAGC,GACD,MAAM;IACF;;;;;;KAMC,GACD,4BAA4B,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE;QAC5D,OAAO,CAAC;IACZ;IACA,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,WAAW,EAAG,EAAE,UAAU,CAAC,CAAC,CAAE;QACpH;;;SAGC,GACD,IAAI,CAAC,OAAO,GAAG;QACf;;SAEC,GACD,IAAI,CAAC,QAAQ,GAAG,IAAI;QACpB;;SAEC,GACD,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,qBAAqB,GAAG;QAC7B;;;;;;SAMC,GACD,IAAI,CAAC,kBAAkB,GAAG;QAC1B;;;;SAIC,GACD,IAAI,CAAC,MAAM,GAAG,IAAI;QAClB,IAAI,CAAC,gBAAgB,GAAG,2LAAA,CAAA,mBAAgB;QACxC;;SAEC,GACD,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB;;;SAGC,GACD,IAAI,CAAC,kBAAkB,GAAG,IAAI;QAC9B;;;;SAIC,GACD,IAAI,CAAC,gBAAgB,GAAG,CAAC;QACzB;;SAEC,GACD,IAAI,CAAC,MAAM,GAAG,CAAC;QACf;;;;SAIC,GACD,IAAI,CAAC,sBAAsB,GAAG,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,IAAM,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,YAAY;QACjE,IAAI,CAAC,MAAM,GAAG;YACV,IAAI,CAAC,IAAI,CAAC,OAAO,EACb;YACJ,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU;QACzF;QACA,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,cAAc,GAAG;YAClB,MAAM,MAAM,yKAAA,CAAA,OAAI,CAAC,GAAG;YACpB,IAAI,IAAI,CAAC,iBAAiB,GAAG,KAAK;gBAC9B,IAAI,CAAC,iBAAiB,GAAG;gBACzB,kKAAA,CAAA,QAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO;YACrC;QACJ;QACA,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG;QACtC,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,UAAU,GAAG;YAAE,GAAG,YAAY;QAAC;QACpC,IAAI,CAAC,aAAa,GAAG,MAAM,OAAO,GAAG;YAAE,GAAG,YAAY;QAAC,IAAI,CAAC;QAC5D,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,KAAK,GAAG,SAAS,OAAO,KAAK,GAAG,IAAI;QACzC,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,qBAAqB,GAAG,QAAQ;QACrC,IAAI,CAAC,qBAAqB,GAAG,CAAA,GAAA,mMAAA,CAAA,wBAAqB,AAAD,EAAE;QACnD,IAAI,CAAC,aAAa,GAAG,CAAA,GAAA,mMAAA,CAAA,gBAAa,AAAD,EAAE;QACnC,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,eAAe,GAAG,IAAI;QAC/B;QACA,IAAI,CAAC,sBAAsB,GAAG,QAAQ,UAAU,OAAO,OAAO;QAC9D;;;;;;;;;SASC,GACD,MAAM,EAAE,UAAU,EAAE,GAAG,qBAAqB,GAAG,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,GAAG,IAAI;QAC/F,IAAK,MAAM,OAAO,oBAAqB;YACnC,MAAM,QAAQ,mBAAmB,CAAC,IAAI;YACtC,IAAI,YAAY,CAAC,IAAI,KAAK,aAAa,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBACzD,MAAM,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;YACjC;QACJ;IACJ;IACA,MAAM,QAAQ,EAAE;QACZ,IAAI,CAAC,OAAO,GAAG;QACf,kKAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,UAAU,IAAI;QACrC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;YAC9C,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;QAC1B;QACA,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAClE,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI;QACjE;QACA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,MAAQ,IAAI,CAAC,iBAAiB,CAAC,KAAK;QAChE,IAAI,CAAC,sLAAA,CAAA,2BAAwB,CAAC,OAAO,EAAE;YACnC,CAAA,GAAA,sLAAA,CAAA,2BAAwB,AAAD;QAC3B;QACA,IAAI,CAAC,kBAAkB,GACnB,IAAI,CAAC,mBAAmB,KAAK,UACvB,QACA,IAAI,CAAC,mBAAmB,KAAK,WACzB,OACA,sLAAA,CAAA,uBAAoB,CAAC,OAAO;QAC1C,wCAA2C;YACvC,CAAA,GAAA,8JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,kBAAkB,KAAK,MAAM;QAC/C;QACA,IAAI,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI;QACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe;IAChD;IACA,UAAU;QACN,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO;QAC1C,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,YAAY;QAC7B,CAAA,GAAA,kKAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,MAAM;QACvB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,SAAW;QAC5C,IAAI,CAAC,kBAAkB,CAAC,KAAK;QAC7B,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB;QACxD,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI;QAC/C,IAAK,MAAM,OAAO,IAAI,CAAC,MAAM,CAAE;YAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK;QAC1B;QACA,IAAK,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAE;YAC7B,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI;YAClC,IAAI,SAAS;gBACT,QAAQ,OAAO;gBACf,QAAQ,SAAS,GAAG;YACxB;QACJ;QACA,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,kBAAkB,GAAG,EAAE,KAAK,EAAE;QAC1B,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM;YAClC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;QAChC;QACA,MAAM,mBAAmB,oLAAA,CAAA,iBAAc,CAAC,GAAG,CAAC;QAC5C,IAAI,oBAAoB,IAAI,CAAC,eAAe,EAAE;YAC1C,IAAI,CAAC,eAAe;QACxB;QACA,MAAM,iBAAiB,MAAM,EAAE,CAAC,UAAU,CAAC;YACvC,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG;YACzB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,kKAAA,CAAA,QAAK,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY;YACxD,IAAI,oBAAoB,IAAI,CAAC,UAAU,EAAE;gBACrC,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG;YACvC;QACJ;QACA,MAAM,wBAAwB,MAAM,EAAE,CAAC,iBAAiB,IAAI,CAAC,cAAc;QAC3E,IAAI;QACJ,IAAI,OAAO,qBAAqB,EAAE;YAC9B,kBAAkB,OAAO,qBAAqB,CAAC,IAAI,EAAE,KAAK;QAC9D;QACA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK;YAC7B;YACA;YACA,IAAI,iBACA;YACJ,IAAI,MAAM,KAAK,EACX,MAAM,IAAI;QAClB;IACJ;IACA,iBAAiB,KAAK,EAAE;QACpB;;SAEC,GACD,IAAI,CAAC,IAAI,CAAC,OAAO,IACb,CAAC,IAAI,CAAC,wBAAwB,IAC9B,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,EAAE;YAC1B,OAAO;QACX;QACA,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,OAAO;IACpE;IACA,iBAAiB;QACb,IAAI,MAAM;QACV,IAAK,OAAO,oLAAA,CAAA,qBAAkB,CAAE;YAC5B,MAAM,oBAAoB,oLAAA,CAAA,qBAAkB,CAAC,IAAI;YACjD,IAAI,CAAC,mBACD;YACJ,MAAM,EAAE,SAAS,EAAE,SAAS,kBAAkB,EAAE,GAAG;YACnD;;aAEC,GACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IACnB,sBACA,UAAU,IAAI,CAAC,KAAK,GAAG;gBACvB,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,mBAAmB,IAAI;YACpD;YACA;;aAEC,GACD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;gBACpB,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI;gBAClC,IAAI,QAAQ,SAAS,EAAE;oBACnB,QAAQ,MAAM;gBAClB,OACK;oBACD,QAAQ,KAAK;oBACb,QAAQ,SAAS,GAAG;gBACxB;YACJ;QACJ;IACJ;IACA,eAAe;QACX,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK;IAC9D;IACA;;;;KAIC,GACD,qBAAqB;QACjB,OAAO,IAAI,CAAC,OAAO,GACb,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,IACxD,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD;IAClB;IACA,eAAe,GAAG,EAAE;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI;IACjC;IACA,eAAe,GAAG,EAAE,KAAK,EAAE;QACvB,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG;IAC7B;IACA;;;KAGC,GACD,OAAO,KAAK,EAAE,eAAe,EAAE;QAC3B,IAAI,MAAM,iBAAiB,IAAI,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE;YACzD,IAAI,CAAC,cAAc;QACvB;QACA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK;QAC3B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,eAAe;QAC/C,IAAI,CAAC,eAAe,GAAG;QACvB;;SAEC,GACD,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,MAAM,EAAE,IAAK;YAC/C,MAAM,MAAM,iBAAiB,CAAC,EAAE;YAChC,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE;gBAClC,IAAI,CAAC,sBAAsB,CAAC,IAAI;gBAChC,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI;YAC3C;YACA,MAAM,eAAgB,OAAO;YAC7B,MAAM,WAAW,KAAK,CAAC,aAAa;YACpC,IAAI,UAAU;gBACV,IAAI,CAAC,sBAAsB,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK;YACpD;QACJ;QACA,IAAI,CAAC,gBAAgB,GAAG,CAAA,GAAA,sLAAA,CAAA,8BAA2B,AAAD,EAAE,IAAI,EAAE,IAAI,CAAC,2BAA2B,CAAC,OAAO,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG,IAAI,CAAC,gBAAgB;QAC9I,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB;QAC/B;IACJ;IACA,WAAW;QACP,OAAO,IAAI,CAAC,KAAK;IACrB;IACA;;KAEC,GACD,WAAW,IAAI,EAAE;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG;IAC7D;IACA;;KAEC,GACD,uBAAuB;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU;IAChC;IACA,wBAAwB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB;IACxC;IACA,wBAAwB;QACpB,OAAO,IAAI,CAAC,aAAa,GACnB,IAAI,GACJ,IAAI,CAAC,MAAM,GACP,IAAI,CAAC,MAAM,CAAC,qBAAqB,KACjC;IACd;IACA;;KAEC,GACD,gBAAgB,KAAK,EAAE;QACnB,MAAM,qBAAqB,IAAI,CAAC,qBAAqB;QACrD,IAAI,oBAAoB;YACpB,mBAAmB,eAAe,IAC9B,mBAAmB,eAAe,CAAC,GAAG,CAAC;YAC3C,OAAO,IAAM,mBAAmB,eAAe,CAAC,MAAM,CAAC;QAC3D;IACJ;IACA;;KAEC,GACD,SAAS,GAAG,EAAE,KAAK,EAAE;QACjB,qCAAqC;QACrC,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QACtC,IAAI,UAAU,eAAe;YACzB,IAAI,eACA,IAAI,CAAC,WAAW,CAAC;YACrB,IAAI,CAAC,iBAAiB,CAAC,KAAK;YAC5B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK;YACrB,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,MAAM,GAAG;QACtC;IACJ;IACA;;KAEC,GACD,YAAY,GAAG,EAAE;QACb,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QACnB,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;QAChD,IAAI,aAAa;YACb;YACA,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;QACnC;QACA,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI;QAC7B,IAAI,CAAC,0BAA0B,CAAC,KAAK,IAAI,CAAC,WAAW;IACzD;IACA;;KAEC,GACD,SAAS,GAAG,EAAE;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;IAC3B;IACA,SAAS,GAAG,EAAE,YAAY,EAAE;QACxB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE;YAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;QACjC;QACA,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QAC5B,IAAI,UAAU,aAAa,iBAAiB,WAAW;YACnD,QAAQ,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,iBAAiB,OAAO,YAAY,cAAc;gBAAE,OAAO,IAAI;YAAC;YACpF,IAAI,CAAC,QAAQ,CAAC,KAAK;QACvB;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,UAAU,GAAG,EAAE,MAAM,EAAE;QACnB,IAAI,QAAQ,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,CAAC,OAAO,GAC3D,IAAI,CAAC,YAAY,CAAC,IAAI,GACtB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,EAAE,QACtC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,OAAO;QAClE,IAAI,UAAU,aAAa,UAAU,MAAM;YACvC,IAAI,OAAO,UAAU,YACjB,CAAC,CAAA,GAAA,2KAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,CAAA,GAAA,+KAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,GAAG;gBACxD,gFAAgF;gBAChF,QAAQ,WAAW;YACvB,OACK,IAAI,CAAC,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,kLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,SAAS;gBACpD,QAAQ,CAAA,GAAA,6LAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK;YACnC;YACA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,MAAM,GAAG,KAAK;QACjE;QACA,OAAO,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,MAAM,GAAG,KAAK;IAChD;IACA;;;KAGC,GACD,cAAc,GAAG,EAAE,KAAK,EAAE;QACtB,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG;IAC3B;IACA;;;KAGC,GACD,cAAc,GAAG,EAAE;QACf,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK;QAC9B,IAAI;QACJ,IAAI,OAAO,YAAY,YAAY,OAAO,YAAY,UAAU;YAC5D,MAAM,UAAU,CAAA,GAAA,yLAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,eAAe,EAAE;YACnF,IAAI,SAAS;gBACT,mBAAmB,OAAO,CAAC,IAAI;YACnC;QACJ;QACA;;SAEC,GACD,IAAI,WAAW,qBAAqB,WAAW;YAC3C,OAAO;QACX;QACA;;;SAGC,GACD,MAAM,SAAS,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,KAAK,EAAE;QACvD,IAAI,WAAW,aAAa,CAAC,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,SACvC,OAAO;QACX;;;SAGC,GACD,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,aAC/B,qBAAqB,YACnB,YACA,IAAI,CAAC,UAAU,CAAC,IAAI;IAC9B;IACA,GAAG,SAAS,EAAE,QAAQ,EAAE;QACpB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,yKAAA,CAAA,sBAAmB;QACpD;QACA,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC;IACtC;IACA,OAAO,SAAS,EAAE,GAAG,IAAI,EAAE;QACvB,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACxB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI;QACrC;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7295, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs"], "sourcesContent": ["import { DOMKeyframesResolver, isMotionValue } from 'motion-dom';\nimport { VisualElement } from '../VisualElement.mjs';\n\nclass DOMVisualElement extends VisualElement {\n    constructor() {\n        super(...arguments);\n        this.KeyframeResolver = DOMKeyframesResolver;\n    }\n    sortInstanceNodePosition(a, b) {\n        /**\n         * compareDocumentPosition returns a bitmask, by using the bitwise &\n         * we're returning true if 2 in that bitmask is set to true. 2 is set\n         * to true if b preceeds a.\n         */\n        return a.compareDocumentPosition(b) & 2 ? 1 : -1;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props.style\n            ? props.style[key]\n            : undefined;\n    }\n    removeValueFromRenderState(key, { vars, style }) {\n        delete vars[key];\n        delete style[key];\n    }\n    handleChildMotionValue() {\n        if (this.childSubscription) {\n            this.childSubscription();\n            delete this.childSubscription;\n        }\n        const { children } = this.props;\n        if (isMotionValue(children)) {\n            this.childSubscription = children.on(\"change\", (latest) => {\n                if (this.current) {\n                    this.current.textContent = `${latest}`;\n                }\n            });\n        }\n    }\n}\n\nexport { DOMVisualElement };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA,MAAM,yBAAyB,0KAAA,CAAA,gBAAa;IACxC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,gBAAgB,GAAG,8LAAA,CAAA,uBAAoB;IAChD;IACA,yBAAyB,CAAC,EAAE,CAAC,EAAE;QAC3B;;;;SAIC,GACD,OAAO,EAAE,uBAAuB,CAAC,KAAK,IAAI,IAAI,CAAC;IACnD;IACA,uBAAuB,KAAK,EAAE,GAAG,EAAE;QAC/B,OAAO,MAAM,KAAK,GACZ,MAAM,KAAK,CAAC,IAAI,GAChB;IACV;IACA,2BAA2B,GAAG,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QAC7C,OAAO,IAAI,CAAC,IAAI;QAChB,OAAO,KAAK,CAAC,IAAI;IACrB;IACA,yBAAyB;QACrB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB;YACtB,OAAO,IAAI,CAAC,iBAAiB;QACjC;QACA,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,KAAK;QAC/B,IAAI,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;YACzB,IAAI,CAAC,iBAAiB,GAAG,SAAS,EAAE,CAAC,UAAU,CAAC;gBAC5C,IAAI,IAAI,CAAC,OAAO,EAAE;oBACd,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,GAAG,QAAQ;gBAC1C;YACJ;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7344, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/html/utils/render.mjs"], "sourcesContent": ["function renderHTML(element, { style, vars }, styleProp, projection) {\n    const elementStyle = element.style;\n    let key;\n    for (key in style) {\n        // CSSStyleDeclaration has [index: number]: string; in the types, so we use that as key type.\n        elementStyle[key] = style[key];\n    }\n    // Write projection styles directly to element style\n    projection?.applyProjectionStyles(elementStyle, styleProp);\n    for (key in vars) {\n        // Loop over any CSS variables and assign those.\n        // They can only be assigned using `setProperty`.\n        elementStyle.setProperty(key, vars[key]);\n    }\n}\n\nexport { renderHTML };\n"], "names": [], "mappings": ";;;AAAA,SAAS,WAAW,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,UAAU;IAC/D,MAAM,eAAe,QAAQ,KAAK;IAClC,IAAI;IACJ,IAAK,OAAO,MAAO;QACf,6FAA6F;QAC7F,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;IAClC;IACA,oDAAoD;IACpD,YAAY,sBAAsB,cAAc;IAChD,IAAK,OAAO,KAAM;QACd,gDAAgD;QAChD,iDAAiD;QACjD,aAAa,WAAW,CAAC,KAAK,IAAI,CAAC,IAAI;IAC3C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7369, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs"], "sourcesContent": ["import { transformProps, defaultTransformValue, readTransformValue, isCSSVariableName } from 'motion-dom';\nimport { measureViewportBox } from '../../projection/utils/measure.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { renderHTML } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nfunction getComputedStyle(element) {\n    return window.getComputedStyle(element);\n}\nclass HTMLVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"html\";\n        this.renderInstance = renderHTML;\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            return this.projection?.isProjecting\n                ? defaultTransformValue(key)\n                : readTransformValue(instance, key);\n        }\n        else {\n            const computedStyle = getComputedStyle(instance);\n            const value = (isCSSVariableName(key)\n                ? computedStyle.getPropertyValue(key)\n                : computedStyle[key]) || 0;\n            return typeof value === \"string\" ? value.trim() : value;\n        }\n    }\n    measureInstanceViewportBox(instance, { transformPagePoint }) {\n        return measureViewportBox(instance, transformPagePoint);\n    }\n    build(renderState, latestValues, props) {\n        buildHTMLStyles(renderState, latestValues, props.transformTemplate);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n    }\n}\n\nexport { HTMLVisualElement, getComputedStyle };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,SAAS,iBAAiB,OAAO;IAC7B,OAAO,OAAO,gBAAgB,CAAC;AACnC;AACA,MAAM,0BAA0B,oLAAA,CAAA,mBAAgB;IAC5C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,cAAc,GAAG,oLAAA,CAAA,aAAU;IACpC;IACA,sBAAsB,QAAQ,EAAE,GAAG,EAAE;QACjC,IAAI,oLAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,MAAM;YACzB,OAAO,IAAI,CAAC,UAAU,EAAE,eAClB,CAAA,GAAA,mLAAA,CAAA,wBAAqB,AAAD,EAAE,OACtB,CAAA,GAAA,mLAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU;QACvC,OACK;YACD,MAAM,gBAAgB,iBAAiB;YACvC,MAAM,QAAQ,CAAC,CAAA,GAAA,2LAAA,CAAA,oBAAiB,AAAD,EAAE,OAC3B,cAAc,gBAAgB,CAAC,OAC/B,aAAa,CAAC,IAAI,KAAK;YAC7B,OAAO,OAAO,UAAU,WAAW,MAAM,IAAI,KAAK;QACtD;IACJ;IACA,2BAA2B,QAAQ,EAAE,EAAE,kBAAkB,EAAE,EAAE;QACzD,OAAO,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU;IACxC;IACA,MAAM,WAAW,EAAE,YAAY,EAAE,KAAK,EAAE;QACpC,CAAA,GAAA,6LAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,cAAc,MAAM,iBAAiB;IACtE;IACA,4BAA4B,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE;QACzD,OAAO,CAAA,GAAA,wMAAA,CAAA,8BAA2B,AAAD,EAAE,OAAO,WAAW;IACzD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7422, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs"], "sourcesContent": ["/**\n * A set of attribute names that are always read/written as camel case.\n */\nconst camelCaseAttributes = new Set([\n    \"baseFrequency\",\n    \"diffuseConstant\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerWidth\",\n    \"numOctaves\",\n    \"targetX\",\n    \"targetY\",\n    \"surfaceScale\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"stdDeviation\",\n    \"tableValues\",\n    \"viewBox\",\n    \"gradientTransform\",\n    \"pathLength\",\n    \"startOffset\",\n    \"textLength\",\n    \"lengthAdjust\",\n]);\n\nexport { camelCaseAttributes };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,sBAAsB,IAAI,IAAI;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7459, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/svg/utils/render.mjs"], "sourcesContent": ["import { camelToDash } from '../../dom/utils/camel-to-dash.mjs';\nimport { renderHTML } from '../../html/utils/render.mjs';\nimport { camelCaseAttributes } from './camel-case-attrs.mjs';\n\nfunction renderSVG(element, renderState, _styleProp, projection) {\n    renderHTML(element, renderState, undefined, projection);\n    for (const key in renderState.attrs) {\n        element.setAttribute(!camelCaseAttributes.has(key) ? camelToDash(key) : key, renderState.attrs[key]);\n    }\n}\n\nexport { renderSVG };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,SAAS,UAAU,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU;IAC3D,CAAA,GAAA,oLAAA,CAAA,aAAU,AAAD,EAAE,SAAS,aAAa,WAAW;IAC5C,IAAK,MAAM,OAAO,YAAY,KAAK,CAAE;QACjC,QAAQ,YAAY,CAAC,CAAC,mMAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,OAAO,CAAA,GAAA,gMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;IACvG;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7481, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs"], "sourcesContent": ["import { transformProps, getDefaultValueType } from 'motion-dom';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { camelToDash } from '../dom/utils/camel-to-dash.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { camelCaseAttributes } from './utils/camel-case-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nclass SVGVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"svg\";\n        this.isSVGTag = false;\n        this.measureInstanceViewportBox = createBox;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props[key];\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            const defaultType = getDefaultValueType(key);\n            return defaultType ? defaultType.default || 0 : 0;\n        }\n        key = !camelCaseAttributes.has(key) ? camelToDash(key) : key;\n        return instance.getAttribute(key);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n    }\n    build(renderState, latestValues, props) {\n        buildSVGAttrs(renderState, latestValues, this.isSVGTag, props.transformTemplate, props.style);\n    }\n    renderInstance(instance, renderState, styleProp, projection) {\n        renderSVG(instance, renderState, styleProp, projection);\n    }\n    mount(instance) {\n        this.isSVGTag = isSVGTag(instance.tagName);\n        super.mount(instance);\n    }\n}\n\nexport { SVGVisualElement };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,MAAM,yBAAyB,oLAAA,CAAA,mBAAgB;IAC3C,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,0BAA0B,GAAG,mLAAA,CAAA,YAAS;IAC/C;IACA,uBAAuB,KAAK,EAAE,GAAG,EAAE;QAC/B,OAAO,KAAK,CAAC,IAAI;IACrB;IACA,sBAAsB,QAAQ,EAAE,GAAG,EAAE;QACjC,IAAI,oLAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,MAAM;YACzB,MAAM,cAAc,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAE;YACxC,OAAO,cAAc,YAAY,OAAO,IAAI,IAAI;QACpD;QACA,MAAM,CAAC,mMAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,OAAO,CAAA,GAAA,gMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzD,OAAO,SAAS,YAAY,CAAC;IACjC;IACA,4BAA4B,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE;QACzD,OAAO,CAAA,GAAA,uMAAA,CAAA,8BAA2B,AAAD,EAAE,OAAO,WAAW;IACzD;IACA,MAAM,WAAW,EAAE,YAAY,EAAE,KAAK,EAAE;QACpC,CAAA,GAAA,2LAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,cAAc,IAAI,CAAC,QAAQ,EAAE,MAAM,iBAAiB,EAAE,MAAM,KAAK;IAChG;IACA,eAAe,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE;QACzD,CAAA,GAAA,mLAAA,CAAA,YAAS,AAAD,EAAE,UAAU,aAAa,WAAW;IAChD;IACA,MAAM,QAAQ,EAAE;QACZ,IAAI,CAAC,QAAQ,GAAG,CAAA,GAAA,6LAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,OAAO;QACzC,KAAK,CAAC,MAAM;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7542, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs"], "sourcesContent": ["import { Fragment } from 'react';\nimport { HTMLVisualElement } from '../html/HTMLVisualElement.mjs';\nimport { SVGVisualElement } from '../svg/SVGVisualElement.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\n\nconst createDomVisualElement = (Component, options) => {\n    return isSVGComponent(Component)\n        ? new SVGVisualElement(options)\n        : new HTMLVisualElement(options, {\n            allowProjection: Component !== Fragment,\n        });\n};\n\nexport { createDomVisualElement };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,yBAAyB,CAAC,WAAW;IACvC,OAAO,CAAA,GAAA,mMAAA,CAAA,iBAAc,AAAD,EAAE,aAChB,IAAI,oLAAA,CAAA,mBAAgB,CAAC,WACrB,IAAI,sLAAA,CAAA,oBAAiB,CAAC,SAAS;QAC7B,iBAAiB,cAAc,qMAAA,CAAA,WAAQ;IAC3C;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7565, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/components/motion/create.mjs"], "sourcesContent": ["import { animations } from '../../../motion/features/animations.mjs';\nimport { drag } from '../../../motion/features/drag.mjs';\nimport { gestureAnimations } from '../../../motion/features/gestures.mjs';\nimport { layout } from '../../../motion/features/layout.mjs';\nimport { createMotionComponentFactory } from '../create-factory.mjs';\nimport { createDomVisualElement } from '../../dom/create-visual-element.mjs';\n\nconst createMotionComponent = /*@__PURE__*/ createMotionComponentFactory({\n    ...animations,\n    ...gestureAnimations,\n    ...drag,\n    ...layout,\n}, createDomVisualElement);\n\nexport { createMotionComponent };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,wBAAwB,WAAW,GAAG,CAAA,GAAA,4LAAA,CAAA,+BAA4B,AAAD,EAAE;IACrE,GAAG,mLAAA,CAAA,aAAU;IACb,GAAG,iLAAA,CAAA,oBAAiB;IACpB,GAAG,6KAAA,CAAA,OAAI;IACP,GAAG,+KAAA,CAAA,SAAM;AACb,GAAG,+LAAA,CAAA,yBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7593, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs"], "sourcesContent": ["import { createDOMMotionComponentProxy } from '../create-proxy.mjs';\nimport { createMotionComponent } from './create.mjs';\n\nconst motion = /*@__PURE__*/ createDOMMotionComponentProxy(createMotionComponent);\n\nexport { motion };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,SAAS,WAAW,GAAG,CAAA,GAAA,0LAAA,CAAA,gCAA6B,AAAD,EAAE,2LAAA,CAAA,wBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7608, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport { isHTMLElement } from 'motion-dom';\nimport * as React from 'react';\nimport { useId, useRef, useContext, useInsertionEffect } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends React.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const parent = element.offsetParent;\n            const parentWidth = isHTMLElement(parent)\n                ? parent.offsetWidth || 0\n                : 0;\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n            size.right = parentWidth - size.width - size.left;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */\n    componentDidUpdate() { }\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild({ children, isPresent, anchorX, root }) {\n    const id = useId();\n    const ref = useRef(null);\n    const size = useRef({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        right: 0,\n    });\n    const { nonce } = useContext(MotionConfigContext);\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */\n    useInsertionEffect(() => {\n        const { width, height, top, left, right } = size.current;\n        if (isPresent || !ref.current || !width || !height)\n            return;\n        const x = anchorX === \"left\" ? `left: ${left}` : `right: ${right}`;\n        ref.current.dataset.motionPopId = id;\n        const style = document.createElement(\"style\");\n        if (nonce)\n            style.nonce = nonce;\n        const parent = root ?? document.head;\n        parent.appendChild(style);\n        if (style.sheet) {\n            style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            ${x}px !important;\n            top: ${top}px !important;\n          }\n        `);\n        }\n        return () => {\n            parent.removeChild(style);\n            if (parent.contains(style)) {\n                parent.removeChild(style);\n            }\n        };\n    }, [isPresent]);\n    return (jsx(PopChildMeasure, { isPresent: isPresent, childRef: ref, sizeRef: size, children: React.cloneElement(children, { ref }) }));\n}\n\nexport { PopChild };\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AAEA;AALA;;;;;;AAOA;;;CAGC,GACD,MAAM,wBAAwB,qMAAA,CAAA,YAAe;IACzC,wBAAwB,SAAS,EAAE;QAC/B,MAAM,UAAU,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO;QAC3C,IAAI,WAAW,UAAU,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACzD,MAAM,SAAS,QAAQ,YAAY;YACnC,MAAM,cAAc,CAAA,GAAA,8KAAA,CAAA,gBAAa,AAAD,EAAE,UAC5B,OAAO,WAAW,IAAI,IACtB;YACN,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;YACvC,KAAK,MAAM,GAAG,QAAQ,YAAY,IAAI;YACtC,KAAK,KAAK,GAAG,QAAQ,WAAW,IAAI;YACpC,KAAK,GAAG,GAAG,QAAQ,SAAS;YAC5B,KAAK,IAAI,GAAG,QAAQ,UAAU;YAC9B,KAAK,KAAK,GAAG,cAAc,KAAK,KAAK,GAAG,KAAK,IAAI;QACrD;QACA,OAAO;IACX;IACA;;KAEC,GACD,qBAAqB,CAAE;IACvB,SAAS;QACL,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC9B;AACJ;AACA,SAAS,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE;IACpD,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACf,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAChB,OAAO;QACP,QAAQ;QACR,KAAK;QACL,MAAM;QACN,OAAO;IACX;IACA,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,iLAAA,CAAA,sBAAmB;IAChD;;;;;;;;KAQC,GACD,CAAA,GAAA,qMAAA,CAAA,qBAAkB,AAAD,EAAE;QACf,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,OAAO;QACxD,IAAI,aAAa,CAAC,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,QACxC;QACJ,MAAM,IAAI,YAAY,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,OAAO,EAAE,OAAO;QAClE,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,GAAG;QAClC,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,IAAI,OACA,MAAM,KAAK,GAAG;QAClB,MAAM,SAAS,QAAQ,SAAS,IAAI;QACpC,OAAO,WAAW,CAAC;QACnB,IAAI,MAAM,KAAK,EAAE;YACb,MAAM,KAAK,CAAC,UAAU,CAAC,CAAC;+BACL,EAAE,GAAG;;mBAEjB,EAAE,MAAM;oBACP,EAAE,OAAO;YACjB,EAAE,EAAE;iBACC,EAAE,IAAI;;QAEf,CAAC;QACD;QACA,OAAO;YACH,OAAO,WAAW,CAAC;YACnB,IAAI,OAAO,QAAQ,CAAC,QAAQ;gBACxB,OAAO,WAAW,CAAC;YACvB;QACJ;IACJ,GAAG;QAAC;KAAU;IACd,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,iBAAiB;QAAE,WAAW;QAAW,UAAU;QAAK,SAAS;QAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,eAAkB,AAAD,EAAE,UAAU;YAAE;QAAI;IAAG;AACvI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7710, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx } from 'react/jsx-runtime';\nimport * as React from 'react';\nimport { useId, useMemo } from 'react';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { PopChild } from './PopChild.mjs';\n\nconst PresenceChild = ({ children, initial, isPresent, onExitComplete, custom, presenceAffectsLayout, mode, anchorX, root }) => {\n    const presenceChildren = useConstant(newChildrenMap);\n    const id = useId();\n    let isReusedContext = true;\n    let context = useMemo(() => {\n        isReusedContext = false;\n        return {\n            id,\n            initial,\n            isPresent,\n            custom,\n            onExitComplete: (childId) => {\n                presenceChildren.set(childId, true);\n                for (const isComplete of presenceChildren.values()) {\n                    if (!isComplete)\n                        return; // can stop searching when any is incomplete\n                }\n                onExitComplete && onExitComplete();\n            },\n            register: (childId) => {\n                presenceChildren.set(childId, false);\n                return () => presenceChildren.delete(childId);\n            },\n        };\n    }, [isPresent, presenceChildren, onExitComplete]);\n    /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */\n    if (presenceAffectsLayout && isReusedContext) {\n        context = { ...context };\n    }\n    useMemo(() => {\n        presenceChildren.forEach((_, key) => presenceChildren.set(key, false));\n    }, [isPresent]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */\n    React.useEffect(() => {\n        !isPresent &&\n            !presenceChildren.size &&\n            onExitComplete &&\n            onExitComplete();\n    }, [isPresent]);\n    if (mode === \"popLayout\") {\n        children = (jsx(PopChild, { isPresent: isPresent, anchorX: anchorX, root: root, children: children }));\n    }\n    return (jsx(PresenceContext.Provider, { value: context, children: children }));\n};\nfunction newChildrenMap() {\n    return new Map();\n}\n\nexport { PresenceChild };\n"], "names": [], "mappings": ";;;AACA;AACA;AAEA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,qBAAqB,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;IACvH,MAAM,mBAAmB,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;IACrC,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAK,AAAD;IACf,IAAI,kBAAkB;IACtB,IAAI,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAClB,kBAAkB;QAClB,OAAO;YACH;YACA;YACA;YACA;YACA,gBAAgB,CAAC;gBACb,iBAAiB,GAAG,CAAC,SAAS;gBAC9B,KAAK,MAAM,cAAc,iBAAiB,MAAM,GAAI;oBAChD,IAAI,CAAC,YACD,QAAQ,4CAA4C;gBAC5D;gBACA,kBAAkB;YACtB;YACA,UAAU,CAAC;gBACP,iBAAiB,GAAG,CAAC,SAAS;gBAC9B,OAAO,IAAM,iBAAiB,MAAM,CAAC;YACzC;QACJ;IACJ,GAAG;QAAC;QAAW;QAAkB;KAAe;IAChD;;;;KAIC,GACD,IAAI,yBAAyB,iBAAiB;QAC1C,UAAU;YAAE,GAAG,OAAO;QAAC;IAC3B;IACA,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACJ,iBAAiB,OAAO,CAAC,CAAC,GAAG,MAAQ,iBAAiB,GAAG,CAAC,KAAK;IACnE,GAAG;QAAC;KAAU;IACd;;;KAGC,GACD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACZ,CAAC,aACG,CAAC,iBAAiB,IAAI,IACtB,kBACA;IACR,GAAG;QAAC;KAAU;IACd,IAAI,SAAS,aAAa;QACtB,WAAY,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,4LAAA,CAAA,WAAQ,EAAE;YAAE,WAAW;YAAW,SAAS;YAAS,MAAM;YAAM,UAAU;QAAS;IACvG;IACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,6KAAA,CAAA,kBAAe,CAAC,QAAQ,EAAE;QAAE,OAAO;QAAS,UAAU;IAAS;AAC/E;AACA,SAAS;IACL,OAAO,IAAI;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7798, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs"], "sourcesContent": ["import { Children, isValidElement } from 'react';\n\nconst getChildKey = (child) => child.key || \"\";\nfunction onlyElements(children) {\n    const filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    Children.forEach(children, (child) => {\n        if (isValidElement(child))\n            filtered.push(child);\n    });\n    return filtered;\n}\n\nexport { getChildKey, onlyElements };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc,CAAC,QAAU,MAAM,GAAG,IAAI;AAC5C,SAAS,aAAa,QAAQ;IAC1B,MAAM,WAAW,EAAE;IACnB,0FAA0F;IAC1F,qMAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,UAAU,CAAC;QACxB,IAAI,CAAA,GAAA,qMAAA,CAAA,iBAAc,AAAD,EAAE,QACf,SAAS,IAAI,CAAC;IACtB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7820, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs"], "sourcesContent": ["\"use client\";\nimport { jsx, Fragment } from 'react/jsx-runtime';\nimport { useMemo, useRef, useState, useContext } from 'react';\nimport { LayoutGroupContext } from '../../context/LayoutGroupContext.mjs';\nimport { useConstant } from '../../utils/use-constant.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nimport { PresenceChild } from './PresenceChild.mjs';\nimport { usePresence } from './use-presence.mjs';\nimport { onlyElements, getChildKey } from './utils.mjs';\n\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */\nconst AnimatePresence = ({ children, custom, initial = true, onExitComplete, presenceAffectsLayout = true, mode = \"sync\", propagate = false, anchorX = \"left\", root }) => {\n    const [isParentPresent, safeToRemove] = usePresence(propagate);\n    /**\n     * Filter any children that aren't ReactElements. We can only track components\n     * between renders with a props.key.\n     */\n    const presentChildren = useMemo(() => onlyElements(children), [children]);\n    /**\n     * Track the keys of the currently rendered children. This is used to\n     * determine which children are exiting.\n     */\n    const presentKeys = propagate && !isParentPresent ? [] : presentChildren.map(getChildKey);\n    /**\n     * If `initial={false}` we only want to pass this to components in the first render.\n     */\n    const isInitialRender = useRef(true);\n    /**\n     * A ref containing the currently present children. When all exit animations\n     * are complete, we use this to re-render the component with the latest children\n     * *committed* rather than the latest children *rendered*.\n     */\n    const pendingPresentChildren = useRef(presentChildren);\n    /**\n     * Track which exiting children have finished animating out.\n     */\n    const exitComplete = useConstant(() => new Map());\n    /**\n     * Save children to render as React state. To ensure this component is concurrent-safe,\n     * we check for exiting children via an effect.\n     */\n    const [diffedChildren, setDiffedChildren] = useState(presentChildren);\n    const [renderedChildren, setRenderedChildren] = useState(presentChildren);\n    useIsomorphicLayoutEffect(() => {\n        isInitialRender.current = false;\n        pendingPresentChildren.current = presentChildren;\n        /**\n         * Update complete status of exiting children.\n         */\n        for (let i = 0; i < renderedChildren.length; i++) {\n            const key = getChildKey(renderedChildren[i]);\n            if (!presentKeys.includes(key)) {\n                if (exitComplete.get(key) !== true) {\n                    exitComplete.set(key, false);\n                }\n            }\n            else {\n                exitComplete.delete(key);\n            }\n        }\n    }, [renderedChildren, presentKeys.length, presentKeys.join(\"-\")]);\n    const exitingChildren = [];\n    if (presentChildren !== diffedChildren) {\n        let nextChildren = [...presentChildren];\n        /**\n         * Loop through all the currently rendered components and decide which\n         * are exiting.\n         */\n        for (let i = 0; i < renderedChildren.length; i++) {\n            const child = renderedChildren[i];\n            const key = getChildKey(child);\n            if (!presentKeys.includes(key)) {\n                nextChildren.splice(i, 0, child);\n                exitingChildren.push(child);\n            }\n        }\n        /**\n         * If we're in \"wait\" mode, and we have exiting children, we want to\n         * only render these until they've all exited.\n         */\n        if (mode === \"wait\" && exitingChildren.length) {\n            nextChildren = exitingChildren;\n        }\n        setRenderedChildren(onlyElements(nextChildren));\n        setDiffedChildren(presentChildren);\n        /**\n         * Early return to ensure once we've set state with the latest diffed\n         * children, we can immediately re-render.\n         */\n        return null;\n    }\n    if (process.env.NODE_ENV !== \"production\" &&\n        mode === \"wait\" &&\n        renderedChildren.length > 1) {\n        console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.`);\n    }\n    /**\n     * If we've been provided a forceRender function by the LayoutGroupContext,\n     * we can use it to force a re-render amongst all surrounding components once\n     * all components have finished animating out.\n     */\n    const { forceRender } = useContext(LayoutGroupContext);\n    return (jsx(Fragment, { children: renderedChildren.map((child) => {\n            const key = getChildKey(child);\n            const isPresent = propagate && !isParentPresent\n                ? false\n                : presentChildren === renderedChildren ||\n                    presentKeys.includes(key);\n            const onExit = () => {\n                if (exitComplete.has(key)) {\n                    exitComplete.set(key, true);\n                }\n                else {\n                    return;\n                }\n                let isEveryExitComplete = true;\n                exitComplete.forEach((isExitComplete) => {\n                    if (!isExitComplete)\n                        isEveryExitComplete = false;\n                });\n                if (isEveryExitComplete) {\n                    forceRender?.();\n                    setRenderedChildren(pendingPresentChildren.current);\n                    propagate && safeToRemove?.();\n                    onExitComplete && onExitComplete();\n                }\n            };\n            return (jsx(PresenceChild, { isPresent: isPresent, initial: !isInitialRender.current || initial\n                    ? undefined\n                    : false, custom: custom, presenceAffectsLayout: presenceAffectsLayout, mode: mode, root: root, onExitComplete: isPresent ? undefined : onExit, anchorX: anchorX, children: child }, key));\n        }) }));\n};\n\nexport { AnimatePresence };\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgCC,GACD,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,IAAI,EAAE,cAAc,EAAE,wBAAwB,IAAI,EAAE,OAAO,MAAM,EAAE,YAAY,KAAK,EAAE,UAAU,MAAM,EAAE,IAAI,EAAE;IACjK,MAAM,CAAC,iBAAiB,aAAa,GAAG,CAAA,GAAA,mMAAA,CAAA,cAAW,AAAD,EAAE;IACpD;;;KAGC,GACD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,WAAW;QAAC;KAAS;IACxE;;;KAGC,GACD,MAAM,cAAc,aAAa,CAAC,kBAAkB,EAAE,GAAG,gBAAgB,GAAG,CAAC,yLAAA,CAAA,cAAW;IACxF;;KAEC,GACD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B;;;;KAIC,GACD,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACtC;;KAEC,GACD,MAAM,eAAe,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE,IAAM,IAAI;IAC3C;;;KAGC,GACD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,CAAA,GAAA,uLAAA,CAAA,4BAAyB,AAAD,EAAE;QACtB,gBAAgB,OAAO,GAAG;QAC1B,uBAAuB,OAAO,GAAG;QACjC;;SAEC,GACD,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;YAC9C,MAAM,MAAM,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,CAAC,EAAE;YAC3C,IAAI,CAAC,YAAY,QAAQ,CAAC,MAAM;gBAC5B,IAAI,aAAa,GAAG,CAAC,SAAS,MAAM;oBAChC,aAAa,GAAG,CAAC,KAAK;gBAC1B;YACJ,OACK;gBACD,aAAa,MAAM,CAAC;YACxB;QACJ;IACJ,GAAG;QAAC;QAAkB,YAAY,MAAM;QAAE,YAAY,IAAI,CAAC;KAAK;IAChE,MAAM,kBAAkB,EAAE;IAC1B,IAAI,oBAAoB,gBAAgB;QACpC,IAAI,eAAe;eAAI;SAAgB;QACvC;;;SAGC,GACD,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;YAC9C,MAAM,QAAQ,gBAAgB,CAAC,EAAE;YACjC,MAAM,MAAM,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE;YACxB,IAAI,CAAC,YAAY,QAAQ,CAAC,MAAM;gBAC5B,aAAa,MAAM,CAAC,GAAG,GAAG;gBAC1B,gBAAgB,IAAI,CAAC;YACzB;QACJ;QACA;;;SAGC,GACD,IAAI,SAAS,UAAU,gBAAgB,MAAM,EAAE;YAC3C,eAAe;QACnB;QACA,oBAAoB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE;QACjC,kBAAkB;QAClB;;;SAGC,GACD,OAAO;IACX;IACA,IAAI,oDAAyB,gBACzB,SAAS,UACT,iBAAiB,MAAM,GAAG,GAAG;QAC7B,QAAQ,IAAI,CAAC,CAAC,6IAA6I,CAAC;IAChK;IACA;;;;KAIC,GACD,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,gLAAA,CAAA,qBAAkB;IACrD,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,uNAAA,CAAA,WAAQ,EAAE;QAAE,UAAU,iBAAiB,GAAG,CAAC,CAAC;YAChD,MAAM,MAAM,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE;YACxB,MAAM,YAAY,aAAa,CAAC,kBAC1B,QACA,oBAAoB,oBAClB,YAAY,QAAQ,CAAC;YAC7B,MAAM,SAAS;gBACX,IAAI,aAAa,GAAG,CAAC,MAAM;oBACvB,aAAa,GAAG,CAAC,KAAK;gBAC1B,OACK;oBACD;gBACJ;gBACA,IAAI,sBAAsB;gBAC1B,aAAa,OAAO,CAAC,CAAC;oBAClB,IAAI,CAAC,gBACD,sBAAsB;gBAC9B;gBACA,IAAI,qBAAqB;oBACrB;oBACA,oBAAoB,uBAAuB,OAAO;oBAClD,aAAa;oBACb,kBAAkB;gBACtB;YACJ;YACA,OAAQ,CAAA,GAAA,uNAAA,CAAA,MAAG,AAAD,EAAE,iMAAA,CAAA,gBAAa,EAAE;gBAAE,WAAW;gBAAW,SAAS,CAAC,gBAAgB,OAAO,IAAI,UAC9E,YACA;gBAAO,QAAQ;gBAAQ,uBAAuB;gBAAuB,MAAM;gBAAM,MAAM;gBAAM,gBAAgB,YAAY,YAAY;gBAAQ,SAAS;gBAAS,UAAU;YAAM,GAAG;QAChM;IAAG;AACX", "ignoreList": [0], "debugId": null}}]}