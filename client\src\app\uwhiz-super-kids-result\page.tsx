'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Crown } from 'lucide-react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Header from '@/app-components/Header';
import Footer from '@/app-components/Footer';
import cellularWord from '../../../public/cellular_world.jpeg';
import rbNews from '../../../public/rb-news.png';
import winnerPhoto from '../../../public/uwhiz-kids-winner.jpeg';

interface UwhizKidsResult {
    rank: number;
    firstName: string;
    lastName: string;
}

const staticData: UwhizKidsResult[] = [
    { rank: 1, firstName: 'Mantra', lastName: 'Upadhyay' },
    { rank: 2, firstName: 'Vihan', lastName: 'Khambhara' },
    { rank: 3, firstName: 'Ridham', lastName: '<PERSON><PERSON><PERSON><PERSON>' },
    { rank: 4, firstName: '<PERSON><PERSON>', lastName: '<PERSON><PERSON><PERSON>' },
    { rank: 5, firstName: 'Meet', lastName: '<PERSON><PERSON><PERSON>' },
    { rank: 6, firstName: 'Vyom', lastName: 'Kothadiya' },
    { rank: 7, firstName: 'Rachit', lastName: 'Nimavat' },
    { rank: 8, firstName: 'Dharvi', lastName: 'Kavar' },
    { rank: 9, firstName: 'Dhairya', lastName: 'Kavar' },
    { rank: 10, firstName: 'Hiyan', lastName: 'Sanandiya' },
    { rank: 11, firstName: 'Daksh', lastName: 'Gadara' },
    { rank: 12, firstName: 'Janki', lastName: 'Sanandiya' },
    { rank: 13, firstName: 'Harshil', lastName: 'Padsumbiya' },
    { rank: 14, firstName: 'Aarav', lastName: 'Amrutiya' },
    { rank: 15, firstName: 'Dhruvi', lastName: 'Detroja' },
    { rank: 16, firstName: 'Henil', lastName: 'Rokad' },
    { rank: 17, firstName: 'Riya', lastName: 'Bhatiya' },
    { rank: 18, firstName: 'Raghav', lastName: 'Padsumbiya' },
    { rank: 19, firstName: 'Darshi', lastName: 'Barasara' },
    { rank: 20, firstName: 'Jaimin', lastName: 'Virpariya' },
    { rank: 21, firstName: 'Sneh', lastName: 'Bhimani' },
    { rank: 22, firstName: 'Yash', lastName: 'Vora' },
    { rank: 23, firstName: 'Gopi', lastName: 'Vaghadiya' },
    { rank: 24, firstName: 'Nij', lastName: 'Kanani' },
    { rank: 25, firstName: 'Harshil', lastName: 'Parmar' },
    { rank: 26, firstName: 'Preksha', lastName: 'Kavar' },
    { rank: 27, firstName: 'Siddhraj', lastName: 'Tamaliya' },
    { rank: 28, firstName: 'Vincy', lastName: 'Patel' },
    { rank: 29, firstName: 'Swara', lastName: 'Loriya' },
    { rank: 30, firstName: 'Pallavi', lastName: 'Bhut' },
    { rank: 31, firstName: 'Yogesh', lastName: 'Patel' },
    { rank: 32, firstName: 'Khodal', lastName: 'Padsumbiya' },
    { rank: 33, firstName: 'Ansh Vinodbhai', lastName: 'Chavda' },
    { rank: 34, firstName: 'Heer', lastName: 'Padsumbiya' },
    { rank: 35, firstName: 'Hetvi Dhavalbhai', lastName: 'Godhani' },
    { rank: 36, firstName: 'Gyani', lastName: 'Padsumbiya' },
    { rank: 37, firstName: 'Aayush', lastName: 'Khambhala' },
    { rank: 38, firstName: 'Hardik', lastName: 'Dharodiya' },
    { rank: 39, firstName: 'Hemil', lastName: 'Gohel' },
    { rank: 40, firstName: 'Neej', lastName: 'Aghera' },
];

export default function LeaderboardPage() {
    const [visibleCount, setVisibleCount] = useState(10);

    const topOne = staticData.filter((user) => user.rank === 1);
    const remainingUsers = staticData.filter((user) => user.rank > 1);
    const visibleUsers = remainingUsers.slice(0, visibleCount);

    const renderProfile = (user: UwhizKidsResult, size: number = 96, isFirst: boolean = false) => {
        const profile = (
            <div
                style={{ width: size, height: size }}
                className="flex items-center justify-center rounded-full bg-white text-customOrange font-bold text-lg sm:text-xl md:text-2xl border-4 border-customOrange"
            >
                {user.firstName.charAt(0) + user.lastName.charAt(0)}
            </div>
        );

        return isFirst ? (
            <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
            >
                {profile}
            </motion.div>
        ) : (
            profile
        );
    };

    return (
        <>
            <Header />
            <div className="min-h-screen bg-white text-black font-sans py-4 sm:py-8 px-4 flex justify-center">
                <div className="w-full max-w-5xl space-y-6 sm:space-y-8 pt-8">
                    <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-center text-customOrange pb-10">
                        Result - Uwhiz Super Kids
                    </h1>

                    {/* Top Winner */}
                    <div className="flex flex-col sm:flex-row justify-center items-center gap-6 mt-10 bg-white p-4 sm:p-6 rounded-xl text-black shadow-lg">
                        {topOne.map((user) => (
                            <motion.div
                                key={user.rank}
                                initial={{ y: 50, opacity: 0 }}
                                animate={{ y: 0, opacity: 1 }}
                                transition={{ delay: 0.2 }}
                                className="flex flex-col items-center"
                            >
                                <div className="relative scale-110">
                                    <motion.div
                                        animate={{ scale: [1, 1.1, 1] }}
                                        transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                                        className="rounded-full border-4 p-1 border-customOrange"
                                    >
                                        <div className="rounded-full border-4 border-white p-1 bg-white">
                                            <Image
                                                src={winnerPhoto}
                                                alt="Winner"
                                                width={120}
                                                height={120}
                                                className="rounded-full object-cover shadow-xl w-24 h-24 sm:w-32 sm:h-32"
                                            />
                                        </div>
                                    </motion.div>

                                    <Crown className="absolute -top-10 sm:-top-16 left-1/2 -translate-x-1/2 text-customOrange w-8 sm:w-12 h-8 sm:h-12" />

                                    <div className="absolute -bottom-4 left-1/2 -translate-x-1/2 bg-orange-500 text-white font-bold rounded-full w-9 h-9 flex items-center justify-center border-4 border-orange-500">
                                        {user.rank}
                                    </div>
                                </div>

                                <p className="mt-6 text-lg sm:text-xl font-semibold text-center">
                                    {user.firstName} {user.lastName}
                                </p>
                                <p className="text-sm sm:text-lg text-center">
                                    Winning Price : ₹1,00,000
                                </p>
                            </motion.div>
                        ))}
                    </div>

                    {/* Leaderboard List */}
                    <div className="space-y-4">
                        {visibleUsers.map((user) => {
                            const isCellular = user.rank >= 2 && user.rank <= 11;
                            const isRb = user.rank >= 12 && user.rank <= 40;

                            return (
                                <div
                                    key={user.rank}
                                    className="flex flex-col sm:flex-row items-center justify-between p-4 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition"
                                >
                                    <div className="flex items-center gap-4">
                                        <div className="w-10 h-10 flex items-center justify-center rounded-full bg-orange-100 text-orange-500 font-bold">
                                            {user.rank}
                                        </div>
                                        {renderProfile(user, 48)}
                                        <p className="font-semibold text-lg text-black">
                                            {user.firstName} {user.lastName}
                                        </p>
                                    </div>

                                    {(isCellular || isRb) && (
                                        <div className="flex items-center gap-2 mt-5 sm:mt-0">
                                            <Image
                                                src={isCellular ? cellularWord : rbNews}
                                                alt={isCellular ? 'Cellular Word' : 'RB News'}
                                                width={36}
                                                height={36}
                                                className="object-contain"
                                            />
                                            <span
                                                className={`text-xs sm:text-sm font-medium px-3 py-1 rounded-full ${isCellular
                                                    ? 'bg-yellow-100 text-yellow-600'
                                                    : 'bg-blue-100 text-blue-700'
                                                    }`}
                                            >
                                                Gifted by {isCellular ? 'Cellular Word' : 'RB News'}
                                            </span>
                                        </div>
                                    )}
                                </div>
                            );
                        })}
                    </div>

                    {/* Load More */}
                    {visibleCount < remainingUsers.length && (
                        <div className="flex justify-center mt-8 mb-10">
                            <Button
                                onClick={() => setVisibleCount((prev) => prev + 10)}
                                className="px-6 py-2 rounded-full bg-customOrange text-white"
                            >
                                Load More
                            </Button>
                        </div>
                    )}
                </div>
            </div>
            <Footer />
        </>
    );
}